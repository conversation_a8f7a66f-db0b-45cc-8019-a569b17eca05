<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عكس النصوص - Text Reverser</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>عكس النصوص <span>Text Reverser</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="inputText" class="ar">أدخل النص هنا:</label>
            <label for="inputText" class="en">Enter your text here:</label>
            <textarea id="inputText" rows="5" placeholder="Type or paste your text..."></textarea>
        </div>

        <div class="button-group">
            <button id="reverseChars" class="ar">عكس الحروف</button>
            <button id="reverseChars" class="en">Reverse Characters</button>
            
            <button id="reverseWords" class="ar">عكس الكلمات</button>
            <button id="reverseWords" class="en">Reverse Words</button>
        </div>

        <div class="output-group">
            <label for="outputText" class="ar">النتيجة:</label>
            <label for="outputText" class="en">Result:</label>
            <textarea id="outputText" rows="5" readonly></textarea>
            <button id="copyBtn" class="ar">نسخ النتيجة</button>
            <button id="copyBtn" class="en">Copy Result</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            const outputText = document.getElementById('outputText');

            // Load saved input if exists
            inputText.value = window.utils.getSavedInput('textReverser');

            // Reverse characters
            document.getElementById('reverseChars').addEventListener('click', () => {
                const text = inputText.value;
                outputText.value = text.split('').reverse().join('');
                window.utils.saveInput('textReverser', text);
            });

            // Reverse words
            document.getElementById('reverseWords').addEventListener('click', () => {
                const text = inputText.value;
                // Split by spaces, reverse array, and join back with spaces
                outputText.value = text.split(/\s+/).reverse().join(' ');
                window.utils.saveInput('textReverser', text);
            });

            // Copy to clipboard
            document.getElementById('copyBtn').addEventListener('click', () => {
                if (outputText.value) {
                    window.utils.copyToClipboard(outputText.value);
                }
            });
        });
    </script>
</body>
</html>