<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة الفرق الزمني - Time Difference Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .time-form {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
        .time-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .time-group {
            background-color: var(--bg-color);
            padding: 15px;
            border-radius: var(--border-radius);
        }
        .time-group h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        .date-time-input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        .result-container {
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            display: none;
        }
        .difference-box {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .difference-item {
            background-color: var(--card-bg);
            padding: 15px;
            border-radius: var(--border-radius);
            text-align: center;
        }
        .difference-value {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin: 10px 0;
        }
        .difference-label {
            color: var(--text-color);
        }
        .quick-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .quick-option {
            padding: 10px;
            background-color: var(--bg-color);
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s;
        }
        .quick-option:hover {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <header>
        <h1>حاسبة الفرق الزمني <span>Time Difference Calculator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="time-form">
            <div class="time-inputs">
                <div class="time-group">
                    <h3 class="ar">الوقت الأول</h3>
                    <h3 class="en">First Time</h3>
                    <input type="datetime-local" id="startTime" class="date-time-input">
                </div>

                <div class="time-group">
                    <h3 class="ar">الوقت الثاني</h3>
                    <h3 class="en">Second Time</h3>
                    <input type="datetime-local" id="endTime" class="date-time-input">
                </div>
            </div>

            <div class="quick-options">
                <div class="quick-option" data-unit="1h">+1 ساعة / Hour</div>
                <div class="quick-option" data-unit="24h">+24 ساعة / Hours</div>
                <div class="quick-option" data-unit="7d">+7 أيام / Days</div>
                <div class="quick-option" data-unit="30d">+30 يوم / Days</div>
                <div class="quick-option" data-unit="1y">+1 سنة / Year</div>
            </div>

            <div class="button-group">
                <button id="calculateBtn" class="ar">احسب الفرق</button>
                <button id="calculateBtn" class="en">Calculate Difference</button>
            </div>
        </div>

        <div class="result-container">
            <div class="difference-box">
                <div class="difference-item">
                    <div class="difference-value" id="yearsValue">0</div>
                    <div class="difference-label ar">سنوات</div>
                    <div class="difference-label en">Years</div>
                </div>
                <div class="difference-item">
                    <div class="difference-value" id="monthsValue">0</div>
                    <div class="difference-label ar">شهور</div>
                    <div class="difference-label en">Months</div>
                </div>
                <div class="difference-item">
                    <div class="difference-value" id="daysValue">0</div>
                    <div class="difference-label ar">أيام</div>
                    <div class="difference-label en">Days</div>
                </div>
                <div class="difference-item">
                    <div class="difference-value" id="hoursValue">0</div>
                    <div class="difference-label ar">ساعات</div>
                    <div class="difference-label en">Hours</div>
                </div>
                <div class="difference-item">
                    <div class="difference-value" id="minutesValue">0</div>
                    <div class="difference-label ar">دقائق</div>
                    <div class="difference-label en">Minutes</div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const startTime = document.getElementById('startTime');
            const endTime = document.getElementById('endTime');
            const calculateBtn = document.getElementById('calculateBtn');
            const resultContainer = document.querySelector('.result-container');
            const quickOptions = document.querySelectorAll('.quick-option');

            // Set default start time to now
            const now = new Date();
            startTime.value = now.toISOString().slice(0, 16);

            // Load saved values
            const savedStart = localStorage.getItem('timeDiff_start');
            const savedEnd = localStorage.getItem('timeDiff_end');
            if (savedStart) startTime.value = savedStart;
            if (savedEnd) endTime.value = savedEnd;

            function calculateDifference() {
                const start = new Date(startTime.value);
                const end = new Date(endTime.value);
                
                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                    alert('Please enter valid dates and times');
                    return;
                }

                const diff = Math.abs(end - start);
                const diffDate = new Date(diff);

                // Calculate individual units
                const years = Math.floor(diff / (365.25 * 24 * 60 * 60 * 1000));
                const months = Math.floor((diff % (365.25 * 24 * 60 * 60 * 1000)) / (30.44 * 24 * 60 * 60 * 1000));
                const days = Math.floor((diff % (30.44 * 24 * 60 * 60 * 1000)) / (24 * 60 * 60 * 1000));
                const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
                const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));

                // Update display
                document.getElementById('yearsValue').textContent = years;
                document.getElementById('monthsValue').textContent = months;
                document.getElementById('daysValue').textContent = days;
                document.getElementById('hoursValue').textContent = hours;
                document.getElementById('minutesValue').textContent = minutes;

                resultContainer.style.display = 'block';

                // Save values
                localStorage.setItem('timeDiff_start', startTime.value);
                localStorage.setItem('timeDiff_end', endTime.value);
            }

            // Quick options
            quickOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const unit = option.dataset.unit;
                    const start = new Date(startTime.value);
                    let end;

                    switch(unit) {
                        case '1h':
                            end = new Date(start.getTime() + 60 * 60 * 1000);
                            break;
                        case '24h':
                            end = new Date(start.getTime() + 24 * 60 * 60 * 1000);
                            break;
                        case '7d':
                            end = new Date(start.getTime() + 7 * 24 * 60 * 60 * 1000);
                            break;
                        case '30d':
                            end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000);
                            break;
                        case '1y':
                            end = new Date(start.getTime() + 365 * 24 * 60 * 60 * 1000);
                            break;
                    }

                    endTime.value = end.toISOString().slice(0, 16);
                    calculateDifference();
                });
            });

            // Calculate on button click
            calculateBtn.addEventListener('click', calculateDifference);

            // Calculate when either input changes
            startTime.addEventListener('change', calculateDifference);
            endTime.addEventListener('change', calculateDifference);
        });
    </script>
</body>
</html>