<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة الرموز والأيقونات - Emoji & Icons Library</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .library {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .search-section {
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 1.1em;
        }
        .categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .category-tag {
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            background-color: var(--secondary-bg);
            transition: all 0.3s;
        }
        .category-tag.active {
            background-color: var(--primary-color);
            color: white;
        }
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .item-card {
            background-color: var(--secondary-bg);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
            position: relative;
        }
        .item-card:hover {
            transform: scale(1.1);
        }
        .item-content {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .item-code {
            font-size: 0.8em;
            color: var(--text-color);
            opacity: 0.8;
            word-break: break-all;
        }
        .copy-tooltip {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8em;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <header>
        <h1>مكتبة الرموز والأيقونات <span>Emoji & Icons Library</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="library">
            <div class="search-section">
                <input type="text" class="search-input" id="searchInput" 
                    placeholder="ابحث عن رموز وأيقونات - Search emojis and icons">
            </div>

            <div class="tabs">
                <div class="tab active" onclick="switchTab('emoji')">
                    <i class="far fa-smile"></i> إيموجي - Emoji
                </div>
                <div class="tab" onclick="switchTab('fontawesome')">
                    <i class="fab fa-font-awesome"></i> أيقونات - Icons
                </div>
            </div>

            <div class="categories" id="categories"></div>

            <div class="items-grid" id="itemsGrid"></div>
        </div>
    </main>

    <script>
        const emojis = {
            'وجوه - Smileys': ['😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '😉', '😍', '🥰'],
            'حيوانات - Animals': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮'],
            'طعام - Food': ['🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭'],
            'رياضة - Sports': ['⚽', '🏀', '🏈', '⚾', '🎾', '🏐', '🏉', '🎱', '🏓', '🏸', '🏒', '🏑'],
            'سفر - Travel': ['✈️', '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻'],
            'قلوب - Hearts': ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💖', '💗', '💓']
        };

        const fontAwesomeIcons = {
            'أساسية - Basic': ['home', 'user', 'cog', 'star', 'heart', 'circle', 'square', 'check', 'times', 'search'],
            'تواصل - Social': ['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'github', 'whatsapp'],
            'تحرير - Editor': ['bold', 'italic', 'underline', 'strikethrough', 'list', 'align-left', 'align-center'],
            'ملفات - Files': ['file', 'folder', 'image', 'video', 'music', 'pdf', 'word', 'excel', 'powerpoint'],
            'أعمال - Business': ['briefcase', 'chart-bar', 'chart-line', 'chart-pie', 'calculator', 'phone', 'envelope']
        };

        let currentTab = 'emoji';
        let currentCategory = Object.keys(emojis)[0];

        function switchTab(tab) {
            currentTab = tab;
            document.querySelectorAll('.tab').forEach(t => {
                t.classList.toggle('active', t.textContent.includes(tab === 'emoji' ? 'Emoji' : 'Icons'));
            });
            currentCategory = Object.keys(tab === 'emoji' ? emojis : fontAwesomeIcons)[0];
            updateCategories();
            updateGrid();
        }

        function updateCategories() {
            const categories = currentTab === 'emoji' ? emojis : fontAwesomeIcons;
            const container = document.getElementById('categories');
            container.innerHTML = Object.keys(categories).map(category => `
                <div class="category-tag ${category === currentCategory ? 'active' : ''}"
                    onclick="selectCategory('${category}')">
                    ${category}
                </div>
            `).join('');
        }

        function selectCategory(category) {
            currentCategory = category;
            updateCategories();
            updateGrid();
        }

        function updateGrid() {
            const grid = document.getElementById('itemsGrid');
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const items = currentTab === 'emoji' ? emojis[currentCategory] : fontAwesomeIcons[currentCategory];
            
            grid.innerHTML = items.map(item => {
                if (currentTab === 'emoji') {
                    return `
                        <div class="item-card" onclick="copyItem('${item}')">
                            <div class="copy-tooltip">تم النسخ! - Copied!</div>
                            <div class="item-content">${item}</div>
                            <div class="item-code">${item}</div>
                        </div>
                    `;
                } else {
                    return `
                        <div class="item-card" onclick="copyItem('<i class=\\'fas fa-${item}\\'></i>')">
                            <div class="copy-tooltip">تم النسخ! - Copied!</div>
                            <div class="item-content">
                                <i class="fas fa-${item}"></i>
                            </div>
                            <div class="item-code">fa-${item}</div>
                        </div>
                    `;
                }
            }).join('');
        }

        function copyItem(text) {
            navigator.clipboard.writeText(text).then(() => {
                const tooltip = event.currentTarget.querySelector('.copy-tooltip');
                tooltip.style.opacity = '1';
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                }, 1000);
            });
        }

        document.getElementById('searchInput').addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            // Implement search functionality here
            updateGrid();
        });

        // Initialize
        updateCategories();
        updateGrid();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>