<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="أهمية الذكاء الاصطناعي في التكنولوجيا الحديثة">
    <meta name="description" content="اكتشف كيف يؤثر الذكاء الاصطناعي على التكنولوجيا الحديثة، ويعزز الابتكار، ويحل المشكلات عبر الصناعات المختلفة بطرق غير مسبوقة.">
    <meta name="keywords" content="ذكاء اصطناعي, تكنولوجيا حديثة, ابتكار, تقدم تكنولوجي, أتمتة, تحسين محركات البحث, تعلم آلي, تحليل بيانات">
    <meta name="robots" content="index, follow">
    <title>أهمية الذكاء الاصطناعي في التكنولوجيا الحديثة</title>

    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .container { background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .field { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; position: relative; }
        .field[contenteditable="true"] { background: #f9f9f9; border-color: #007bff; }
        .sub-field { margin: 10px 0; padding: 10px; border: 1px dashed #ccc; border-radius: 3px; }
        .sub-field::before { content: attr(data-label); display: block; font-size: 0.9em; color: #666; margin-bottom: 5px; }
        h1 { font-size: 2.5em; color: #222; margin-bottom: 20px; }
        h2 { font-size: 1.8em; color: #444; margin: 20px 0 15px; }
        h3 { font-size: 1.5em; color: #555; margin: 15px 0 10px; }
        h4 { font-size: 1.3em; color: #666; margin: 10px 0 10px; }
        p { font-size: 1.1em; margin-bottom: 15px; min-height: 4.8em; }
        ul { margin: 10px 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table, th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background: #f8f9fa; }
        img { max-width: 100%; height: auto; margin: 10px 0; }
        .controls { display: flex; gap: 10px; flex-wrap: wrap; justify-content: flex-end; margin: 20px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #saveChanges { background: #28a745; display: none; }
        #saveChanges:hover { background: #218838; }
        #seoStats { padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em; margin-top: 20px; }
        #seoStats .warning { color: #dc3545; }
        #seoStats .success { color: #28a745; }
        .generator { margin-bottom: 20px; display: flex; gap: 10px; align-items: center; }
        #articleTitle { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 5px; }
        #generateButton { background: #17a2b8; }
        #generateButton:hover { background: #138496; }
        #modelSelect { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .loading { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 1em; color: #007bff; }
        @media (max-width: 768px) {
            h1 { font-size: 2em; } h2 { font-size: 1.5em; } h3 { font-size: 1.3em; }
            .container { padding: 15px; } button { padding: 8px 15px; }
            #articleTitle { width: 200px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- قسم إدخال العنوان وتوليد المقال -->
        <div class="generator">
            <input type="text" id="articleTitle" placeholder="أدخل عنوان المقال الجديد">
            <select id="modelSelect">
                <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo (OpenAI)</option>
                <option value="anthropic/claude-3-haiku">Claude 3 Haiku (Anthropic)</option>
                <option value="meta-llama/llama-3.1-8b">LLaMA 3.1 8B (Meta)</option>
                <option value="mistral/mixtral-8x7b">Mixtral 8x7B (Mistral)</option>
            </select>
            <button id="generateButton">توليد</button>
        </div>

 <!-- المقدمة -->
        <div class="field" id="intro">
            <div class="sub-field" data-label="العنوان الرئيسي"><h1>أهمية الذكاء الاصطناعي في التكنولوجيا الحديثة</h1></div>
            <div class="sub-field" data-label="الصورة الرئيسية"><img src="https://via.placeholder.com/800x400" alt="الذكاء الاصطناعي في التكنولوجيا الحديثة"></div>
            <div class="sub-field" data-label="الفقرة الأولى"><p>في عالم يتسارع فيه التقدم التكنولوجي، يبرز <strong>الذكاء الاصطناعي</strong> كأحد أهم الابتكارات التي تعيد تشكيل حياتنا اليومية. من خلال قدرته على تحليل البيانات الضخمة واتخاذ القرارات الذكية، أصبح الذكاء الاصطناعي أداة لا غنى عنها في مختلف القطاعات.</p></div>
            <div class="sub-field" data-label="الفقرة الثانية"><p>يهدف هذا المقال إلى تقديم نظرة شاملة على تأثير <strong>الذكاء الاصطناعي</strong> في التكنولوجيا الحديثة، مع التركيز على تطبيقاته العملية في مجالات مثل الرعاية الصحية، النقل، التعليم، والأعمال. سنستعرض كيف يساهم في تعزيز الكفاءة وحل المشكلات المعقدة.</p></div>
            <div class="sub-field" data-label="الفقرة الثالثة"><p>مع محتوى يتجاوز 2000 كلمة، ستجد تحليلاً متعمقًا مدعومًا بقوائم نقطية، جداول، صور توضيحية، وأسئلة شائعة، مما يجعل هذه المقالة مرجعًا شاملاً ومتوافقًا مع معايير <strong>تحسين محركات البحث</strong>.</p></div>
        </div>

        <!-- العنوان 1 -->
        <div class="field">
            <div class="sub-field" data-label="العنوان"><h2>دور الذكاء الاصطناعي في تحسين الرعاية الصحية</h2></div>
            <div class="sub-field" data-label="الفقرة الأولى"><p>يُحدث <strong>الذكاء الاصطناعي</strong> ثورة في مجال الرعاية الصحية من خلال تحسين دقة التشخيص. تُستخدم تقنيات التعلم الآلي لتحليل الصور الطبية مثل الأشعة السينية والتصوير بالرنين المغناطيسي، مما يساعد الأطباء على اكتشاف الأمراض مبكرًا.</p></div>
            <div class="sub-field" data-label="الفقرة الثانية"><p>كما يوفر الذكاء الاصطناعي أنظمة دعم قرارات تعتمد على تحليل بيانات المرضى في الوقت الفعلي. هذا يقلل من الأخطاء الطبية ويحسن جودة العلاج، مما ينعكس إيجابيًا على حياة المرضى.</p></div>
            <div class="sub-field" data-label="الفقرة الثالثة"><p>علاوة على ذلك، يُستخدم في تطوير الأدوية الجديدة من خلال محاكاة التفاعلات الكيميائية. هذا يقلل من الوقت اللازم لتجارب الأدوية ويسرع وصولها إلى الأسواق، مما يوفر حياة الملايين.</p></div>
        </div>

        <!-- العنوان 2 (مع قائمة نقطية) -->
        <div class="field">
            <div class="sub-field" data-label="العنوان"><h2>تأثير الذكاء الاصطناعي على النقل الذكي</h2></div>
            <div class="sub-field" data-label="الفقرة الأولى"><p>في قطاع النقل، يُعتبر <strong>الذكاء الاصطناعي</strong> العامل الأساسي وراء تطوير السيارات ذاتية القيادة. تعتمد هذه التقنية على معالجة البيانات من الكاميرات وأجهزة الاستشعار لضمان التنقل الآمن.</p></div>
            <div class="sub-field" data-label="الفقرة الثانية"><p>تشمل فوائد الذكاء الاصطناعي في النقل ما يلي:</p><ul><li>تقليل الحوادث المرورية بفضل الاستجابة الفورية للمخاطر.</li><li>تحسين كفاءة استهلاك الوقود من خلال اختيار المسارات المثلى.</li><li>تقليص الازدحام عبر تحليل أنماط الحركة المرورية.</li></ul></div>
            <div class="sub-field" data-label="الفقرة الثالثة"><p>مع استمرار التطور، سيصبح النقل الذكي جزءًا أساسيًا من البنية التحتية الحديثة، مما يعزز الاستدامة ويقلل التكاليف على المدى الطويل.</p></div>
        </div>

        <!-- العنوان 3 (مع جدول) -->
        <div class="field">
            <div class="sub-field" data-label="العنوان"><h2>الذكاء الاصطناعي في تطوير التعليم</h2></div>
            <div class="sub-field" data-label="الفقرة الأولى"><p>يُستخدم <strong>الذكاء الاصطناعي</strong> لتخصيص تجارب التعلم بناءً على احتياجات كل طالب. تعتمد المنصات التعليمية على تحليل أداء الطلاب لتقديم محتوى يتناسب مع مستواهم.</p></div>
            <div class="sub-field" data-label="الفقرة الثانية"><p>يوضح الجدول التالي تطبيقات الذكاء الاصطناعي في التعليم:</p></div>
            <div class="sub-field" data-label="جدول مناسب"><table><tr><th>التطبيق</th><th>الفائدة</th></tr><tr><td>التعلم التكيفي</td><td>تخصيص الدروس حسب مستوى الطالب</td></tr><tr><td>التصحيح الآلي</td><td>تقليل الجهد على المعلمين</td></tr><tr><td>المساعدون الافتراضيون</td><td>دعم الطلاب على مدار الساعة</td></tr></table></div>
            <div class="sub-field" data-label="الفقرة الثالثة"><p>هذه التطبيقات تجعل التعليم أكثر فعالية وشمولية، مما يساعد في سد الفجوات التعليمية وتحسين النتائج الأكاديمية عالميًا.</p></div>
        </div>

        <!-- (بقية الأقسام محذوفة للاختصار، لكنها موجودة في الكود الكامل كما في الأصل) -->

        <!-- الأسئلة الشائعة -->
        <div class="field" id="faq">
            <div class="sub-field" data-label="العنوان"><h2>الأسئلة الشائعة حول الذكاء الاصطناعي</h2></div>
            <div class="sub-field" data-label="سؤال 1"><h3>ما هو الذكاء الاصطناعي؟</h3></div>
            <div class="sub-field" data-label="إجابة 1"><p>الذكاء الاصطناعي هو فرع من علوم الحاسوب يهدف إلى محاكاة القدرات البشرية مثل التفكير وحل المشكلات. يعتمد على تقنيات مثل التعلم الآلي ومعالجة اللغة الطبيعية.</p></div>
            <div class="sub-field" data-label="سؤال 2"><h3>هل يمكن للذكاء الاصطناعي أن يحل محل البشر؟</h3></div>
            <div class="sub-field" data-label="إجابة 2"><p>على الرغم من تقدمه، يُعتبر <strong>الذكاء الاصطناعي</strong> أداة مساعدة وليس بديلاً كاملاً للإبداع البشري. يعمل على تعزيز القدرات البشرية وليس استبدالها.</p></div>
            <div class="sub-field" data-label="سؤال 3"><h3>ما هي التحديات الأخلاقية للذكاء الاصطناعي؟</h3></div>
            <div class="sub-field" data-label="إجابة 3"><p>تشمل التحديات قضايا الخصوصية، التحيز في البيانات، وتأثيره على سوق العمل. يتطلب ذلك تطويرًا مسؤولاً لضمان تحقيق فوائده دون ضرر.</p></div>
            <div class="sub-field" data-label="سؤال فرعي"><h4>هل الذكاء الاصطناعي آمن؟</h4></div>
            <div class="sub-field" data-label="إجابة فرعية"><p>يمكن أن يكون آمنًا إذا تم استخدامه بشكل صحيح مع وضع ضوابط أخلاقية. التحدي يكمن في منع سوء الاستخدام من قبل جهات غير مسؤولة.</p></div>
        </div>

        <!-- الخاتمة -->
        <div class="field" id="conclusion">
            <div class="sub-field" data-label="العنوان"><h2>الخاتمة</h2></div>
            <div class="sub-field" data-label="الفقرة الأولى"><p>في الختام، يُعد <strong>الذكاء الاصطناعي</strong> أحد أعظم الابتكارات في عصرنا الحديث، حيث يعيد تشكيل التكنولوجيا والمجتمع بطرق لم نكن نتخيلها من قبل. من خلال تعزيز الكفاءة وحل المشكلات المعقدة، يفتح أبوابًا جديدة للتقدم عبر الصناعات.</p></div>
            <div class="sub-field" data-label="الفقرة الثانية"><p>مع ذلك، يتطلب هذا التطور التزامًا بالمسؤولية الأخلاقية لضمان أن تكون فوائده شاملة ومستدامة. الذكاء الاصطناعي ليس مجرد أداة، بل شريك في بناء مستقبل أكثر ذكاءً وازدهارًا للبشرية.</p></div>
            <div class="sub-field" data-label="الفقرة الثالثة"><p>مع استمرار البحث والابتكار، سيظل الذكاء الاصطناعي القوة الدافعة وراء ثورة تكنولوجية مستمرة، مما يجعله ركيزة أساسية في عالم الغد.</p></div>
        </div>

        <div class="controls">
            <button id="editToggle">تحرير المقال</button>
            <button id="downloadHtml">تنزيل كـ HTML</button>
            <button id="copyHtml">نسخ HTML</button>
            <button id="exportMarkdown">تصدير كـ Markdown</button>
            <button id="saveChanges">حفظ التعديلات</button>
        </div>

        <div id="seoStats"></div>
    </div>
<!-- (HTML كما هو موجود بالكود الأصلي، سأركز على الجزء البرمجي فقط) -->

<script>
    let isEditing = false;
    const fields = document.querySelectorAll('.field');
    const saveButton = document.getElementById('saveChanges');

    // مفاتيح API للنماذج المختلفة (يجب استبدالها بمفاتيح حقيقية بعد التسجيل)
    const apiKeys = {
        openRouter: '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',
        huggingFace: 'YOUR_HUGGING_FACE_API_KEY', // استبدل بمفتاحك من Hugging Face
        arabyAI: 'YOUR_ARABY_AI_API_KEY' // استبدل بمفتاحك من Araby AI
    };

    document.getElementById('editToggle').addEventListener('click', toggleEditMode);
    saveButton.addEventListener('click', () => isEditing && alert('تم حفظ التعديلات بنجاح!'));
    document.getElementById('downloadHtml').addEventListener('click', downloadHtml);
    document.getElementById('copyHtml').addEventListener('click', copyHtml);
    document.getElementById('exportMarkdown').addEventListener('click', exportMarkdown);
    document.getElementById('generateButton').addEventListener('click', generateArticle);

    function toggleEditMode() {
        isEditing = !isEditing;
        fields.forEach(field => field.contentEditable = isEditing);
        saveButton.style.display = isEditing ? 'inline-block' : 'none';
        document.getElementById('editToggle').textContent = isEditing ? 'عرض فقط' : 'تحرير المقال';
        updateSeoStats();
    }

    async function generateArticle() {
        const title = document.getElementById('articleTitle').value.trim();
        const model = document.getElementById('modelSelect').value;
        if (!title) {
            alert('يرجى إدخال عنوان للمقال!');
            return;
        }

        // قائمة النماذج للمحاولة (الأولوية للنماذج المجانية)
        const modelProviders = [
            { name: 'huggingFace', url: 'https://api-inference.huggingface.co/models/akhooli/gpt2-arabic', apiKey: apiKeys.huggingFace },
            { name: 'arabyAI', url: 'https://api.araby.ai/v1/generate', apiKey: apiKeys.arabyAI },
            { name: 'openRouter-openchat-3.5', url: 'https://openrouter.ai/api/v1/chat/completions', apiKey: apiKeys.openRouter, model: 'openchat/openchat-3.5:free' },
            { name: 'openRouter-gemma', url: 'https://openrouter.ai/api/v1/chat/completions', apiKey: apiKeys.openRouter, model: 'google/gemma-2b:free' },
            { name: 'openRouter-llama-2-7b', url: 'https://openrouter.ai/api/v1/chat/completions', apiKey: apiKeys.openRouter, model: 'meta-llama/llama-2-7b-chat:free' },
            { name: 'openRouter', url: 'https://openrouter.ai/api/v1/chat/completions', apiKey: apiKeys.openRouter, model: model }
        ];

        let previousContent = [];

        for (const field of fields) {
            const subFields = field.querySelectorAll('.sub-field');
            const sectionTitle = field.querySelector('h1, h2')?.textContent || 'قسم عام';

            for (const subField of subFields) {
                subField.innerHTML = '<div class="loading">جاري التحميل...</div>';
                const label = subField.getAttribute('data-label');
                const existingTag = subField.querySelector('h1, h2, h3, h4, p, img, table, ul')?.tagName.toLowerCase() || 'p';
                let prompt;

                const context = previousContent.length > 0 ? `المحتوى السابق في القسم: ${previousContent.slice(-3).join(' ')}` : '';

                // موجهات مخصصة لكل حقل
                switch (label) {
                    case 'العنوان الرئيسي':
                        prompt = `قم بكتابة عنوان رئيسي (H1) باللغة العربية لمقال بعنوان "${title}". اجعل العنوان موجزًا (5-10 كلمات)، جذابًا، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <h1>، مثال: <h1>عنوان رئيسي جديد</h1>. لا تضف نصًا خارج الوسم.`;
                        break;
                    case 'الصورة الرئيسية':
                        prompt = `قم بكتابة وصف بديل (alt text) باللغة العربية لصورة رئيسية لمقال بعنوان "${title}". اجعل الوصف موجزًا (5-10 كلمات) ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <img>، مثال: <img src="https://via.placeholder.com/800x400" alt="وصف الصورة">. لا تضف نصًا خارج الوسم.`;
                        break;
                    case 'الفقرة الأولى':
                        prompt = `قم بكتابة الفقرة الأولى باللغة العربية لمقدمة مقال بعنوان "${title}" متعلق بـ "${sectionTitle}". ${context} اجعل النص مترابطًا (70-100 كلمة)، يقدم لمحة عامة عن الموضوع، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <p>، مثال: <p>هذه فقرة جديدة...</p>. لا تضف نصًا خارج الوسم وتأكد من اكتمال الجمل.`;
                        break;
                    case 'الفقرة الثانية':
                        prompt = `قم بكتابة الفقرة الثانية باللغة العربية لمقدمة مقال بعنوان "${title}" متعلق بـ "${sectionTitle}". ${context} اجعل النص مترابطًا (70-100 كلمة)، يحدد الهدف من المقال، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <p>، مثال: <p>هذه فقرة جديدة...</p>. لا تضف نصًا خارج الوسم وتأكد من اكتمال الجمل.`;
                        break;
                    case 'الفقرة الثالثة':
                        prompt = `قم بكتابة الفقرة الثالثة باللغة العربية لمقدمة مقال بعنوان "${title}" متعلق بـ "${sectionTitle}". ${context} اجعل النص مترابطًا (70-100 كلمة)، يصف محتوى المقال (مثل الجداول، القوائم، الأسئلة الشائعة)، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <p>، مثال: <p>هذه فقرة جديدة...</p>. لا تضف نصًا خارج الوسم وتأكد من اكتمال الجمل.`;
                        break;
                    case 'العنوان':
                        prompt = `قم بكتابة عنوان فرعي (H2) باللغة العربية لقسم جديد ضمن مقال بعنوان "${title}" متعلق بـ "${sectionTitle}". ${context} اجعل العنوان موجزًا (5-10 كلمات)، ذا صلة، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <h2>، مثال: <h2>عنوان فرعي جديد</h2>. لا تضف نصًا خارج الوسم.`;
                        break;
                    case 'جدول مناسب':
                        prompt = `قم بكتابة جدول بتنسيق HTML باللغة العربية لقسم متعلق بـ "${sectionTitle}" ضمن مقال بعنوان "${title}". ${context} يحتوي الجدول على عمودين (مثل "العنصر" و"الوصف") و3 صفوف على الأقل، مع بيانات مترابطة ومفيدة. أرجع النص داخل وسم <table>، مثال: <table><tr><th>العنصر</th><th>الوصف</th></tr><tr><td>نقطة</td><td>وصف</td></tr>...</table>. لا تضف نصًا خارج الوسم.`;
                        break;
                    case 'سؤال 1':
                    case 'سؤال 2':
                    case 'سؤال 3':
                        prompt = `قم بكتابة سؤال شائع (H3) باللغة العربية لقسم الأسئلة الشائعة ضمن مقال بعنوان "${title}". ${context} اجعل السؤال موجزًا (5-10 كلمات) ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <h3>، مثال: <h3>ما هو الذكاء الاصطناعي؟</h3>. لا تضف نصًا خارج الوسم.`;
                        break;
                    case 'إجابة 1':
                    case 'إجابة 2':
                    case 'إجابة 3':
                        prompt = `قم بكتابة إجابة لسؤال شائع باللغة العربية لقسم الأسئلة الشائعة ضمن مقال بعنوان "${title}". ${context} اجعل النص مترابطًا (70-100 كلمة)، مفيدًا، ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <p>، مثال: <p>هذه إجابة جديدة...</p>. لا تضف نصًا خارج الوسم وتأكد من اكتمال الجمل.`;
                        break;
                    default:
                        prompt = `قم بكتابة نص باللغة العربية لـ "${label}" ضمن مقال بعنوان "${title}" متعلق بـ "${sectionTitle}". ${context} اجعل النص مفيدًا (70-100 كلمة) ومتوافقًا مع تحسين محركات البحث. أرجع النص داخل وسم <${existingTag === 'h1' || existingTag === 'h2' || existingTag === 'h3' || existingTag === 'h4' ? existingTag : 'p'}>، مثال: <p>نص جديد...</p>. لا تضف نصًا خارج الوسم وتأكد من اكتمال الجمل.`;
                }

                let content = null;
                let successfulProvider = null;

                // محاولة استخدام النماذج بالترتيب
                for (const provider of modelProviders) {
                    try {
                        let response;
                        if (provider.name === 'huggingFace') {
                            response = await fetch(provider.url, {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${provider.apiKey}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    inputs: prompt,
                                    parameters: { max_length: existingTag === 'table' || existingTag === 'ul' ? 500 : existingTag === 'p' ? 300 : 150 }
                                })
                            });
                        } else if (provider.name === 'arabyAI') {
                            response = await fetch(provider.url, {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${provider.apiKey}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    prompt: prompt,
                                    max_tokens: existingTag === 'table' || existingTag === 'ul' ? 500 : existingTag === 'p' ? 300 : 150,
                                    temperature: 0.5
                                })
                            });
                        } else if (provider.name.startsWith('openRouter')) {
                            response = await fetch(provider.url, {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${provider.apiKey}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    model: provider.model,
                                    messages: [{ role: 'user', content: prompt }],
                                    max_tokens: existingTag === 'table' || existingTag === 'ul' ? 500 : existingTag === 'p' ? 300 : 150,
                                    temperature: 0.5
                                })
                            });
                        }

                        if (!response.ok) {
                            throw new Error(`فشل الطلب مع ${provider.name}: ${response.status} ${response.statusText}`);
                        }

                        const data = await response.json();

                        if (provider.name === 'huggingFace') {
                            content = data[0]?.generated_text || null;
                        } else if (provider.name === 'arabyAI') {
                            content = data?.text || data?.result || null;
                        } else if (provider.name.startsWith('openRouter')) {
                            content = data.choices?.[0]?.message?.content || null;
                        }

                        if (content) {
                            successfulProvider = provider.name;
                            break; // إذا نجح الطلب، توقف عن المحاولة
                        }
                    } catch (error) {
                        console.error(`خطأ مع ${provider.name}:`, error);
                        continue; // جرب النموذج التالي
                    }
                }

                if (!content) {
                    console.error('فشل جميع النماذج، يتم استخدام محتوى افتراضي.');
                    // محتوى افتراضي في حالة الفشل
                    switch (existingTag) {
                        case 'h1':
                        case 'h2':
                        case 'h3':
                        case 'h4':
                            content = `<${existingTag}>عنوان افتراضي بسبب خطأ API مع ${successfulProvider || 'جميع النماذج'}</${existingTag}>`;
                            break;
                        case 'p':
                            content = `<p>فقرة افتراضية بسبب خطأ في API مع ${successfulProvider || 'جميع النماذج'}. يرجى التحقق من المفاتيح أو الاتصال.</p>`;
                            break;
                        case 'table':
                            content = `<table><tr><th>العنصر</th><th>الوصف</th></tr><tr><td>نقطة 1</td><td>وصف افتراضي</td></tr><tr><td>نقطة 2</td><td>وصف افتراضي</td></tr><tr><td>نقطة 3</td><td>وصف افتراضي</td></tr></table>`;
                            break;
                        case 'ul':
                            content = `<ul><li>نقطة افتراضية 1</li><li>نقطة افتراضية 2</li><li>نقطة افتراضية 3</li></ul>`;
                            break;
                        case 'img':
                            content = `<img src="https://via.placeholder.com/800x400" alt="وصف افتراضي بسبب خطأ API مع ${successfulProvider || 'جميع النماذج'}">`;
                            break;
                        default:
                            content = `<p>نص افتراضي بسبب خطأ في API مع ${successfulProvider || 'جميع النماذج'}.</p>`;
                    }
                } else {
                    // معالجة الاستجابة إذا نجحت
                    switch (existingTag) {
                        case 'h1':
                        case 'h2':
                        case 'h3':
                        case 'h4':
                            if (!content.includes(`<${existingTag}>`)) {
                                content = content.replace(/<\/?[^>]+(>|$)/g, '').trim();
                                content = content.length > 0 ? content : 'عنوان افتراضي';
                                content = `<${existingTag}>${content}</${existingTag}>`;
                            }
                            break;
                        case 'p':
                            if (!content.includes('<p>')) {
                                content = content.replace(/<\/?[^>]+(>|$)/g, '').trim();
                                if (!content.endsWith('.')) {
                                    content += '...';
                                }
                                content = content.length > 0 ? content : 'فقرة افتراضية للحقل.';
                                content = `<p>${content}</p>`;
                            }
                            break;
                        case 'table':
                            if (!content.includes('<table')) {
                                content = `<table><tr><th>العنصر</th><th>الوصف</th></tr><tr><td>نقطة 1</td><td>${content.split('.')[0] || 'وصف افتراضي'}</td></tr><tr><td>نقطة 2</td><td>${content.split('.')[1] || 'وصف افتراضي'}</td></tr><tr><td>نقطة 3</td><td>${content.split('.')[2] || 'وصف افتراضي'}</td></tr></table>`;
                            }
                            break;
                        case 'ul':
                            if (!content.includes('<ul')) {
                                const items = content.replace(/<\/?[^>]+(>|$)/g, '').trim().split('.').filter(item => item.trim()).slice(0, 3);
                                content = `<ul>${items.map(item => `<li>${item.trim()}</li>`).join('') || '<li>نقطة افتراضية</li>'.repeat(3)}</ul>`;
                            }
                            break;
                        case 'img':
                            if (!content.includes('<img')) {
                                content = content.replace(/<\/?[^>]+(>|$)/g, '').trim();
                                content = content.length > 0 ? content : 'وصف افتراضي للصورة';
                                content = `<img src="https://via.placeholder.com/800x400" alt="${content}">`;
                            }
                            break;
                        default:
                            if (!content.includes('<p>')) {
                                content = content.replace(/<\/?[^>]+(>|$)/g, '').trim();
                                if (!content.endsWith('.')) {
                                    content += '...';
                                }
                                content = `<p>${content}</p>`;
                            }
                    }
                    previousContent.push(content.replace(/<\/?[^>]+(>|$)/g, '').trim());
                }

                subField.innerHTML = content;
            }
        }

        updateSeoStats();
    }

    function getArticleContent() {
        const containerClone = document.querySelector('.container').cloneNode(true);
        containerClone.querySelector('.generator').remove();
        containerClone.querySelector('.controls').remove();
        containerClone.querySelector('#seoStats').remove();
        const fields = containerClone.querySelectorAll('.field');
        fields.forEach(field => {
            const subFields = field.querySelectorAll('.sub-field');
            subFields.forEach(subField => {
                field.insertAdjacentHTML('beforeend', subField.innerHTML);
                subField.remove();
            });
        });
        return containerClone.innerHTML;
    }

    function downloadHtml() {
        const content = `<!DOCTYPE html><html lang="ar" dir="rtl"><head><meta charset="UTF-8"><title>${document.getElementById('articleTitle').value || 'مقال'}</title></head><body>${getArticleContent()}</body></html>`;
        const blob = new Blob([content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'مقال_جديد.html';
        a.click();
        URL.revokeObjectURL(url);
    }

    function copyHtml() {
        const content = getArticleContent();
        navigator.clipboard.writeText(content);
        alert('تم نسخ HTML إلى الحافظة!');
    }

    function exportMarkdown() {
        let markdown = getArticleContent()
            .replace(/<h1>/gi, '# ')
            .replace(/<h2>/gi, '## ')
            .replace(/<h3>/gi, '### ')
            .replace(/<h4>/gi, '#### ')
            .replace(/<\/h[1-4]>/gi, '\n')
            .replace(/<p>/gi, '')
            .replace(/<\/p>/gi, '\n\n')
            .replace(/<strong>/gi, '**')
            .replace(/<\/strong>/gi, '**')
            .replace(/<ul>/gi, '')
            .replace(/<\/ul>/gi, '\n')
            .replace(/<li>/gi, '- ')
            .replace(/<\/li>/gi, '\n');
        const blob = new Blob([markdown], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'مقال_جديد.md';
        a.click();
        URL.revokeObjectURL(url);
    }

    function updateSeoStats() {
        const textContent = document.querySelector('.container').innerText;
        const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;
        const h1Count = document.getElementsByTagName('h1').length;
        const h2Count = document.getElementsByTagName('h2').length;
        const h3Count = document.getElementsByTagName('h3').length;
        const h4Count = document.getElementsByTagName('h4').length;

        let statsHtml = `
            <p>عدد الكلمات: ${wordCount} ${wordCount < 2000 ? '<span class="warning">(يجب أن يكون 2000 كلمة على الأقل)</span>' : '<span class="success">(جيد)</span>'}</p>
            <p>عدد H1: ${h1Count} ${h1Count !== 1 ? '<span class="warning">(يجب أن يكون واحد فقط)</span>' : '<span class="success">(جيد)</span>'}</p>
            <p>عدد H2: ${h2Count} ${h2Count < 25 ? '<span class="warning">(يجب أن يكون 25 على الأقل)</span>' : '<span class="success">(جيد)</span>'}</p>
            <p>عدد H3: ${h3Count}</p>
            <p>عدد H4: ${h4Count}</p>
        `;
        document.getElementById('seoStats').innerHTML = statsHtml;
    }

    updateSeoStats();
</script>
</body>
</html>