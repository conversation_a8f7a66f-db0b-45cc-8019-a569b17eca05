<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة | Useful Tools</title>
    <meta name="title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta name="description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير. سهلة الاستخدام وتعمل مباشرة في المتصفح بدون تسجيل.">
    <meta name="keywords" content="محول النصوص, تحويل حالة النصوص, حروف كبيرة, حروف صغيرة, text converter, uppercase, lowercase, capitalize">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Useful Tools">
    <link rel="canonical" href="https://usefultools.com/tool1.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://usefultools.com/tool1.html">
    <meta property="og:title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta property="og:description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير. سهلة الاستخدام وتعمل مباشرة في المتصفح.">
    <meta property="og:image" content="https://usefultools.com/assets/text-converter-og.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://usefultools.com/tool1.html">
    <meta property="twitter:title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta property="twitter:description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير.">
    <meta property="twitter:image" content="https://usefultools.com/assets/text-converter-og.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <meta name="theme-color" content="#4a6baf">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="enhanced-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "محول حالة النصوص",
        "alternateName": "Text Case Converter",
        "url": "https://usefultools.com/tool1.html",
        "description": "أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "تحويل إلى حروف كبيرة",
            "تحويل إلى حروف صغيرة",
            "تحويل كل كلمة بحرف كبير",
            "نسخ النتيجة بنقرة واحدة"
        ]
    }
    </script>
</head>
<body>
    <header>
        <h1>محول حالة النصوص <span>Text Case Converter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <!-- Breadcrumb Navigation -->
        <nav class="breadcrumb" aria-label="Breadcrumb">
            <div class="breadcrumb-item">
                <a href="index.html">
                    <i class="fas fa-home"></i>
                    <span class="ar">الرئيسية</span>
                    <span class="en">Home</span>
                </a>
            </div>
            <div class="breadcrumb-item">
                <span class="ar">أدوات النصوص</span>
                <span class="en">Text Tools</span>
            </div>
            <div class="breadcrumb-item active">
                <span class="ar">محول حالة النصوص</span>
                <span class="en">Text Case Converter</span>
            </div>
        </nav>

        <!-- Tool Header -->
        <div class="tool-header">
            <div class="tool-icon">
                <i class="fas fa-font"></i>
            </div>
            <div class="tool-info">
                <h1 class="ar">محول حالة النصوص</h1>
                <h1 class="en">Text Case Converter</h1>
                <p class="ar">تحويل النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير</p>
                <p class="en">Convert text to uppercase, lowercase, or capitalize each word</p>
            </div>
        </div>

        <div class="input-group">
            <label for="inputText" class="ar">أدخل النص هنا:</label>
            <label for="inputText" class="en">Enter your text here:</label>
            <textarea id="inputText" rows="5" placeholder="Type or paste your text..."></textarea>
        </div>

        <div class="button-group">
            <button id="upperCase" class="ar">حروف كبيرة</button>
            <button id="upperCase" class="en">UPPERCASE</button>
            
            <button id="lowerCase" class="ar">حروف صغيرة</button>
            <button id="lowerCase" class="en">lowercase</button>
            
            <button id="capitalize" class="ar">كل كلمة بحرف كبير</button>
            <button id="capitalize" class="en">Capitalize Words</button>
        </div>

        <div class="output-group">
            <label for="outputText" class="ar">النتيجة:</label>
            <label for="outputText" class="en">Result:</label>
            <textarea id="outputText" rows="5" readonly></textarea>
            <button id="copyBtn" class="ar">نسخ النتيجة</button>
            <button id="copyBtn" class="en">Copy Result</button>
        </div>
    </main>

    <footer class="tool-footer">
        <a href="index.html">
            <i class="fas fa-arrow-left"></i>
            <span class="ar">العودة إلى القائمة الرئيسية</span>
            <span class="en">Back to Main Menu</span>
        </a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Load saved input if exists
            const inputText = document.getElementById('inputText');
            inputText.value = window.utils.getSavedInput('caseConverter');

            // Case conversion functions
            document.getElementById('upperCase').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.toUpperCase();
                window.utils.saveInput('caseConverter', text);
            });

            document.getElementById('lowerCase').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.toLowerCase();
                window.utils.saveInput('caseConverter', text);
            });

            document.getElementById('capitalize').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.replace(/\b\w/g, l => l.toUpperCase());
                window.utils.saveInput('caseConverter', text);
            });

            // Copy to clipboard
            document.getElementById('copyBtn').addEventListener('click', () => {
                const outputText = document.getElementById('outputText').value;
                if (outputText) {
                    window.utils.copyToClipboard(outputText);
                }
            });
        });
    </script>
</body>
</html>