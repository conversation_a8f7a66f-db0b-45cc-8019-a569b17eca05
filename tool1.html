<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة | Useful Tools</title>
    <meta name="title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta name="description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير. سهلة الاستخدام وتعمل مباشرة في المتصفح بدون تسجيل.">
    <meta name="keywords" content="محول النصوص, تحويل حالة النصوص, حروف كبيرة, حروف صغيرة, text converter, uppercase, lowercase, capitalize">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Useful Tools">
    <link rel="canonical" href="https://usefultools.com/tool1.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://usefultools.com/tool1.html">
    <meta property="og:title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta property="og:description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير. سهلة الاستخدام وتعمل مباشرة في المتصفح.">
    <meta property="og:image" content="https://usefultools.com/assets/text-converter-og.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://usefultools.com/tool1.html">
    <meta property="twitter:title" content="محول حالة النصوص - تحويل النصوص إلى حروف كبيرة وصغيرة">
    <meta property="twitter:description" content="أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير.">
    <meta property="twitter:image" content="https://usefultools.com/assets/text-converter-og.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <meta name="theme-color" content="#4a6baf">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="enhanced-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "محول حالة النصوص",
        "alternateName": "Text Case Converter",
        "url": "https://usefultools.com/tool1.html",
        "description": "أداة مجانية لتحويل حالة النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "تحويل إلى حروف كبيرة",
            "تحويل إلى حروف صغيرة",
            "تحويل كل كلمة بحرف كبير",
            "نسخ النتيجة بنقرة واحدة"
        ]
    }
    </script>
</head>
<body>
    <header>
        <h1>محول حالة النصوص <span>Text Case Converter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <!-- Breadcrumb Navigation -->
        <nav class="breadcrumb" aria-label="Breadcrumb">
            <div class="breadcrumb-item">
                <a href="index.html">
                    <i class="fas fa-home"></i>
                    <span class="ar">الرئيسية</span>
                    <span class="en">Home</span>
                </a>
            </div>
            <div class="breadcrumb-item">
                <span class="ar">أدوات النصوص</span>
                <span class="en">Text Tools</span>
            </div>
            <div class="breadcrumb-item active">
                <span class="ar">محول حالة النصوص</span>
                <span class="en">Text Case Converter</span>
            </div>
        </nav>

        <!-- Tool Header -->
        <div class="tool-header">
            <div class="tool-icon">
                <i class="fas fa-font"></i>
            </div>
            <div class="tool-info">
                <h1 class="ar">محول حالة النصوص</h1>
                <h1 class="en">Text Case Converter</h1>
                <p class="ar">تحويل النصوص إلى حروف كبيرة، صغيرة، أو كل كلمة بحرف كبير</p>
                <p class="en">Convert text to uppercase, lowercase, or capitalize each word</p>
            </div>
        </div>

        <div class="tool-workspace">
            <div class="input-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-edit"></i>
                        <span class="ar">إدخال النص</span>
                        <span class="en">Text Input</span>
                    </h3>
                    <div class="section-actions">
                        <button class="btn-icon" onclick="pasteFromClipboard()" title="لصق من الحافظة">
                            <i class="fas fa-paste"></i>
                        </button>
                        <button class="btn-icon" onclick="clearInput()" title="مسح النص">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="input-group">
                    <textarea id="inputText" rows="8" placeholder="اكتب النص هنا أو الصق من الحافظة... / Type or paste text here..."></textarea>
                    <div class="input-stats">
                        <span class="stat">
                            <i class="fas fa-font"></i>
                            <span id="charCount">0</span>
                            <span class="ar">حرف</span>
                            <span class="en">characters</span>
                        </span>
                        <span class="stat">
                            <i class="fas fa-file-word"></i>
                            <span id="wordCount">0</span>
                            <span class="ar">كلمة</span>
                            <span class="en">words</span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="controls-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-cogs"></i>
                        <span class="ar">خيارات التحويل</span>
                        <span class="en">Conversion Options</span>
                    </h3>
                </div>
                <div class="button-grid">
                    <button id="upperCase" class="btn-action primary">
                        <i class="fas fa-arrow-up"></i>
                        <span class="ar">حروف كبيرة</span>
                        <span class="en">UPPERCASE</span>
                    </button>

                    <button id="lowerCase" class="btn-action secondary">
                        <i class="fas fa-arrow-down"></i>
                        <span class="ar">حروف صغيرة</span>
                        <span class="en">lowercase</span>
                    </button>

                    <button id="capitalize" class="btn-action accent">
                        <i class="fas fa-text-height"></i>
                        <span class="ar">كل كلمة بحرف كبير</span>
                        <span class="en">Title Case</span>
                    </button>

                    <button id="sentenceCase" class="btn-action info">
                        <i class="fas fa-paragraph"></i>
                        <span class="ar">حرف كبير للجملة</span>
                        <span class="en">Sentence case</span>
                    </button>
                </div>
            </div>

            <div class="output-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-check-circle"></i>
                        <span class="ar">النتيجة</span>
                        <span class="en">Result</span>
                    </h3>
                    <div class="section-actions">
                        <button id="copyBtn" class="btn-icon success" title="نسخ النتيجة">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-icon" onclick="downloadResult()" title="تحميل كملف">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn-icon" onclick="clearOutput()" title="مسح النتيجة">
                            <i class="fas fa-eraser"></i>
                        </button>
                    </div>
                </div>
                <div class="output-group">
                    <textarea id="outputText" rows="8" readonly placeholder="ستظهر النتيجة هنا... / Result will appear here..."></textarea>
                    <div class="output-stats">
                        <span id="conversionStatus" class="stat success" style="display: none;">
                            <i class="fas fa-check"></i>
                            <span class="ar">تم التحويل بنجاح</span>
                            <span class="en">Converted successfully</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="tool-footer">
        <a href="index.html">
            <i class="fas fa-arrow-left"></i>
            <span class="ar">العودة إلى القائمة الرئيسية</span>
            <span class="en">Back to Main Menu</span>
        </a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            const outputText = document.getElementById('outputText');
            const charCount = document.getElementById('charCount');
            const wordCount = document.getElementById('wordCount');
            const conversionStatus = document.getElementById('conversionStatus');

            // Load saved input if exists
            inputText.value = window.utils.getSavedInput('caseConverter');
            updateStats();

            // Update character and word count
            function updateStats() {
                const text = inputText.value;
                charCount.textContent = text.length;
                wordCount.textContent = text.trim() ? text.trim().split(/\s+/).length : 0;
            }

            // Show conversion status
            function showStatus() {
                conversionStatus.style.display = 'flex';
                setTimeout(() => {
                    conversionStatus.style.display = 'none';
                }, 3000);
            }

            // Input event listener for real-time stats
            inputText.addEventListener('input', updateStats);

            // Case conversion functions
            document.getElementById('upperCase').addEventListener('click', () => {
                const text = inputText.value;
                if (text.trim()) {
                    outputText.value = text.toUpperCase();
                    window.utils.saveInput('caseConverter', text);
                    showStatus();
                }
            });

            document.getElementById('lowerCase').addEventListener('click', () => {
                const text = inputText.value;
                if (text.trim()) {
                    outputText.value = text.toLowerCase();
                    window.utils.saveInput('caseConverter', text);
                    showStatus();
                }
            });

            document.getElementById('capitalize').addEventListener('click', () => {
                const text = inputText.value;
                if (text.trim()) {
                    outputText.value = text.replace(/\b\w/g, l => l.toUpperCase());
                    window.utils.saveInput('caseConverter', text);
                    showStatus();
                }
            });

            document.getElementById('sentenceCase').addEventListener('click', () => {
                const text = inputText.value;
                if (text.trim()) {
                    outputText.value = text.toLowerCase().replace(/(^\w|\.\s+\w)/g, l => l.toUpperCase());
                    window.utils.saveInput('caseConverter', text);
                    showStatus();
                }
            });

            // Copy to clipboard
            document.getElementById('copyBtn').addEventListener('click', () => {
                const text = outputText.value;
                if (text) {
                    navigator.clipboard.writeText(text).then(() => {
                        const icon = document.querySelector('#copyBtn i');
                        const originalClass = icon.className;
                        icon.className = 'fas fa-check';
                        setTimeout(() => {
                            icon.className = originalClass;
                        }, 2000);
                    });
                }
            });

            // Additional functions
            window.pasteFromClipboard = async () => {
                try {
                    const text = await navigator.clipboard.readText();
                    inputText.value = text;
                    updateStats();
                } catch (err) {
                    console.error('Failed to read clipboard:', err);
                }
            };

            window.clearInput = () => {
                inputText.value = '';
                updateStats();
            };

            window.clearOutput = () => {
                outputText.value = '';
            };

            window.downloadResult = () => {
                const text = outputText.value;
                if (text) {
                    const blob = new Blob([text], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'converted-text.txt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            };
        });
    </script>
</body>
</html>