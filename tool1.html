<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول حالة النصوص - Text Case Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>محول حالة النصوص <span>Text Case Converter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="inputText" class="ar">أدخل النص هنا:</label>
            <label for="inputText" class="en">Enter your text here:</label>
            <textarea id="inputText" rows="5" placeholder="Type or paste your text..."></textarea>
        </div>

        <div class="button-group">
            <button id="upperCase" class="ar">حروف كبيرة</button>
            <button id="upperCase" class="en">UPPERCASE</button>
            
            <button id="lowerCase" class="ar">حروف صغيرة</button>
            <button id="lowerCase" class="en">lowercase</button>
            
            <button id="capitalize" class="ar">كل كلمة بحرف كبير</button>
            <button id="capitalize" class="en">Capitalize Words</button>
        </div>

        <div class="output-group">
            <label for="outputText" class="ar">النتيجة:</label>
            <label for="outputText" class="en">Result:</label>
            <textarea id="outputText" rows="5" readonly></textarea>
            <button id="copyBtn" class="ar">نسخ النتيجة</button>
            <button id="copyBtn" class="en">Copy Result</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Load saved input if exists
            const inputText = document.getElementById('inputText');
            inputText.value = window.utils.getSavedInput('caseConverter');

            // Case conversion functions
            document.getElementById('upperCase').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.toUpperCase();
                window.utils.saveInput('caseConverter', text);
            });

            document.getElementById('lowerCase').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.toLowerCase();
                window.utils.saveInput('caseConverter', text);
            });

            document.getElementById('capitalize').addEventListener('click', () => {
                const text = inputText.value;
                document.getElementById('outputText').value = text.replace(/\b\w/g, l => l.toUpperCase());
                window.utils.saveInput('caseConverter', text);
            });

            // Copy to clipboard
            document.getElementById('copyBtn').addEventListener('click', () => {
                const outputText = document.getElementById('outputText').value;
                if (outputText) {
                    window.utils.copyToClipboard(outputText);
                }
            });
        });
    </script>
</body>
</html>