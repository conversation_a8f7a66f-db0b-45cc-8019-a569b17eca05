<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أرقام عشوائية - Random Number Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .generator-form {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .form-group input[type="number"] {
            padding: 8px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .options-group {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 15px 0;
        }
        .option-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .result-container {
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
        }
        .result-numbers {
            font-size: 1.5rem;
            font-family: monospace;
            color: var(--text-color);
            word-break: break-all;
            line-height: 1.6;
        }
        .history {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
        }
        .history-item {
            padding: 10px;
            border-bottom: 1px solid var(--secondary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .history-numbers {
            color: var(--text-color);
        }
        .history-timestamp {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد أرقام عشوائية <span>Random Number Generator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="generator-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="minNumber" class="ar">الحد الأدنى:</label>
                    <label for="minNumber" class="en">Minimum:</label>
                    <input type="number" id="minNumber" value="1">
                </div>

                <div class="form-group">
                    <label for="maxNumber" class="ar">الحد الأقصى:</label>
                    <label for="maxNumber" class="en">Maximum:</label>
                    <input type="number" id="maxNumber" value="100">
                </div>

                <div class="form-group">
                    <label for="quantity" class="ar">عدد الأرقام:</label>
                    <label for="quantity" class="en">How many numbers:</label>
                    <input type="number" id="quantity" min="1" max="1000" value="1">
                </div>
            </div>

            <div class="options-group">
                <div class="option-item">
                    <input type="checkbox" id="uniqueNumbers">
                    <label for="uniqueNumbers" class="ar">أرقام فريدة</label>
                    <label for="uniqueNumbers" class="en">Unique numbers</label>
                </div>

                <div class="option-item">
                    <input type="checkbox" id="sortNumbers">
                    <label for="sortNumbers" class="ar">ترتيب تصاعدي</label>
                    <label for="sortNumbers" class="en">Sort ascending</label>
                </div>

                <div class="option-item">
                    <input type="checkbox" id="includeZero">
                    <label for="includeZero" class="ar">تضمين الصفر</label>
                    <label for="includeZero" class="en">Include zero</label>
                </div>
            </div>

            <div class="button-group">
                <button id="generateBtn" class="ar">توليد الأرقام</button>
                <button id="generateBtn" class="en">Generate Numbers</button>
                
                <button id="copyBtn" class="ar" disabled>نسخ النتائج</button>
                <button id="copyBtn" class="en" disabled>Copy Results</button>
            </div>
        </div>

        <div class="result-container" style="display: none;">
            <div class="result-numbers" id="result"></div>
        </div>

        <div class="history">
            <h3 class="ar">السجل السابق</h3>
            <h3 class="en">History</h3>
            <div id="historyList"></div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const minNumber = document.getElementById('minNumber');
            const maxNumber = document.getElementById('maxNumber');
            const quantity = document.getElementById('quantity');
            const uniqueNumbers = document.getElementById('uniqueNumbers');
            const sortNumbers = document.getElementById('sortNumbers');
            const includeZero = document.getElementById('includeZero');
            const generateBtn = document.getElementById('generateBtn');
            const copyBtn = document.getElementById('copyBtn');
            const resultContainer = document.querySelector('.result-container');
            const result = document.getElementById('result');
            const historyList = document.getElementById('historyList');

            // Load saved settings
            const savedSettings = JSON.parse(localStorage.getItem('randomGenerator_settings') || '{}');
            minNumber.value = savedSettings.min || 1;
            maxNumber.value = savedSettings.max || 100;
            quantity.value = savedSettings.quantity || 1;
            uniqueNumbers.checked = savedSettings.unique || false;
            sortNumbers.checked = savedSettings.sort || false;
            includeZero.checked = savedSettings.includeZero || false;

            // Load history
            const history = JSON.parse(localStorage.getItem('randomGenerator_history') || '[]');
            updateHistoryDisplay();

            function generateRandomNumbers() {
                let min = parseInt(minNumber.value);
                let max = parseInt(maxNumber.value);
                let count = parseInt(quantity.value);
                
                if (!includeZero.checked && min <= 0) min = 1;
                if (uniqueNumbers.checked) {
                    count = Math.min(count, max - min + 1);
                }

                const numbers = [];
                const used = new Set();

                while (numbers.length < count) {
                    const num = Math.floor(Math.random() * (max - min + 1)) + min;
                    if (!uniqueNumbers.checked || !used.has(num)) {
                        numbers.push(num);
                        used.add(num);
                    }
                }

                if (sortNumbers.checked) {
                    numbers.sort((a, b) => a - b);
                }

                return numbers;
            }

            function updateHistoryDisplay() {
                historyList.innerHTML = history
                    .map(item => `
                        <div class="history-item">
                            <span class="history-numbers">${item.numbers.join(', ')}</span>
                            <span class="history-timestamp">${new Date(item.timestamp).toLocaleString()}</span>
                        </div>
                    `)
                    .join('');
            }

            function saveSettings() {
                const settings = {
                    min: minNumber.value,
                    max: maxNumber.value,
                    quantity: quantity.value,
                    unique: uniqueNumbers.checked,
                    sort: sortNumbers.checked,
                    includeZero: includeZero.checked
                };
                localStorage.setItem('randomGenerator_settings', JSON.stringify(settings));
            }

            // Generate numbers
            generateBtn.addEventListener('click', () => {
                const numbers = generateRandomNumbers();
                result.textContent = numbers.join(', ');
                resultContainer.style.display = 'block';
                copyBtn.disabled = false;

                // Add to history
                history.unshift({
                    numbers,
                    timestamp: new Date().getTime()
                });
                if (history.length > 10) history.pop();
                localStorage.setItem('randomGenerator_history', JSON.stringify(history));
                updateHistoryDisplay();

                // Save settings
                saveSettings();
            });

            // Copy results
            copyBtn.addEventListener('click', () => {
                window.utils.copyToClipboard(result.textContent);
            });

            // Save settings on change
            [minNumber, maxNumber, quantity].forEach(input => {
                input.addEventListener('change', saveSettings);
            });
            [uniqueNumbers, sortNumbers, includeZero].forEach(checkbox => {
                checkbox.addEventListener('change', saveSettings);
            });
        });
    </script>
</body>
</html>