<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المهام - Todo List</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .todo-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .todo-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .todo-input input {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .todo-list {
            list-style: none;
            padding: 0;
        }
        .todo-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background-color: var(--bg-color);
            border-radius: 4px;
            transition: opacity 0.3s;
        }
        .todo-item.completed {
            opacity: 0.7;
        }
        .todo-item label {
            flex-grow: 1;
            margin: 0 10px;
            color: var(--text-color);
        }
        .todo-item.completed label {
            text-decoration: line-through;
        }
        .todo-actions {
            display: flex;
            gap: 10px;
        }
        .todo-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
        }
        .todo-actions .edit-btn {
            color: var(--primary-color);
        }
        .todo-actions .delete-btn {
            color: #ff4444;
        }
        .filters {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        .filter-btn {
            background: none;
            border: 1px solid var(--border-color);
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            color: var(--text-color);
        }
        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>قائمة المهام <span>Todo List</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main>
        <div class="todo-container">
            <div class="todo-input">
                <input type="text" id="todoInput" placeholder="أضف مهمة جديدة... (Add new task...)">
                <button onclick="addTodo()" class="primary-button">
                    <i class="fas fa-plus"></i> إضافة
                </button>
            </div>

            <div class="filters">
                <button class="filter-btn active" data-filter="all">الكل (All)</button>
                <button class="filter-btn" data-filter="active">النشطة (Active)</button>
                <button class="filter-btn" data-filter="completed">المكتملة (Completed)</button>
            </div>

            <ul class="todo-list" id="todoList">
                <!-- Todo items will be added here dynamically -->
            </ul>
        </div>
    </main>

    <script>
        let todos = JSON.parse(localStorage.getItem('todos')) || [];
        let currentFilter = 'all';

        function saveTodos() {
            localStorage.setItem('todos', JSON.stringify(todos));
        }

        function addTodo() {
            const input = document.getElementById('todoInput');
            const text = input.value.trim();

            if (text) {
                todos.push({
                    id: Date.now(),
                    text,
                    completed: false
                });
                saveTodos();
                input.value = '';
                renderTodos();
            }
        }

        function toggleTodo(id) {
            todos = todos.map(todo =>
                todo.id === id ? { ...todo, completed: !todo.completed } : todo
            );
            saveTodos();
            renderTodos();
        }

        function editTodo(id) {
            const todo = todos.find(t => t.id === id);
            const newText = prompt('تعديل المهمة (Edit task):', todo.text);
            
            if (newText !== null && newText.trim()) {
                todos = todos.map(t =>
                    t.id === id ? { ...t, text: newText.trim() } : t
                );
                saveTodos();
                renderTodos();
            }
        }

        function deleteTodo(id) {
            todos = todos.filter(todo => todo.id !== id);
            saveTodos();
            renderTodos();
        }

        function setFilter(filter) {
            currentFilter = filter;
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.filter === filter);
            });
            renderTodos();
        }

        function renderTodos() {
            const list = document.getElementById('todoList');
            list.innerHTML = '';

            const filteredTodos = todos.filter(todo => {
                if (currentFilter === 'active') return !todo.completed;
                if (currentFilter === 'completed') return todo.completed;
                return true;
            });

            filteredTodos.forEach(todo => {
                const li = document.createElement('li');
                li.className = `todo-item ${todo.completed ? 'completed' : ''}`;
                li.innerHTML = `
                    <input type="checkbox" ${todo.completed ? 'checked' : ''} 
                           onchange="toggleTodo(${todo.id})">
                    <label>${todo.text}</label>
                    <div class="todo-actions">
                        <button class="edit-btn" onclick="editTodo(${todo.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-btn" onclick="deleteTodo(${todo.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                list.appendChild(li);
            });
        }

        // Event listeners
        document.getElementById('todoInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') addTodo();
        });

        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => setFilter(btn.dataset.filter));
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
        });

        // Language toggle
        const toggleLang = document.getElementById('toggleLang');
        toggleLang.addEventListener('click', () => {
            document.documentElement.dir = document.documentElement.dir === 'rtl' ? 'ltr' : 'rtl';
            document.documentElement.lang = document.documentElement.lang === 'ar' ? 'en' : 'ar';
        });

        // Initial render
        renderTodos();
    </script>
</body>
</html>