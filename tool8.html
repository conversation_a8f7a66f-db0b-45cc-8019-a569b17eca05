<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير حجم الصور - Image Resizer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .image-container {
            max-width: 100%;
            margin: 20px 0;
            text-align: center;
        }
        #previewImage {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            display: none;
        }
        .dimension-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .dimension-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .dimension-input input {
            width: 100px;
            padding: 5px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        .aspect-ratio-lock {
            cursor: pointer;
            color: var(--primary-color);
            padding: 5px;
        }
        .drag-area {
            border: 2px dashed var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .drag-area:hover {
            border-color: var(--primary-color);
        }
        .drag-area i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <header>
        <h1>تغيير حجم الصور <span>Image Resizer</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="drag-area" id="dragArea">
            <i class="fas fa-cloud-upload-alt"></i>
            <p class="ar">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
            <p class="en">Drag & drop image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="image-container">
            <img id="previewImage" alt="Preview">
        </div>

        <div class="dimension-controls">
            <div class="dimension-input">
                <label for="widthInput" class="ar">العرض:</label>
                <label for="widthInput" class="en">Width:</label>
                <input type="number" id="widthInput" min="1">
                <span>px</span>
            </div>

            <div class="dimension-input">
                <label for="heightInput" class="ar">الارتفاع:</label>
                <label for="heightInput" class="en">Height:</label>
                <input type="number" id="heightInput" min="1">
                <span>px</span>
            </div>

            <div class="dimension-input">
                <i class="fas fa-link aspect-ratio-lock" id="aspectRatioLock" title="Lock aspect ratio"></i>
            </div>
        </div>

        <div class="button-group">
            <button id="resizeBtn" class="ar">تغيير الحجم</button>
            <button id="resizeBtn" class="en">Resize</button>
            
            <button id="downloadBtn" class="ar" disabled>تحميل</button>
            <button id="downloadBtn" class="en" disabled>Download</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const previewImage = document.getElementById('previewImage');
            const widthInput = document.getElementById('widthInput');
            const heightInput = document.getElementById('heightInput');
            const aspectRatioLock = document.getElementById('aspectRatioLock');
            const resizeBtn = document.getElementById('resizeBtn');
            const downloadBtn = document.getElementById('downloadBtn');

            let originalImage = null;
            let aspectRatio = 1;
            let keepAspectRatio = true;

            // Toggle aspect ratio lock
            aspectRatioLock.addEventListener('click', () => {
                keepAspectRatio = !keepAspectRatio;
                aspectRatioLock.style.color = keepAspectRatio ? 
                    'var(--primary-color)' : 'var(--secondary-color)';
            });

            // Handle file selection
            function handleFile(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = () => {
                            originalImage = img;
                            previewImage.src = img.src;
                            previewImage.style.display = 'block';
                            aspectRatio = img.width / img.height;
                            widthInput.value = img.width;
                            heightInput.value = img.height;
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }

            // File input change
            fileInput.addEventListener('change', (e) => {
                handleFile(e.target.files[0]);
            });

            // Drag and drop
            dragArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--primary-color)';
            });

            dragArea.addEventListener('dragleave', () => {
                dragArea.style.borderColor = 'var(--secondary-color)';
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--secondary-color)';
                handleFile(e.dataTransfer.files[0]);
            });

            dragArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Handle dimension inputs
            widthInput.addEventListener('input', () => {
                if (keepAspectRatio && originalImage) {
                    heightInput.value = Math.round(widthInput.value / aspectRatio);
                }
            });

            heightInput.addEventListener('input', () => {
                if (keepAspectRatio && originalImage) {
                    widthInput.value = Math.round(heightInput.value * aspectRatio);
                }
            });

            // Resize image
            resizeBtn.addEventListener('click', () => {
                if (!originalImage) return;

                const canvas = document.createElement('canvas');
                canvas.width = parseInt(widthInput.value);
                canvas.height = parseInt(heightInput.value);
                const ctx = canvas.getContext('2d');

                // Use better quality settings
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);
                previewImage.src = canvas.toDataURL('image/png');
                downloadBtn.disabled = false;
            });

            // Download resized image
            downloadBtn.addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = 'resized-image.png';
                link.href = previewImage.src;
                link.click();
            });
        });
    </script>
</body>
</html>