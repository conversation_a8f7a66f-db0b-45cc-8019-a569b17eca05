<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدقق تنسيق الإيميل - Email Format Validator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .validation-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: var(--border-radius);
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
        }
        .validation-result.valid {
            background-color: #28a745;
            color: white;
        }
        .validation-result.invalid {
            background-color: #dc3545;
            color: white;
        }
        .validation-details {
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>مدقق تنسيق الإيميل <span>Email Format Validator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="emailInput" class="ar">أدخل عنوان البريد الإلكتروني:</label>
            <label for="emailInput" class="en">Enter email address:</label>
            <input type="text" id="emailInput" placeholder="<EMAIL>" style="width: 100%; padding: 10px;">
        </div>

        <div class="button-group">
            <button id="validateBtn" class="ar">تحقق من الصحة</button>
            <button id="validateBtn" class="en">Validate</button>
        </div>

        <div id="validationResult" class="validation-result" style="display: none;"></div>
        <div id="validationDetails" class="validation-details" style="display: none;"></div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const emailInput = document.getElementById('emailInput');
            const validationResult = document.getElementById('validationResult');
            const validationDetails = document.getElementById('validationDetails');

            // Load saved input if exists
            emailInput.value = window.utils.getSavedInput('emailValidator');

            // Comprehensive email validation
            function validateEmail(email) {
                const regex = /^(?=[a-zA-Z0-9@._%+-]{6,254}$)[a-zA-Z0-9._%+-]{1,64}@(?:[a-zA-Z0-9-]{1,63}\.){1,8}[a-zA-Z]{2,63}$/;
                
                const checks = {
                    format: regex.test(email),
                    length: email.length <= 254,
                    localPart: email.split('@')[0]?.length <= 64,
                    noDotAtEnds: !email.startsWith('.') && !email.endsWith('.'),
                    noConsecutiveDots: !/\.{2,}/.test(email)
                };

                return {
                    isValid: Object.values(checks).every(v => v),
                    checks
                };
            }

            // Display validation results
            function showValidationResult(validationResult, details) {
                const isValid = validationResult.isValid;
                const resultDiv = document.getElementById('validationResult');
                const detailsDiv = document.getElementById('validationDetails');

                resultDiv.style.display = 'block';
                detailsDiv.style.display = 'block';

                resultDiv.className = 'validation-result ' + (isValid ? 'valid' : 'invalid');
                
                const lang = document.documentElement.lang;
                resultDiv.textContent = isValid ? 
                    (lang === 'ar' ? 'صيغة البريد الإلكتروني صحيحة ✓' : 'Email format is valid ✓') :
                    (lang === 'ar' ? 'صيغة البريد الإلكتروني غير صحيحة ✗' : 'Email format is invalid ✗');

                // Show detailed validation feedback
                const getDetailMessage = (check, isValid) => {
                    const messages = {
                        format: {
                            ar: 'تنسيق البريد الإلكتروني العام',
                            en: 'General email format'
                        },
                        length: {
                            ar: 'الطول الإجمالي للبريد الإلكتروني',
                            en: 'Overall email length'
                        },
                        localPart: {
                            ar: 'طول الجزء المحلي (قبل @)',
                            en: 'Local part length (before @)'
                        },
                        noDotAtEnds: {
                            ar: 'لا توجد نقاط في البداية أو النهاية',
                            en: 'No dots at start or end'
                        },
                        noConsecutiveDots: {
                            ar: 'لا توجد نقاط متتالية',
                            en: 'No consecutive dots'
                        }
                    };

                    return `${messages[check][lang]}: ${isValid ? '✓' : '✗'}`;
                };

                detailsDiv.innerHTML = Object.entries(details.checks)
                    .map(([check, isValid]) => `<div>${getDetailMessage(check, isValid)}</div>`)
                    .join('');
            }

            // Validate on button click
            document.getElementById('validateBtn').addEventListener('click', () => {
                const email = emailInput.value.trim();
                const validation = validateEmail(email);
                showValidationResult(validation, validation);
                window.utils.saveInput('emailValidator', email);
            });

            // Validate on Enter key
            emailInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('validateBtn').click();
                }
            });
        });
    </script>
</body>
</html>