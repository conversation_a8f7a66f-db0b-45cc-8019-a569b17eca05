<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد روابط الإيميل - Email Link Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-color);
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        .output-box {
            background-color: var(--card-bg);
            padding: 15px;
            border-radius: var(--border-radius);
            margin: 10px 0;
            position: relative;
        }
        .output-box h4 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        .output-value {
            word-break: break-all;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        .preview-link {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .preview-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد روابط الإيميل <span>Email Link Generator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <div class="form-group">
                <label for="to" class="ar">البريد الإلكتروني:</label>
                <label for="to" class="en">Email Address:</label>
                <input type="email" id="to" placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="subject" class="ar">الموضوع:</label>
                <label for="subject" class="en">Subject:</label>
                <input type="text" id="subject">
            </div>

            <div class="form-group">
                <label for="cc" class="ar">نسخة كربونية (CC):</label>
                <label for="cc" class="en">CC:</label>
                <input type="text" id="cc" placeholder="<EMAIL>, <EMAIL>">
            </div>

            <div class="form-group">
                <label for="bcc" class="ar">نسخة كربونية مخفية (BCC):</label>
                <label for="bcc" class="en">BCC:</label>
                <input type="text" id="bcc" placeholder="<EMAIL>, <EMAIL>">
            </div>

            <div class="form-group">
                <label for="body" class="ar">نص الرسالة:</label>
                <label for="body" class="en">Body:</label>
                <textarea id="body" rows="4"></textarea>
            </div>
        </div>

        <div class="button-group">
            <button id="generateBtn" class="ar">توليد الروابط</button>
            <button id="generateBtn" class="en">Generate Links</button>
        </div>

        <div class="output-group">
            <div class="output-box">
                <h4 class="ar">رابط mailto:</h4>
                <h4 class="en">mailto: Link</h4>
                <div id="mailtoOutput" class="output-value"></div>
                <button class="copy-email" id="copyMailto">
                    <i class="fas fa-copy"></i>
                    <span class="ar">نسخ</span>
                    <span class="en">Copy</span>
                </button>
            </div>

            <div class="output-box">
                <h4 class="ar">كود HTML:</h4>
                <h4 class="en">HTML Code:</h4>
                <div id="htmlOutput" class="output-value"></div>
                <button class="copy-email" id="copyHtml">
                    <i class="fas fa-copy"></i>
                    <span class="ar">نسخ</span>
                    <span class="en">Copy</span>
                </button>
            </div>

            <div class="output-box">
                <h4 class="ar">معاينة:</h4>
                <h4 class="en">Preview:</h4>
                <div id="previewOutput"></div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputs = {
                to: document.getElementById('to'),
                subject: document.getElementById('subject'),
                cc: document.getElementById('cc'),
                bcc: document.getElementById('bcc'),
                body: document.getElementById('body')
            };

            // Load saved inputs
            Object.keys(inputs).forEach(key => {
                inputs[key].value = localStorage.getItem(`emailLink_${key}`) || '';
            });

            function generateMailtoLink() {
                const params = new URLSearchParams();
                if (inputs.subject.value) params.append('subject', inputs.subject.value);
                if (inputs.cc.value) params.append('cc', inputs.cc.value);
                if (inputs.bcc.value) params.append('bcc', inputs.bcc.value);
                if (inputs.body.value) params.append('body', inputs.body.value);

                const mailtoLink = `mailto:${inputs.to.value}${params.toString() ? '?' + params.toString() : ''}`;
                const htmlCode = `<a href="${mailtoLink}">${inputs.to.value}</a>`;

                document.getElementById('mailtoOutput').textContent = mailtoLink;
                document.getElementById('htmlOutput').textContent = htmlCode;
                document.getElementById('previewOutput').innerHTML = htmlCode;

                // Save inputs
                Object.keys(inputs).forEach(key => {
                    localStorage.setItem(`emailLink_${key}`, inputs[key].value);
                });
            }

            // Generate links on button click
            document.getElementById('generateBtn').addEventListener('click', generateMailtoLink);

            // Copy buttons
            document.getElementById('copyMailto').addEventListener('click', () => {
                window.utils.copyToClipboard(document.getElementById('mailtoOutput').textContent);
            });

            document.getElementById('copyHtml').addEventListener('click', () => {
                window.utils.copyToClipboard(document.getElementById('htmlOutput').textContent);
            });

            // Generate on any input change
            Object.values(inputs).forEach(input => {
                input.addEventListener('input', generateMailtoLink);
            });
        });
    </script>
</body>
</html>