<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد قوائم التسوق - Shopping List Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .generator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            display: grid;
            grid-template-columns: 2fr 1fr auto;
            gap: 10px;
            margin-bottom: 20px;
        }
        .input-section input, .input-section select {
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .category-tag {
            padding: 5px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .category-tag.active {
            background-color: var(--primary-color);
            color: white;
        }
        .shopping-list {
            margin-top: 20px;
        }
        .list-category {
            margin-bottom: 20px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            overflow: hidden;
        }
        .category-header {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .items-list {
            padding: 15px;
        }
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .item-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .item-actions {
            display: flex;
            gap: 10px;
        }
        .action-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            color: var(--text-color);
        }
        .action-button.delete {
            color: #dc3545;
        }
        .print-section {
            margin-top: 20px;
            text-align: center;
        }
        @media print {
            header, .input-section, .categories, .action-button {
                display: none !important;
            }
            .list-category {
                break-inside: avoid;
            }
            .category-header {
                background-color: #f0f0f0 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد قوائم التسوق <span>Shopping List Generator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="generator">
            <div class="input-section">
                <input type="text" id="itemName" placeholder="اسم العنصر - Item name">
                <input type="number" id="itemQuantity" placeholder="الكمية - Quantity" min="1" value="1">
                <button onclick="addItem()" class="primary-button">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <div class="categories">
                <button class="category-tag active" data-category="groceries">
                    <i class="fas fa-shopping-basket"></i> بقالة - Groceries
                </button>
                <button class="category-tag" data-category="vegetables">
                    <i class="fas fa-carrot"></i> خضروات - Vegetables
                </button>
                <button class="category-tag" data-category="fruits">
                    <i class="fas fa-apple-alt"></i> فواكه - Fruits
                </button>
                <button class="category-tag" data-category="meat">
                    <i class="fas fa-drumstick-bite"></i> لحوم - Meat
                </button>
                <button class="category-tag" data-category="dairy">
                    <i class="fas fa-cheese"></i> ألبان - Dairy
                </button>
                <button class="category-tag" data-category="household">
                    <i class="fas fa-home"></i> منزلية - Household
                </button>
            </div>

            <div class="shopping-list" id="shoppingList"></div>

            <div class="print-section">
                <button onclick="window.print()" class="primary-button">
                    <i class="fas fa-print"></i>
                    <span class="ar">طباعة القائمة</span>
                    <span class="en">Print List</span>
                </button>
            </div>
        </div>
    </main>

    <script>
        let shoppingLists = {
            groceries: [],
            vegetables: [],
            fruits: [],
            meat: [],
            dairy: [],
            household: []
        };

        let currentCategory = 'groceries';

        const categoryIcons = {
            groceries: 'fas fa-shopping-basket',
            vegetables: 'fas fa-carrot',
            fruits: 'fas fa-apple-alt',
            meat: 'fas fa-drumstick-bite',
            dairy: 'fas fa-cheese',
            household: 'fas fa-home'
        };

        // Set up category selection
        document.querySelectorAll('.category-tag').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.category-tag').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentCategory = btn.dataset.category;
            });
        });

        function addItem() {
            const itemName = document.getElementById('itemName').value.trim();
            const quantity = parseInt(document.getElementById('itemQuantity').value);

            if (!itemName) {
                alert('الرجاء إدخال اسم العنصر - Please enter an item name');
                return;
            }

            if (isNaN(quantity) || quantity < 1) {
                alert('الرجاء إدخال كمية صحيحة - Please enter a valid quantity');
                return;
            }

            shoppingLists[currentCategory].push({
                name: itemName,
                quantity: quantity,
                completed: false
            });

            document.getElementById('itemName').value = '';
            document.getElementById('itemQuantity').value = '1';
            updateList();
        }

        function deleteItem(category, index) {
            shoppingLists[category].splice(index, 1);
            updateList();
        }

        function toggleItem(category, index) {
            shoppingLists[category][index].completed = !shoppingLists[category][index].completed;
            updateList();
        }

        function updateList() {
            const listContainer = document.getElementById('shoppingList');
            listContainer.innerHTML = '';

            Object.entries(shoppingLists).forEach(([category, items]) => {
                if (items.length === 0) return;

                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'list-category';
                
                const arabicCategories = {
                    groceries: 'بقالة',
                    vegetables: 'خضروات',
                    fruits: 'فواكه',
                    meat: 'لحوم',
                    dairy: 'ألبان',
                    household: 'منزلية'
                };

                categoryDiv.innerHTML = `
                    <div class="category-header">
                        <div>
                            <i class="${categoryIcons[category]}"></i>
                            ${arabicCategories[category]} - ${category.charAt(0).toUpperCase() + category.slice(1)}
                        </div>
                    </div>
                    <div class="items-list">
                        ${items.map((item, index) => `
                            <div class="list-item">
                                <div class="item-info">
                                    <input type="checkbox" ${item.completed ? 'checked' : ''} 
                                        onchange="toggleItem('${category}', ${index})">
                                    <span style="${item.completed ? 'text-decoration: line-through' : ''}">
                                        ${item.name} × ${item.quantity}
                                    </span>
                                </div>
                                <div class="item-actions">
                                    <button class="action-button delete" onclick="deleteItem('${category}', ${index})">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                listContainer.appendChild(categoryDiv);
            });

            // Save to localStorage
            localStorage.setItem('shoppingLists', JSON.stringify(shoppingLists));
        }

        // Load saved lists
        const savedLists = localStorage.getItem('shoppingLists');
        if (savedLists) {
            shoppingLists = JSON.parse(savedLists);
            updateList();
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>