<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستخرج الروابط - URL Extractor</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .extractor-container {
            display: grid;
            gap: 20px;
        }
        .input-section {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
        }
        .input-textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            resize: vertical;
            margin-bottom: 15px;
        }
        .options-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }
        .option-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .results-section {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
        }
        .url-list {
            margin-top: 15px;
        }
        .url-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
            margin-bottom: 10px;
        }
        .url-item:hover {
            background-color: var(--primary-color);
            color: white;
        }
        .url-text {
            flex-grow: 1;
            word-break: break-all;
        }
        .url-actions {
            display: flex;
            gap: 5px;
        }
        .url-action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: white;
        }
        .copy-btn {
            background-color: var(--primary-color);
        }
        .visit-btn {
            background-color: #28a745;
        }
        .export-options {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--secondary-color);
        }
        .export-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .export-btn {
            padding: 10px;
            background-color: var(--bg-color);
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s;
        }
        .export-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }
        .stats {
            margin-top: 10px;
            color: var(--text-color);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>مستخرج الروابط <span>URL Extractor</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="extractor-container">
            <div class="input-section">
                <textarea id="inputText" class="input-textarea" placeholder="Paste text containing URLs here..."></textarea>
                
                <div class="options-group">
                    <div class="option-item">
                        <input type="checkbox" id="validateUrls" checked>
                        <label for="validateUrls" class="ar">التحقق من صحة الروابط</label>
                        <label for="validateUrls" class="en">Validate URLs</label>
                    </div>
                    <div class="option-item">
                        <input type="checkbox" id="removeDuplicates" checked>
                        <label for="removeDuplicates" class="ar">إزالة التكرار</label>
                        <label for="removeDuplicates" class="en">Remove Duplicates</label>
                    </div>
                    <div class="option-item">
                        <input type="checkbox" id="sortUrls">
                        <label for="sortUrls" class="ar">ترتيب أبجدي</label>
                        <label for="sortUrls" class="en">Sort Alphabetically</label>
                    </div>
                </div>

                <div class="button-group">
                    <button id="extractBtn" class="ar">استخراج الروابط</button>
                    <button id="extractBtn" class="en">Extract URLs</button>
                </div>
            </div>

            <div class="results-section" style="display: none;">
                <div class="stats" id="statsDisplay"></div>
                <div class="url-list" id="urlList"></div>

                <div class="export-options">
                    <div class="ar">تصدير الروابط:</div>
                    <div class="en">Export URLs:</div>
                    <div class="export-grid">
                        <button class="export-btn" data-format="text">نص عادي / Plain Text</button>
                        <button class="export-btn" data-format="html">روابط HTML / HTML Links</button>
                        <button class="export-btn" data-format="markdown">روابط Markdown</button>
                        <button class="export-btn" data-format="json">ملف JSON</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            const extractBtn = document.getElementById('extractBtn');
            const validateUrls = document.getElementById('validateUrls');
            const removeDuplicates = document.getElementById('removeDuplicates');
            const sortUrls = document.getElementById('sortUrls');
            const resultsSection = document.querySelector('.results-section');
            const urlList = document.getElementById('urlList');
            const statsDisplay = document.getElementById('statsDisplay');
            const exportBtns = document.querySelectorAll('.export-btn');

            let extractedUrls = [];

            // URL validation regex
            const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi;

            // Load saved input
            inputText.value = localStorage.getItem('urlExtractor_input') || '';

            function validateUrl(url) {
                try {
                    new URL(url);
                    return true;
                } catch {
                    return false;
                }
            }

            function extractUrls() {
                const text = inputText.value;
                let urls = text.match(urlRegex) || [];

                if (validateUrls.checked) {
                    urls = urls.filter(url => validateUrl(url));
                }

                if (removeDuplicates.checked) {
                    urls = [...new Set(urls)];
                }

                if (sortUrls.checked) {
                    urls.sort();
                }

                extractedUrls = urls;
                displayResults();

                // Save input
                localStorage.setItem('urlExtractor_input', text);
            }

            function displayResults() {
                if (extractedUrls.length === 0) {
                    statsDisplay.textContent = document.documentElement.lang === 'ar' ?
                        'لم يتم العثور على روابط' :
                        'No URLs found';
                    urlList.innerHTML = '';
                    return;
                }

                statsDisplay.textContent = document.documentElement.lang === 'ar' ?
                    `تم العثور على ${extractedUrls.length} روابط` :
                    `Found ${extractedUrls.length} URLs`;

                urlList.innerHTML = extractedUrls.map(url => `
                    <div class="url-item">
                        <span class="url-text">${url}</span>
                        <div class="url-actions">
                            <button class="url-action-btn copy-btn" data-url="${url}">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="url-action-btn visit-btn" data-url="${url}">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        </div>
                    </div>
                `).join('');

                resultsSection.style.display = 'block';

                // Add click events
                document.querySelectorAll('.copy-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        window.utils.copyToClipboard(btn.dataset.url);
                    });
                });

                document.querySelectorAll('.visit-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        window.open(btn.dataset.url, '_blank');
                    });
                });
            }

            function exportUrls(format) {
                let content = '';
                switch (format) {
                    case 'text':
                        content = extractedUrls.join('\n');
                        break;
                    case 'html':
                        content = extractedUrls.map(url => 
                            `<a href="${url}">${url}</a>`).join('\n');
                        break;
                    case 'markdown':
                        content = extractedUrls.map(url => 
                            `[${url}](${url})`).join('\n');
                        break;
                    case 'json':
                        content = JSON.stringify(extractedUrls, null, 2);
                        break;
                }

                window.utils.copyToClipboard(content);
            }

            // Event listeners
            extractBtn.addEventListener('click', extractUrls);
            
            [validateUrls, removeDuplicates, sortUrls].forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    if (extractedUrls.length > 0) {
                        extractUrls();
                    }
                });
            });

            exportBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    exportUrls(btn.dataset.format);
                });
            });
        });
    </script>
</body>
</html>