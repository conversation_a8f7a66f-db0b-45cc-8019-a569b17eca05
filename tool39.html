<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول مارك داون - Markdown Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
    <style>
        .converter {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .workspace {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .workspace {
                grid-template-columns: 1fr;
            }
        }
        .editor, .preview {
            background-color: var(--secondary-bg);
            border-radius: 5px;
            padding: 15px;
        }
        .editor-header, .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        .preview-options {
            display: flex;
            gap: 10px;
        }
        .preview-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .preview-toggle.active {
            background-color: var(--primary-color);
            color: white;
        }
        #markdownInput {
            width: 100%;
            height: 500px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            resize: vertical;
            font-family: monospace;
            line-height: 1.6;
            direction: ltr;
        }
        #preview {
            height: 500px;
            overflow-y: auto;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            direction: ltr;
        }
        .markdown-guide {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
        }
        .guide-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .guide-item {
            background-color: var(--input-bg);
            padding: 10px;
            border-radius: 5px;
        }
        .guide-item pre {
            margin: 5px 0;
            padding: 5px;
            background-color: var(--secondary-bg);
            border-radius: 3px;
            overflow-x: auto;
        }
        .preview img {
            max-width: 100%;
            height: auto;
        }
        .preview table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .preview th, .preview td {
            border: 1px solid var(--border-color);
            padding: 8px;
            text-align: left;
        }
        .preview blockquote {
            border-left: 4px solid var(--primary-color);
            margin: 10px 0;
            padding-left: 15px;
            color: var(--text-color);
        }
        #preview.preview-code {
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <header>
        <h1>محول مارك داون <span>Markdown Converter</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="converter">
            <div class="workspace">
                <div class="editor">
                    <div class="editor-header">
                        <h3>مارك داون - Markdown</h3>
                        <button onclick="clearEditor()" class="preview-toggle">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <textarea id="markdownInput" oninput="convertMarkdown()" placeholder="اكتب مارك داون هنا...
Write your markdown here..."></textarea>
                </div>
                
                <div class="preview">
                    <div class="preview-header">
                        <h3>المعاينة - Preview</h3>
                        <div class="preview-options">
                            <button onclick="togglePreviewMode('rendered')" class="preview-toggle active" id="renderedBtn">
                                <i class="fas fa-eye"></i> عرض - Preview
                            </button>
                            <button onclick="togglePreviewMode('code')" class="preview-toggle" id="codeBtn">
                                <i class="fas fa-code"></i> كود - Code
                            </button>
                        </div>
                    </div>
                    <div id="preview"></div>
                </div>
            </div>

            <div class="markdown-guide">
                <h3>دليل مارك داون - Markdown Guide</h3>
                <div class="guide-grid">
                    <div class="guide-item">
                        <strong>العناوين - Headers</strong>
                        <pre># Heading 1
## Heading 2
### Heading 3</pre>
                    </div>
                    <div class="guide-item">
                        <strong>تنسيق النص - Text Formatting</strong>
                        <pre>**bold**
*italic*
~~strikethrough~~</pre>
                    </div>
                    <div class="guide-item">
                        <strong>القوائم - Lists</strong>
                        <pre>- Item 1
- Item 2
1. First
2. Second</pre>
                    </div>
                    <div class="guide-item">
                        <strong>الروابط - Links</strong>
                        <pre>[Link Text](URL)
![Image Alt](URL)</pre>
                    </div>
                    <div class="guide-item">
                        <strong>الإقتباسات - Quotes</strong>
                        <pre>> Blockquote text</pre>
                    </div>
                    <div class="guide-item">
                        <strong>الكود - Code</strong>
                        <pre>`inline code`
```
code block
```</pre>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentPreviewMode = 'rendered';
        
        marked.setOptions({
            breaks: true,
            gfm: true,
            highlight: function (code, lang) {
                if (Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            }
        });

        function convertMarkdown() {
            const markdown = document.getElementById('markdownInput').value;
            const preview = document.getElementById('preview');
            
            if (currentPreviewMode === 'rendered') {
                preview.innerHTML = marked.parse(markdown);
                preview.classList.remove('preview-code');
                Prism.highlightAll();
            } else {
                preview.textContent = marked.parse(markdown);
                preview.classList.add('preview-code');
            }
        }

        function togglePreviewMode(mode) {
            currentPreviewMode = mode;
            document.getElementById('renderedBtn').classList.toggle('active', mode === 'rendered');
            document.getElementById('codeBtn').classList.toggle('active', mode === 'code');
            convertMarkdown();
        }

        function clearEditor() {
            document.getElementById('markdownInput').value = '';
            convertMarkdown();
        }

        // Initial conversion
        convertMarkdown();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>