<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة نص على الصور - Text on Image</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .image-container {
            position: relative;
            max-width: 100%;
            margin: 20px auto;
            text-align: center;
        }
        #imageCanvas {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
        }
        .text-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group input,
        .control-group select {
            padding: 8px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid var(--secondary-color);
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }
        .drag-area {
            border: 2px dashed var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .drag-area:hover {
            border-color: var(--primary-color);
        }
        .drag-area i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <header>
        <h1>إضافة نص على الصور <span>Text on Image</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="drag-area" id="dragArea">
            <i class="fas fa-cloud-upload-alt"></i>
            <p class="ar">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
            <p class="en">Drag & drop image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="text-controls">
            <div class="control-group">
                <label for="textInput" class="ar">النص:</label>
                <label for="textInput" class="en">Text:</label>
                <input type="text" id="textInput" placeholder="Enter your text">
            </div>

            <div class="control-group">
                <label for="fontSize" class="ar">حجم الخط:</label>
                <label for="fontSize" class="en">Font Size:</label>
                <input type="number" id="fontSize" min="8" max="200" value="48">
            </div>

            <div class="control-group">
                <label for="fontFamily" class="ar">نوع الخط:</label>
                <label for="fontFamily" class="en">Font Family:</label>
                <select id="fontFamily">
                    <option value="Arial">Arial</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Impact">Impact</option>
                </select>
            </div>

            <div class="control-group">
                <label for="textColor" class="ar">لون الخط:</label>
                <label for="textColor" class="en">Text Color:</label>
                <input type="color" id="textColor" value="#ffffff">
                <div id="colorPreview" class="color-preview"></div>
            </div>

            <div class="control-group">
                <label for="strokeColor" class="ar">لون الحدود:</label>
                <label for="strokeColor" class="en">Stroke Color:</label>
                <input type="color" id="strokeColor" value="#000000">
            </div>

            <div class="control-group">
                <label for="strokeWidth" class="ar">سمك الحدود:</label>
                <label for="strokeWidth" class="en">Stroke Width:</label>
                <input type="number" id="strokeWidth" min="0" max="20" value="2">
            </div>
        </div>

        <div class="image-container">
            <canvas id="imageCanvas"></canvas>
        </div>

        <div class="button-group">
            <button id="resetBtn" class="ar" disabled>إعادة تعيين</button>
            <button id="resetBtn" class="en" disabled>Reset</button>
            
            <button id="downloadBtn" class="ar" disabled>تحميل</button>
            <button id="downloadBtn" class="en" disabled>Download</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const canvas = document.getElementById('imageCanvas');
            const ctx = canvas.getContext('2d');
            const resetBtn = document.getElementById('resetBtn');
            const downloadBtn = document.getElementById('downloadBtn');

            let originalImage = null;

            // Update color preview
            function updateColorPreview() {
                const preview = document.getElementById('colorPreview');
                preview.style.backgroundColor = document.getElementById('textColor').value;
            }

            // Handle file selection
            function handleFile(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = () => {
                            originalImage = img;
                            resetCanvas();
                            resetBtn.disabled = false;
                            downloadBtn.disabled = false;
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }

            function resetCanvas() {
                if (!originalImage) return;

                const maxWidth = 800;
                const scale = Math.min(1, maxWidth / originalImage.width);
                canvas.width = originalImage.width * scale;
                canvas.height = originalImage.height * scale;

                ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);
                drawText();
            }

            function drawText() {
                const text = document.getElementById('textInput').value;
                const fontSize = document.getElementById('fontSize').value;
                const fontFamily = document.getElementById('fontFamily').value;
                const textColor = document.getElementById('textColor').value;
                const strokeColor = document.getElementById('strokeColor').value;
                const strokeWidth = document.getElementById('strokeWidth').value;

                ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);
                
                ctx.font = `${fontSize}px ${fontFamily}`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                const x = canvas.width / 2;
                const y = canvas.height / 2;

                // Draw stroke
                if (strokeWidth > 0) {
                    ctx.strokeStyle = strokeColor;
                    ctx.lineWidth = strokeWidth;
                    ctx.strokeText(text, x, y);
                }

                // Draw text
                ctx.fillStyle = textColor;
                ctx.fillText(text, x, y);
            }

            // Event listeners
            document.getElementById('textInput').addEventListener('input', drawText);
            document.getElementById('fontSize').addEventListener('input', drawText);
            document.getElementById('fontFamily').addEventListener('change', drawText);
            document.getElementById('textColor').addEventListener('input', () => {
                updateColorPreview();
                drawText();
            });
            document.getElementById('strokeColor').addEventListener('input', drawText);
            document.getElementById('strokeWidth').addEventListener('input', drawText);

            resetBtn.addEventListener('click', resetCanvas);

            downloadBtn.addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = 'image-with-text.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });

            // File input and drag & drop handlers
            fileInput.addEventListener('change', (e) => {
                handleFile(e.target.files[0]);
            });

            dragArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--primary-color)';
            });

            dragArea.addEventListener('dragleave', () => {
                dragArea.style.borderColor = 'var(--secondary-color)';
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--secondary-color)';
                handleFile(e.dataTransfer.files[0]);
            });

            dragArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Initialize color preview
            updateColorPreview();
        });
    </script>
</body>
</html>