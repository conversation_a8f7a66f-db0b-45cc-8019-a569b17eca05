<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أسماء الأطفال - Baby Names Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .generator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group select, .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .names-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .name-card {
            padding: 15px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            transition: transform 0.2s;
        }
        .name-card:hover {
            transform: translateY(-3px);
        }
        .name-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .name-card p {
            color: var(--text-color);
            font-size: 0.9em;
            margin: 0;
        }
        .gender-male {
            border-right: 4px solid #4a90e2;
        }
        .gender-female {
            border-right: 4px solid #e24a84;
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد أسماء الأطفال <span>Baby Names Generator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="generator">
            <div class="input-panel">
                <div class="input-group">
                    <label for="gender">الجنس - Gender</label>
                    <select id="gender" onchange="generateNames()">
                        <option value="male">ذكر - Male</option>
                        <option value="female">أنثى - Female</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="firstLetter">الحرف الأول - First Letter</label>
                    <input type="text" id="firstLetter" maxlength="1" onkeyup="generateNames()" placeholder="اختياري - Optional">
                </div>
            </div>

            <button onclick="generateNames()" class="primary-button">
                <i class="fas fa-magic"></i>
                <span class="ar">ولّد الأسماء</span>
                <span class="en">Generate Names</span>
            </button>

            <div class="names-list" id="namesList"></div>
        </div>
    </main>

    <script>
        const names = {
            male: {
                'أ': [
                    { name: 'أحمد', meaning: 'الأكثر حمداً وشكراً', enName: 'Ahmad' },
                    { name: 'أمير', meaning: 'قائد، حاكم', enName: 'Amir' },
                    { name: 'أنس', meaning: 'الصداقة والألفة', enName: 'Anas' }
                ],
                'ع': [
                    { name: 'عمر', meaning: 'الحياة والعمران', enName: 'Omar' },
                    { name: 'علي', meaning: 'المرتفع، السامي', enName: 'Ali' },
                    { name: 'عبدالله', meaning: 'عبد الله', enName: 'Abdullah' }
                ],
                'م': [
                    { name: 'محمد', meaning: 'المحمود، كثير الخصال الحميدة', enName: 'Muhammad' },
                    { name: 'مالك', meaning: 'صاحب الملك', enName: 'Malik' },
                    { name: 'مازن', meaning: 'السحاب الأبيض', enName: 'Mazen' }
                ]
            },
            female: {
                'أ': [
                    { name: 'آلاء', meaning: 'النعم والعطايا', enName: 'Alaa' },
                    { name: 'أميرة', meaning: 'الأميرة، القائدة', enName: 'Amira' },
                    { name: 'أسماء', meaning: 'العلو والرفعة', enName: 'Asmaa' }
                ],
                'س': [
                    { name: 'سارة', meaning: 'السعيدة، الفرحة', enName: 'Sarah' },
                    { name: 'سلمى', meaning: 'السلامة والعافية', enName: 'Salma' },
                    { name: 'سمية', meaning: 'المرتفعة، العالية', enName: 'Sumaya' }
                ],
                'ن': [
                    { name: 'نور', meaning: 'الضوء والضياء', enName: 'Noor' },
                    { name: 'نادية', meaning: 'النداء، البداية', enName: 'Nadia' },
                    { name: 'نوال', meaning: 'العطاء والهبة', enName: 'Nawal' }
                ]
            }
        };

        function generateNames() {
            const gender = document.getElementById('gender').value;
            const firstLetter = document.getElementById('firstLetter').value.trim();
            const namesList = document.getElementById('namesList');
            namesList.innerHTML = '';

            let selectedNames = [];
            if (firstLetter) {
                // Get names starting with the specified letter
                const normalizedLetter = firstLetter.replace(/[آأإ]/, 'ا');
                selectedNames = Object.values(names[gender])
                    .flat()
                    .filter(n => n.name.startsWith(firstLetter) || n.name.startsWith(normalizedLetter));
            } else {
                // Get all names for the selected gender
                selectedNames = Object.values(names[gender]).flat();
            }

            // Shuffle the names array
            selectedNames = selectedNames.sort(() => Math.random() - 0.5);

            selectedNames.forEach(name => {
                const nameCard = document.createElement('div');
                nameCard.className = `name-card gender-${gender}`;
                nameCard.innerHTML = `
                    <h3>${name.name} (${name.enName})</h3>
                    <p>${name.meaning}</p>
                `;
                namesList.appendChild(nameCard);
            });

            if (selectedNames.length === 0) {
                namesList.innerHTML = `
                    <div class="name-card">
                        <p class="ar">لم يتم العثور على أسماء تبدأ بهذا الحرف</p>
                        <p class="en">No names found starting with this letter</p>
                    </div>
                `;
            }
        }

        // Generate initial names
        generateNames();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>