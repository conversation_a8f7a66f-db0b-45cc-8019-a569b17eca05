<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد كلمات المرور - Password Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .password-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .password-display {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: 4px;
            text-align: center;
            font-family: monospace;
            font-size: 1.2em;
        }
        .copy-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 1.2em;
        }
        .options {
            margin-bottom: 20px;
        }
        .option-group {
            margin-bottom: 15px;
        }
        .option-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-color);
        }
        .option-group input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        .range-value {
            text-align: center;
            font-size: 1.2em;
            color: var(--primary-color);
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .checkbox-group input[type="checkbox"] {
            margin-left: 10px;
        }
        .strength-meter {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .weak { background-color: #ffcdd2; color: #c62828; }
        .medium { background-color: #fff9c4; color: #f57f17; }
        .strong { background-color: #c8e6c9; color: #2e7d32; }
    </style>
</head>
<body>
    <header>
        <h1>مولد كلمات المرور <span>Password Generator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main>
        <div class="password-container">
            <div class="password-display" id="passwordDisplay">
                كلمة المرور ستظهر هنا
                <button class="copy-btn" onclick="copyPassword()" title="نسخ">
                    <i class="fas fa-copy"></i>
                </button>
            </div>

            <div class="options">
                <div class="option-group">
                    <label>طول كلمة المرور (Password Length):</label>
                    <input type="range" id="lengthSlider" min="8" max="32" value="16">
                    <div class="range-value" id="lengthValue">16</div>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="uppercase" checked>
                    <label>حروف كبيرة (Uppercase Letters)</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="lowercase" checked>
                    <label>حروف صغيرة (Lowercase Letters)</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="numbers" checked>
                    <label>أرقام (Numbers)</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="symbols" checked>
                    <label>رموز خاصة (Special Characters)</label>
                </div>
            </div>

            <button onclick="generatePassword()" class="primary-button">
                توليد كلمة مرور (Generate Password)
            </button>

            <div class="strength-meter" id="strengthMeter"></div>
        </div>
    </main>

    <script>
        function generatePassword() {
            const length = document.getElementById('lengthSlider').value;
            const hasUpper = document.getElementById('uppercase').checked;
            const hasLower = document.getElementById('lowercase').checked;
            const hasNumbers = document.getElementById('numbers').checked;
            const hasSymbols = document.getElementById('symbols').checked;

            const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const lower = 'abcdefghijklmnopqrstuvwxyz';
            const numbers = '0123456789';
            const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

            let chars = '';
            if (hasUpper) chars += upper;
            if (hasLower) chars += lower;
            if (hasNumbers) chars += numbers;
            if (hasSymbols) chars += symbols;

            if (!chars) {
                alert('الرجاء اختيار نوع واحد على الأقل (Please select at least one character type)');
                return;
            }

            let password = '';
            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * chars.length);
                password += chars[randomIndex];
            }

            document.getElementById('passwordDisplay').textContent = password;
            updateStrengthMeter(password);
        }

        function copyPassword() {
            const password = document.getElementById('passwordDisplay').textContent;
            if (password && password !== 'كلمة المرور ستظهر هنا') {
                navigator.clipboard.writeText(password).then(() => {
                    alert('تم نسخ كلمة المرور! (Password copied!)');
                });
            }
        }

        function updateStrengthMeter(password) {
            const strength = calculatePasswordStrength(password);
            const meter = document.getElementById('strengthMeter');
            
            if (strength < 3) {
                meter.className = 'strength-meter weak';
                meter.textContent = 'ضعيف (Weak)';
            } else if (strength < 4) {
                meter.className = 'strength-meter medium';
                meter.textContent = 'متوسط (Medium)';
            } else {
                meter.className = 'strength-meter strong';
                meter.textContent = 'قوي (Strong)';
            }
        }

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 12) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^A-Za-z0-9]/)) strength++;
            return strength;
        }

        // Length slider update
        const lengthSlider = document.getElementById('lengthSlider');
        const lengthValue = document.getElementById('lengthValue');
        lengthSlider.addEventListener('input', () => {
            lengthValue.textContent = lengthSlider.value;
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
        });

        // Language toggle
        const toggleLang = document.getElementById('toggleLang');
        toggleLang.addEventListener('click', () => {
            document.documentElement.dir = document.documentElement.dir === 'rtl' ? 'ltr' : 'rtl';
            document.documentElement.lang = document.documentElement.lang === 'ar' ? 'en' : 'ar';
        });

        // Generate initial password
        generatePassword();
    </script>
</body>
</html>