<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالج الفيديوهات بالذكاء الصناعي</title>
    <!-- روابط Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .upload-section {
            margin-top: 50px;
        }
        .result-section {
            margin-top: 50px;
        }
        .video-thumbnail {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
    </style>
</head>
<body>

    <!-- شريط العنوان -->
    <div class="header">
        <h1>معالج الفيديوهات بالذكاء الصناعي</h1>
        <p>قم برفع فيديو أو أدخل رابطًا واحصل على مقاطع قصيرة احترافية!</p>
    </div>

    <!-- قسم تحميل الفيديو أو إدخال الرابط -->
    <div class="container upload-section">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختر طريقة المعالجة</h3>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm">
                            <div class="mb-3">
                                <label for="videoFile" class="form-label">رفع الفيديو:</label>
                                <input type="file" class="form-control" id="videoFile" accept="video/*">
                            </div>
                            <div class="mb-3">
                                <label for="videoUrl" class="form-label">أو أدخل رابط الفيديو:</label>
                                <input type="url" class="form-control" id="videoUrl" placeholder="مثال: https://www.youtube.com/watch?v=...">
                            </div>
                            <button type="submit" class="btn btn-primary btn-lg w-100">معالجه الفيديو</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم عرض النتائج -->
    <div class="container result-section" id="resultSection" style="display: none;">
        <h2>النتائج</h2>
        <div class="row" id="videoResults"></div>
    </div>

    <!-- روابط JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(event) {
            event.preventDefault();

            const fileInput = document.getElementById('videoFile');
            const urlInput = document.getElementById('videoUrl');
            let videoSource;

            if (fileInput.files.length > 0) {
                // تم اختيار ملف فيديو
                videoSource = fileInput.files[0];
            } else if (urlInput.value.trim() !== '') {
                // تم إدخال رابط فيديو
                videoSource = urlInput.value.trim();
            } else {
                alert('يرجى رفع ملف فيديو أو إدخال رابط صالح.');
                return;
            }

            // إظهار قسم النتائج
            document.getElementById('resultSection').style.display = 'block';

            // هنا يمكننا إضافة منطق معالجة الفيديو (سيتم تنفيذه في الخلفية)
            alert('تم استلام المدخلات بنجاح! سيتم معالجته قريباً.');
        });
    </script>

</body>
</html>