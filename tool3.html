<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد نصوص عشوائية - Random Text Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>مولد نصوص عشوائية <span>Random Text Generator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="paragraphCount" class="ar">عدد الفقرات:</label>
            <label for="paragraphCount" class="en">Number of Paragraphs:</label>
            <input type="number" id="paragraphCount" min="1" max="10" value="1">
            
            <label for="wordsPerParagraph" class="ar">الكلمات في كل فقرة:</label>
            <label for="wordsPerParagraph" class="en">Words per Paragraph:</label>
            <input type="number" id="wordsPerParagraph" min="10" max="100" value="50">
        </div>

        <div class="button-group">
            <button id="generateBtn" class="ar">توليد النص</button>
            <button id="generateBtn" class="en">Generate Text</button>
        </div>

        <div class="output-group">
            <label for="outputText" class="ar">النص العشوائي:</label>
            <label for="outputText" class="en">Random Text:</label>
            <textarea id="outputText" rows="10" readonly></textarea>
            <button id="copyBtn" class="ar">نسخ النص</button>
            <button id="copyBtn" class="en">Copy Text</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const paragraphCount = document.getElementById('paragraphCount');
            const wordsPerParagraph = document.getElementById('wordsPerParagraph');
            const outputText = document.getElementById('outputText');
            
            // Load saved settings if they exist
            paragraphCount.value = localStorage.getItem('randomText_paragraphCount') || 1;
            wordsPerParagraph.value = localStorage.getItem('randomText_wordsPerParagraph') || 50;

            // Words bank for generating random text
            const words = [
                'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
                'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore',
                'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud', 'exercitation',
                'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo', 'consequat'
            ];

            // Generate random text
            function generateRandomText() {
                const numParagraphs = parseInt(paragraphCount.value);
                const numWords = parseInt(wordsPerParagraph.value);
                let result = '';

                for (let i = 0; i < numParagraphs; i++) {
                    let paragraph = '';
                    for (let j = 0; j < numWords; j++) {
                        const randomWord = words[Math.floor(Math.random() * words.length)];
                        paragraph += randomWord + ' ';
                    }
                    result += paragraph.trim() + '\n\n';
                }

                outputText.value = result.trim();

                // Save settings
                localStorage.setItem('randomText_paragraphCount', paragraphCount.value);
                localStorage.setItem('randomText_wordsPerParagraph', wordsPerParagraph.value);
            }

            // Event listeners
            document.getElementById('generateBtn').addEventListener('click', generateRandomText);
            
            document.getElementById('copyBtn').addEventListener('click', () => {
                if (outputText.value) {
                    window.utils.copyToClipboard(outputText.value);
                }
            });

            // Generate initial text
            generateRandomText();
        });
    </script>
</body>
</html>