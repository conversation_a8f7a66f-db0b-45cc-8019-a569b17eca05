<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل مواقع Semrush</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .site-list {
            margin: 20px 0;
        }
        .site-item {
            margin: 10px 0;
            word-break: break-all;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-bottom: 10px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>تحليل مواقع Semrush</h1>
    
    <label for="sitesInput">أدخل المواقع (كل موقع في سطر):</label>
    <textarea id="sitesInput" placeholder="مثال:&#10;example.com&#10;test.com"></textarea>
    <button onclick="generateLinks()">إنشاء الروابط</button>
    
    <div class="site-list" id="siteList"></div>
    <button onclick="openAllLinks()" id="openAllBtn" style="display: none;">فتح جميع الروابط</button>

    <script>
        const baseUrl = "https://www.semrush.com/analytics/overview/?searchType=subdomain&q=";
        let websites = [];

        // إنشاء الروابط من المدخلات
        function generateLinks() {
            const input = document.getElementById("sitesInput").value.trim();
            websites = input.split("\n").map(site => site.trim()).filter(site => site !== "");
            displayLinks();
            document.getElementById("openAllBtn").style.display = "block";
        }

        // عرض الروابط
        function displayLinks() {
            const siteList = document.getElementById("siteList");
            siteList.innerHTML = "";
            
            websites.forEach(site => {
                const url = baseUrl + site;
                const div = document.createElement("div");
                div.className = "site-item";
                div.innerHTML = `<a href="${url}" target="_blank">${url}</a>`;
                siteList.appendChild(div);
            });
        }

        // فتح جميع الروابط
        function openAllLinks() {
            websites.forEach(site => {
                const url = baseUrl + site;
                window.open(url, '_blank');
            });
        }
    </script>
</body>
</html>