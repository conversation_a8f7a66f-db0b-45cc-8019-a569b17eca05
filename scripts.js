// Initialize dark mode from localStorage or prefer-color-scheme
function initDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const savedMode = localStorage.getItem('darkMode');

    if (savedMode === 'enabled' || (savedMode === null && prefersDark)) {
        document.body.classList.add('dark-mode');
        darkModeToggle.checked = true;
    }

    darkModeToggle.addEventListener('change', toggleDarkMode);
}

// Toggle dark mode and save preference
function toggleDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle.checked) {
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'enabled');
    } else {
        document.body.classList.remove('dark-mode');
        localStorage.setItem('darkMode', 'disabled');
    }
}

// Initialize language from localStorage or browser language
function initLanguage() {
    const toggleBtn = document.getElementById('toggleLang');
    const savedLang = localStorage.getItem('lang');
    const browserLang = navigator.language.startsWith('ar') ? 'ar' : 'en';

    if (savedLang) {
        setLanguage(savedLang);
    } else if (browserLang === 'ar') {
        setLanguage('ar');
    }

    toggleBtn.addEventListener('click', toggleLanguage);
}

// Toggle between Arabic and English
function toggleLanguage() {
    const currentLang = document.documentElement.lang;
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    setLanguage(newLang);
    localStorage.setItem('lang', newLang);
}

// Set language and direction
function setLanguage(lang) {
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.getElementById('toggleLang').textContent = 
        lang === 'ar' ? 'English/العربية' : 'العربية/English';
}

// Copy text to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('Copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
    });
}

// Handle header scroll effect
function initHeaderScroll() {
    const header = document.querySelector('header');
    const scrollThreshold = 50;

    function handleScroll() {
        if (window.scrollY > scrollThreshold) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initialize on load
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initDarkMode();
    initLanguage();
    initHeaderScroll();
});

// Common utility functions
function getSavedInput(toolId) {
    return localStorage.getItem(`tool_${toolId}_input`) || '';
}

function saveInput(toolId, input) {
    localStorage.setItem(`tool_${toolId}_input`, input);
}

// Initialize tabs functionality
function initTabs() {
    const tabsContainer = document.querySelector('.tabs-container');
    const tabButtons = document.querySelectorAll('.tab-btn');
    const toolCategories = document.querySelectorAll('.tool-category');
    const leftArrow = document.getElementById('tabScrollLeft');
    const rightArrow = document.getElementById('tabScrollRight');
    const scrollAmount = 200;

    // Tab click handling
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const category = button.dataset.category;
            
            // Update active states
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Show selected category
            if (category === 'all') {
                // إظهار جميع الفئات
                toolCategories.forEach(cat => {
                    cat.classList.add('active');
                });
            } else {
                // إظهار فئة محددة فقط
                toolCategories.forEach(cat => {
                    if (cat.dataset.category === category) {
                        cat.classList.add('active');
                    } else {
                        cat.classList.remove('active');
                    }
                });
            }

            // Scroll button into view if needed
            button.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        });
    });

    // Scroll arrows functionality
    if (leftArrow && rightArrow) {
        leftArrow.addEventListener('click', () => {
            const isRTL = document.documentElement.dir === 'rtl';
            tabsContainer.scrollBy({
                left: (isRTL ? 1 : -1) * scrollAmount,
                behavior: 'smooth'
            });
        });

        rightArrow.addEventListener('click', () => {
            const isRTL = document.documentElement.dir === 'rtl';
            tabsContainer.scrollBy({
                left: (isRTL ? -1 : 1) * scrollAmount,
                behavior: 'smooth'
            });
        });

        // Show/hide arrows based on scroll position
        const updateArrows = () => {
            const isRTL = document.documentElement.dir === 'rtl';
            const { scrollLeft, scrollWidth, clientWidth } = tabsContainer;
            const maxScroll = scrollWidth - clientWidth;

            if (isRTL) {
                leftArrow.style.opacity = scrollLeft > -maxScroll ? '1' : '0.3';
                rightArrow.style.opacity = scrollLeft < 0 ? '1' : '0.3';
            } else {
                leftArrow.style.opacity = scrollLeft > 0 ? '1' : '0.3';
                rightArrow.style.opacity = scrollLeft < maxScroll ? '1' : '0.3';
            }
        };

        tabsContainer.addEventListener('scroll', updateArrows);
        window.addEventListener('resize', updateArrows);
        updateArrows();
    }
}

// Tool search functionality
function initToolSearch() {
    const searchInput = document.getElementById('toolSearch');
    const searchInputEn = document.getElementById('toolSearchEn');
    const searchResults = document.getElementById('searchResults');
    const toolCards = Array.from(document.querySelectorAll('.tool-card'));
    let searchTimeout;

    const searchBackdrop = document.getElementById('searchBackdrop');
    
    function performSearch(input, language) {
        clearTimeout(searchTimeout);
        
        searchTimeout = setTimeout(() => {
            const query = input.value.toLowerCase().trim();
            
            if (query.length < 2) {
                hideSearchResults();
                return;
            }

            const matchingTools = toolCards.filter(card => {
                const titleEl = language === 'ar' ?
                    card.querySelector('h3') :
                    card.querySelector('.en');
                const title = titleEl ? titleEl.textContent.toLowerCase() : '';
                const description = card.querySelector('p') ?
                    card.querySelector('p').textContent.toLowerCase() : '';
                
                return title.includes(query) || description.includes(query);
            });

            displaySearchResults(matchingTools);
        }, 150);
    }

    function displaySearchResults(tools) {
        const lang = document.documentElement.lang;
        
        if (tools.length === 0) {
            searchResults.innerHTML = `
                <div class="search-message">
                    <p class="ar" ${lang === 'en' ? 'style="display:none"' : ''}>لا توجد نتائج</p>
                    <p class="en" ${lang === 'ar' ? 'style="display:none"' : ''}>No results found</p>
                </div>
            `;
        } else {
            searchResults.innerHTML = tools.map(tool => {
                const titleAr = tool.querySelector('h3').textContent;
                const titleEn = tool.querySelector('.en').textContent;
                const description = tool.querySelector('p').textContent;
                const href = tool.getAttribute('onclick').match(/'([^']+)'/)[1];
                
                
                return `
                    <div class="search-result" onclick="location.href='${href}'">
                        <h4>
                            <span class="ar">${titleAr}</span>
                            <span class="en">${titleEn}</span>
                        </h4>
                        <p>${description}</p>
                    </div>
                `;
            }).join('');
        }

        showSearchResults();
    }

    function showSearchResults() {
        searchResults.classList.add('active');
        if (searchBackdrop) {
            searchBackdrop.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function hideSearchResults() {
        searchResults.classList.remove('active');
        if (searchBackdrop) {
            searchBackdrop.classList.remove('active');
            document.body.style.overflow = '';
        }
        if (searchInput) searchInput.value = '';
        if (searchInputEn) searchInputEn.value = '';
    }

    // Event listeners for search inputs
    searchInput.addEventListener('input', () => performSearch(searchInput, 'ar'));
    searchInputEn.addEventListener('input', () => performSearch(searchInputEn, 'en'));
    
    // Hide results when clicking outside
    searchBackdrop.addEventListener('click', hideSearchResults);

    // Hide results when clicking outside
    document.addEventListener('click', (e) => {
        if (!searchResults.contains(e.target) &&
            e.target !== searchInput &&
            e.target !== searchInputEn) {
            searchResults.classList.remove('active');
        }
    });
}

// Export functions for tool pages to use
window.utils = {
    copyToClipboard,
    getSavedInput,
    saveInput
};

// فتح الأداة في تبويب جديد
function openInNewTab(url) {
    window.open(url, '_blank');
}

// فتح جميع الأدوات
function openAllTools() {
    const toolCards = document.querySelectorAll('.tool-card');
    const toolUrls = [];

    toolCards.forEach(card => {
        const onclick = card.getAttribute('onclick');
        if (onclick) {
            const match = onclick.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
            if (match) {
                toolUrls.push(match[1]);
            }
        }
    });

    // تأكيد من المستخدم
    const currentLang = document.documentElement.lang;
    const confirmMessage = currentLang === 'ar'
        ? `هل تريد فتح ${toolUrls.length} أداة في تبويبات منفصلة؟`
        : `Do you want to open ${toolUrls.length} tools in separate tabs?`;

    if (confirm(confirmMessage)) {
        toolUrls.forEach((url, index) => {
            setTimeout(() => {
                window.open(url, '_blank');
            }, index * 100); // تأخير صغير لتجنب حظر النوافذ المنبثقة
        });
    }
}

// تبديل عرض الشبكة/القائمة
function toggleView() {
    const toolsGrid = document.querySelector('.tools-grid');
    const toggleBtn = document.getElementById('toggleView');

    if (!toolsGrid || !toggleBtn) return;

    const icon = toggleBtn.querySelector('i');
    const arText = toggleBtn.querySelector('.ar');
    const enText = toggleBtn.querySelector('.en');

    if (toolsGrid.classList.contains('list-view')) {
        toolsGrid.classList.remove('list-view');
        icon.className = 'fas fa-th-large';
        arText.textContent = 'عرض الشبكة';
        enText.textContent = 'Grid View';
        localStorage.setItem('viewMode', 'grid');
    } else {
        toolsGrid.classList.add('list-view');
        icon.className = 'fas fa-list';
        arText.textContent = 'عرض القائمة';
        enText.textContent = 'List View';
        localStorage.setItem('viewMode', 'list');
    }
}

// استعادة وضع العرض المحفوظ
function initViewMode() {
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'list') {
        toggleView();
    }
}

// تحسين بطاقات الأدوات لإضافة أيقونة الفتح في تبويب جديد
function enhanceToolCards() {
    const toolCards = document.querySelectorAll('.tool-card');

    toolCards.forEach(card => {
        // التحقق من وجود الهيكل الجديد
        if (!card.querySelector('.tool-card-header')) {
            const cardContent = card.innerHTML;
            const onclick = card.getAttribute('onclick');
            let url = '';

            if (onclick) {
                // البحث عن الرابط في onclick
                const locationMatch = onclick.match(/location\.href\s*=\s*['"]([^'"]+)['"]/);
                const windowMatch = onclick.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);

                if (locationMatch) {
                    url = locationMatch[1];
                } else if (windowMatch) {
                    url = windowMatch[1];
                }
            }

            if (url) {
                card.innerHTML = `
                    <div class="tool-card-header">
                        <div></div>
                        <button class="tool-external-link" onclick="event.stopPropagation(); openInNewTab('${url}')" title="فتح في تبويب جديد">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                    <div class="tool-card-content">
                        ${cardContent}
                    </div>
                `;
            }
        }
    });
}



// التمرير للأعلى
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// فتح أداة عشوائية
function openRandomTool() {
    const toolCards = document.querySelectorAll('.tool-card');
    if (toolCards.length > 0) {
        const randomIndex = Math.floor(Math.random() * toolCards.length);
        const randomCard = toolCards[randomIndex];
        const onclick = randomCard.getAttribute('onclick');

        if (onclick) {
            const match = onclick.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
            if (match) {
                // إضافة تأثير بصري للبطاقة المختارة
                randomCard.style.transform = 'scale(1.1)';
                randomCard.style.boxShadow = '0 20px 40px rgba(99, 102, 241, 0.3)';

                setTimeout(() => {
                    window.location.href = match[1];
                }, 500);
            }
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initDarkMode();
    initLanguage();
    initHeaderScroll();
    initToolSearch();
    initTabs();
    initViewMode();
    enhanceToolCards();

    // إضافة مستمعي الأحداث للأزرار الجديدة
    const openAllBtn = document.getElementById('openAllTools');
    const toggleViewBtn = document.getElementById('toggleView');

    if (openAllBtn) {
        openAllBtn.addEventListener('click', openAllTools);
    }

    if (toggleViewBtn) {
        toggleViewBtn.addEventListener('click', toggleView);
    }
});