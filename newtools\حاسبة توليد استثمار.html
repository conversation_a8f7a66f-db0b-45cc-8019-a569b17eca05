<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توزيع الاستثمارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>

<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">توزيع الاستثمارات</h1>
        <div class="mb-4">
            <label for="totalInvestment" class="form-label">إجمالي المبلغ للاستثمار (بالجنيه):</label>
            <input type="number" id="totalInvestment" class="form-control" value="100000">
            <button id="calculateButton"  class="btn btn-primary mt-3" onclick="calculateInvestment(); updateAllGroups();">حساب التوزيع</button>
            <!-- <button class="btn btn-primary mt-3" onclick="calculateInvestment()">حساب التوزيع</button> -->		
			<!-- <button id="calculateButton" class="btn btn-warning mt-3" onclick="updateAllGroups()">حساب الاجماليات</button> -->
        </div>

        <!-- صناديق الدخل الثابت -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="fas fa-university"></i> صناديق الدخل الثابت
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الصندوق</th>
                            <th>نسبة الأرباح (%)</th>
                            <th>المبلغ المخصص</th>
                            <th>الأرباح المتوقعة</th>
                        </tr>
                    </thead>
                    <tbody id="fixedIncomeFunds">
                        <tr>
                            <td>صندوق ازيموت ناصر</td>
                            <td><input type="number" class="form-control profit-ratio" value="21"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                        <tr>
                            <td>صندوق az ادخار</td>
                            <td><input type="number" class="form-control profit-ratio" value="15"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                        <tr>
                            <td>بي سيكيور</td>
                            <td><input type="number" class="form-control profit-ratio" value="19"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                        <tr>
                            <td>صندوق مصر للتأمين التكافلي</td>
                            <td><input type="number" class="form-control profit-ratio" value="22"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                    </tbody>
                </table>
				    <div class="row">
        <div class="col-md-6">
            <strong>إجمالي المبلغ لصناديق الدخل الثابت:</strong> <span id="totalfixedIncomeFundsAmount">0</span>
        </div>
        <div class="col-md-6">
            <strong>نسبة الربح النهائية:</strong> <span id="totalfixedIncomeFundsProfit">0</span>%
        </div>
    </div>
            </div>
        </div>

        <!-- صناديق الذهب -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-coins"></i> صناديق الذهب
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الصندوق</th>
                            <th>نسبة الأرباح (%)</th>
                            <th>المبلغ المخصص</th>
                            <th>الأرباح المتوقعة</th>
                        </tr>
                    </thead>
                    <tbody id="goldFunds">
                        <tr>
                            <td>جولد azimut</td>
                            <td><input type="number" class="form-control profit-ratio" value="29"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                        <tr>
                            <td>صندوق بلتون سبائك</td>
                            <td><input type="number" class="form-control profit-ratio" value="5"></td>
                            <td class="allocated-amount">0</td>
                            <td class="expected-profit">0</td>
                        </tr>
                    </tbody>
                </table>
				    <div class="row">
        <div class="col-md-6">
            <strong>إجمالي المبلغ لصناديق الذهب:</strong> <span id="totalgoldFundsAmount">0</span>
        </div>
        <div class="col-md-6">
            <strong>نسبة الربح النهائية:</strong> <span id="totalgoldFundsProfit">0</span>%
        </div>
    </div>
            </div>
        </div>

        <!-- صناديق الأسهم -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-line"></i> صناديق الأسهم
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الصندوق</th>
                            <th>نسبة الأرباح (%)</th>
                            <th>المبلغ المخصص</th>
                            <th>الأرباح المتوقعة</th>
                        </tr>
                    </thead>
<tbody id="stockFunds">
    <tr>
        <td>صندوق ازيموت فرص الشريعة</td>
        <td><input type="number" class="form-control profit-ratio" value="20"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>صندوق az فرص</td>
        <td><input type="number" class="form-control profit-ratio" value="39"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>بلتون مية مية</td>
        <td><input type="number" class="form-control profit-ratio" value="20"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>بلتون وفرة</td>
        <td><input type="number" class="form-control profit-ratio" value="20"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>صندوق مصر اكويتي</td>
        <td><input type="number" class="form-control profit-ratio" value="23"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>صندوق مصر شريعة اكويتي</td>
        <td><input type="number" class="form-control profit-ratio" value="20"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
    <tr>
        <td>صندوق mm أسهم شريعة</td>
        <td><input type="number" class="form-control profit-ratio" value="13"></td>
        <td class="allocated-amount">0</td>
        <td class="expected-profit">0</td>
    </tr>
</tbody>

                </table>
				  <div class="row">
        <div class="col-md-6">
            <strong>إجمالي المبلغ لصناديق الأسهم:</strong> <span id="totalstockFundsAmount">0</span>
        </div>
        <div class="col-md-6">
            <strong>نسبة الربح النهائية:</strong> <span id="totalstockFundsProfit">0</span>%
        </div>
    </div>
            </div>
        </div>

        <!-- استثمار العقار -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <i class="fas fa-home"></i> استثمار العقار/ذهب ملموس/اسهم شركات في البورصة
            </div>
            <div class="card-body">
                <p>يخصص للاستثمار العقاري: <span id="realEstateAllocation">0</span> جنيه</p>
                <p>الأرباح المتوقعة من العقار: <span id="realEstateProfit">0</span> جنيه</p>
            </div>
        </div>

        <!-- إجمالي الأرباح -->
        <div class="alert alert-success text-center">
            <h4><strong>إجمالي الأرباح المتوقعة:</strong> <span id="totalExpectedProfit">0</span> جنيه</h4>
        </div>
    </div>

<script>
function updateAllGroups() {
    // حساب الإجماليات لكل صندوق
    calculateGroupTotals('fixedIncomeFunds', 'totalfixedIncomeFundsAmount', 'totalfixedIncomeFundsProfit');
    calculateGroupTotals('goldFunds', 'totalgoldFundsAmount', 'totalgoldFundsProfit');
    calculateGroupTotals('stockFunds', 'totalstockFundsAmount', 'totalstockFundsProfit');
}

function calculateGroupTotals(tableId, totalAmountId, totalProfitId) {
    const rows = document.getElementById(tableId).querySelectorAll('tr');
    let totalAmount = 0;
    let weightedProfit = 0;

    rows.forEach(row => {
        const allocatedAmount = parseFloat(row.querySelector('.allocated-amount').innerText) || 0;
        const profitRatio = parseFloat(row.querySelector('.profit-ratio').value) || 0;

        // حساب إجمالي المبلغ
        totalAmount += allocatedAmount;

        // حساب نسبة الربح المرجحة
        weightedProfit += allocatedAmount * (profitRatio / 100);
    });

    // تحديث الإجماليات في العناصر المناسبة
    document.getElementById(totalAmountId).innerText = totalAmount.toFixed(2);
    document.getElementById(totalProfitId).innerText = ((weightedProfit / totalAmount) * 100 || 0).toFixed(2);
}

</script>
<script>
    function calculateInvestment() {
        const totalInvestment = parseFloat(document.getElementById('totalInvestment').value);

        // النسب المخصصة
        const allocations = {
            fixedIncome: 0.35,
            gold: 0.15,
            stocks: 0.25,
            realEstate: 0.15,
            companies: 0.10
        };

        // حساب الاستثمار لكل نوع
        const allocatedAmounts = {
            fixedIncome: totalInvestment * allocations.fixedIncome,
            gold: totalInvestment * allocations.gold,
            stocks: totalInvestment * allocations.stocks,
            realEstate: totalInvestment * allocations.realEstate,
            companies: totalInvestment * allocations.companies
        };

        // تحديث استثمار العقار
        document.getElementById('realEstateAllocation').innerText = allocatedAmounts.realEstate.toFixed(2);
        document.getElementById('realEstateProfit').innerText = (allocatedAmounts.realEstate * 0.1).toFixed(2); // أرباح افتراضية 10%

        // تحديث الجداول الأخرى
        updateTable('fixedIncomeFunds', allocatedAmounts.fixedIncome);
        updateTable('goldFunds', allocatedAmounts.gold);
        updateTable('stockFunds', allocatedAmounts.stocks);

        calculateTotalProfit();
    }

    function updateTable(tableId, totalAllocation) {
        const rows = document.getElementById(tableId).querySelectorAll('tr');
        let totalRatio = 0;

        // جمع نسب الأرباح لكل الشركات في الجدول
        rows.forEach(row => {
            totalRatio += parseFloat(row.querySelector('.profit-ratio').value) / 100;
        });

        // توزيع المبلغ الإجمالي على الشركات بناءً على نسب الأرباح
        rows.forEach(row => {
            const profitRatio = parseFloat(row.querySelector('.profit-ratio').value) / 100;
            const allocatedAmount = (profitRatio / totalRatio) * totalAllocation;
            const expectedProfit = allocatedAmount * profitRatio;

            row.querySelector('.allocated-amount').innerText = allocatedAmount.toFixed(2);
            row.querySelector('.expected-profit').innerText = expectedProfit.toFixed(2);
        });
    }

    function calculateTotalProfit() {
        let totalProfit = 0;
        document.querySelectorAll('.expected-profit').forEach(cell => {
            totalProfit += parseFloat(cell.innerText);
        });
        document.getElementById('totalExpectedProfit').innerText = totalProfit.toFixed(2);
    }
</script>

</body>

</html>
