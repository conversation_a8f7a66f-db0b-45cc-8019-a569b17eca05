<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستخرج الإيميلات - Email Extractor</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .results-container {
            margin-top: 20px;
        }
        .email-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--secondary-color);
        }
        .email-text {
            margin-right: 10px;
            margin-left: 10px;
            color: var(--text-color);
        }
        .copy-email {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
        }
        .copy-email:hover {
            background-color: var(--secondary-color);
        }
        .stats {
            margin: 20px 0;
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            text-align: center;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>مستخرج الإيميلات <span>Email Extractor</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="inputText" class="ar">أدخل النص الذي يحتوي على عناوين البريد الإلكتروني:</label>
            <label for="inputText" class="en">Enter text containing email addresses:</label>
            <textarea id="inputText" rows="8" placeholder="Paste your text here..."></textarea>
        </div>

        <div class="button-group">
            <button id="extractBtn" class="ar">استخراج الإيميلات</button>
            <button id="extractBtn" class="en">Extract Emails</button>
            
            <button id="copyAllBtn" class="ar">نسخ جميع الإيميلات</button>
            <button id="copyAllBtn" class="en">Copy All Emails</button>
        </div>

        <div class="stats" id="stats" style="display: none;"></div>
        
        <div class="results-container" id="resultsContainer"></div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            const resultsContainer = document.getElementById('resultsContainer');
            const stats = document.getElementById('stats');

            // Load saved input if exists
            inputText.value = window.utils.getSavedInput('emailExtractor');

            // Extract emails using regex
            function extractEmails(text) {
                const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
                return [...new Set(text.match(emailRegex) || [])]; // Remove duplicates
            }

            // Display results
            function displayResults(emails) {
                const lang = document.documentElement.lang;
                stats.style.display = 'block';
                stats.textContent = lang === 'ar' ?
                    `تم العثور على ${emails.length} عنوان بريد إلكتروني` :
                    `Found ${emails.length} unique email addresses`;

                resultsContainer.innerHTML = '';
                emails.forEach(email => {
                    const emailItem = document.createElement('div');
                    emailItem.className = 'email-item';
                    emailItem.innerHTML = `
                        <span class="email-text">${email}</span>
                        <button class="copy-email" data-email="${email}">
                            <i class="fas fa-copy"></i>
                            ${lang === 'ar' ? 'نسخ' : 'Copy'}
                        </button>
                    `;
                    resultsContainer.appendChild(emailItem);
                });

                // Add click events for individual copy buttons
                document.querySelectorAll('.copy-email').forEach(button => {
                    button.addEventListener('click', () => {
                        const email = button.getAttribute('data-email');
                        window.utils.copyToClipboard(email);
                    });
                });
            }

            // Extract emails on button click
            document.getElementById('extractBtn').addEventListener('click', () => {
                const text = inputText.value;
                const emails = extractEmails(text);
                displayResults(emails);
                window.utils.saveInput('emailExtractor', text);
            });

            // Copy all emails
            document.getElementById('copyAllBtn').addEventListener('click', () => {
                const emails = extractEmails(inputText.value);
                if (emails.length > 0) {
                    window.utils.copyToClipboard(emails.join('\n'));
                }
            });

            // Extract on paste
            inputText.addEventListener('paste', () => {
                setTimeout(() => {
                    document.getElementById('extractBtn').click();
                }, 0);
            });
        });
    </script>
</body>
</html>