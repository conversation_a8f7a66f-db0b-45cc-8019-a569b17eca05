<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آلة حاسبة - Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .calculator {
            max-width: 400px;
            margin: 20px auto;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px;
        }
        .calculator-display {
            background-color: var(--bg-color);
            padding: 20px;
            text-align: right;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            min-height: 60px;
            position: relative;
        }
        .calc-input {
            font-size: 2rem;
            font-family: monospace;
            color: var(--text-color);
            margin: 0;
            word-break: break-all;
        }
        .calc-preview {
            font-size: 1rem;
            color: var(--secondary-color);
            position: absolute;
            top: 5px;
            right: 10px;
        }
        .calculator-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }
        .calculator-grid button {
            padding: 15px;
            font-size: 1.2rem;
            border: none;
            background-color: var(--bg-color);
            color: var(--text-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .calculator-grid button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        .calculator-grid button.operator {
            background-color: var(--primary-color);
            color: white;
        }
        .calculator-grid button.equals {
            grid-column: span 2;
            background-color: #28a745;
            color: white;
        }
        .calculator-grid button.clear {
            background-color: #dc3545;
            color: white;
        }
        .history {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
            max-height: 200px;
            overflow-y: auto;
        }
        .history-item {
            padding: 5px 10px;
            border-bottom: 1px solid var(--secondary-color);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .history-item:hover {
            background-color: var(--card-bg);
        }
        .history-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <header>
        <h1>آلة حاسبة <span>Calculator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="calculator">
            <div class="calculator-display">
                <div class="calc-preview" id="preview"></div>
                <div class="calc-input" id="display">0</div>
            </div>

            <div class="calculator-grid">
                <button class="clear" onclick="calculator.clear()">C</button>
                <button onclick="calculator.delete()">←</button>
                <button onclick="calculator.appendOperator('(')">(</button>
                <button onclick="calculator.appendOperator(')')">)</button>

                <button onclick="calculator.appendNumber('7')">7</button>
                <button onclick="calculator.appendNumber('8')">8</button>
                <button onclick="calculator.appendNumber('9')">9</button>
                <button class="operator" onclick="calculator.appendOperator('/')">/</button>

                <button onclick="calculator.appendNumber('4')">4</button>
                <button onclick="calculator.appendNumber('5')">5</button>
                <button onclick="calculator.appendNumber('6')">6</button>
                <button class="operator" onclick="calculator.appendOperator('*')">×</button>

                <button onclick="calculator.appendNumber('1')">1</button>
                <button onclick="calculator.appendNumber('2')">2</button>
                <button onclick="calculator.appendNumber('3')">3</button>
                <button class="operator" onclick="calculator.appendOperator('-')">-</button>

                <button onclick="calculator.appendNumber('0')">0</button>
                <button onclick="calculator.appendNumber('.')">.</button>
                <button class="equals" onclick="calculator.calculate()">=</button>
                <button class="operator" onclick="calculator.appendOperator('+')">+</button>
            </div>

            <div class="history">
                <div id="history-list"></div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            class Calculator {
                constructor() {
                    this.display = document.getElementById('display');
                    this.preview = document.getElementById('preview');
                    this.historyList = document.getElementById('history-list');
                    this.currentInput = '0';
                    this.history = JSON.parse(localStorage.getItem('calculator_history') || '[]');
                    this.updateHistoryDisplay();
                }

                appendNumber(number) {
                    if (this.currentInput === '0' && number !== '.') {
                        this.currentInput = number;
                    } else {
                        this.currentInput += number;
                    }
                    this.updateDisplay();
                }

                appendOperator(operator) {
                    if (this.currentInput !== '0' || operator === '(') {
                        this.currentInput += operator;
                        this.updateDisplay();
                    }
                }

                clear() {
                    this.currentInput = '0';
                    this.updateDisplay();
                }

                delete() {
                    if (this.currentInput.length === 1) {
                        this.currentInput = '0';
                    } else {
                        this.currentInput = this.currentInput.slice(0, -1);
                    }
                    this.updateDisplay();
                }

                calculate() {
                    try {
                        const result = eval(this.currentInput);
                        const calculation = `${this.currentInput} = ${result}`;
                        this.addToHistory(calculation);
                        this.currentInput = result.toString();
                        this.updateDisplay();
                    } catch (error) {
                        this.display.textContent = 'Error';
                        setTimeout(() => this.updateDisplay(), 2000);
                    }
                }

                updateDisplay() {
                    this.display.textContent = this.currentInput;
                    try {
                        const result = eval(this.currentInput);
                        if (result !== undefined && !isNaN(result) && result !== this.currentInput) {
                            this.preview.textContent = `= ${result}`;
                        } else {
                            this.preview.textContent = '';
                        }
                    } catch {
                        this.preview.textContent = '';
                    }
                }

                addToHistory(calculation) {
                    this.history.unshift(calculation);
                    if (this.history.length > 10) {
                        this.history.pop();
                    }
                    localStorage.setItem('calculator_history', JSON.stringify(this.history));
                    this.updateHistoryDisplay();
                }

                updateHistoryDisplay() {
                    this.historyList.innerHTML = this.history
                        .map(calc => `<div class="history-item" onclick="calculator.useHistoryItem('${calc.split('=')[1].trim()}')">${calc}</div>`)
                        .join('');
                }

                useHistoryItem(result) {
                    this.currentInput = result;
                    this.updateDisplay();
                }
            }

            window.calculator = new Calculator();

            // Keyboard support
            document.addEventListener('keydown', (e) => {
                if (e.key >= '0' && e.key <= '9' || e.key === '.') {
                    calculator.appendNumber(e.key);
                } else if (['+', '-', '*', '/', '(', ')'].includes(e.key)) {
                    calculator.appendOperator(e.key);
                } else if (e.key === 'Enter') {
                    calculator.calculate();
                } else if (e.key === 'Backspace') {
                    calculator.delete();
                } else if (e.key === 'Escape') {
                    calculator.clear();
                }
            });
        });
    </script>
</body>
</html>