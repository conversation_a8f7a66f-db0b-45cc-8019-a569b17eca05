<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالجة الصور الاحترافية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        .image-container {
            text-align: center;
        }
        canvas {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button, input[type="file"] {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>معالجة الصور الاحترافية</h1>
        
        <div class="controls">
            <input type="file" id="imageInput" multiple accept="image/*">
            <button onclick="convertBlackWhite()">تحويل الأسود إلى أبيض</button>
            <button onclick="convertWhiteBlack()">تحويل الأبيض إلى أسود</button>
            <div>
                <label>من لون: </label><input type="color" id="fromColor">
                <label>إلى لون: </label><input type="color" id="toColor">
                <button onclick="replaceColor()">تغيير اللون</button>
            </div>
            <button onclick="exportImages()">تصدير الصور</button>
        </div>

        <div class="image-grid" id="imageGrid"></div>
    </div>

    <script>
        let processedImages = [];

        // معالجة رفع الصور
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const files = e.target.files;
            const imageGrid = document.getElementById('imageGrid');
            imageGrid.innerHTML = '';
            processedImages = [];

            Array.from(files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);

                        const container = document.createElement('div');
                        container.className = 'image-container';
                        container.appendChild(canvas);
                        imageGrid.appendChild(container);

                        processedImages.push({
                            canvas: canvas,
                            originalData: ctx.getImageData(0, 0, canvas.width, canvas.height),
                            fileName: file.name // تخزين اسم الملف الأصلي
                        });
                    }
                    img.src = event.target.result;
                }
                reader.readAsDataURL(file);
            });
        });

        // تحويل الأسود إلى أبيض
        function convertBlackWhite() {
            processedImages.forEach(image => {
                const ctx = image.canvas.getContext('2d');
                const imageData = ctx.getImageData(0, 0, image.canvas.width, image.canvas.height);
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    if (data[i] < 50 && data[i+1] < 50 && data[i+2] < 50) { // إذا كان أسود تقريباً
                        data[i] = 255;     // R
                        data[i+1] = 255;   // G
                        data[i+2] = 255;   // B
                    }
                }
                ctx.putImageData(imageData, 0, 0);
            });
        }

        // تحويل الأبيض إلى أسود
        function convertWhiteBlack() {
            processedImages.forEach(image => {
                const ctx = image.canvas.getContext('2d');
                const imageData = ctx.getImageData(0, 0, image.canvas.width, image.canvas.height);
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    if (data[i] > 200 && data[i+1] > 200 && data[i+2] > 200) { // إذا كان أبيض تقريباً
                        data[i] = 0;     // R
                        data[i+1] = 0;   // G
                        data[i+2] = 0;   // B
                    }
                }
                ctx.putImageData(imageData, 0, 0);
            });
        }

        // تغيير لون معين إلى لون آخر
        function replaceColor() {
            const fromColor = hexToRgb(document.getElementById('fromColor').value);
            const toColor = hexToRgb(document.getElementById('toColor').value);

            processedImages.forEach(image => {
                const ctx = image.canvas.getContext('2d');
                const imageData = ctx.getImageData(0, 0, image.canvas.width, image.canvas.height);
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    if (colorMatch([data[i], data[i+1], data[i+2]], fromColor)) {
                        data[i] = toColor[0];
                        data[i+1] = toColor[1];
                        data[i+2] = toColor[2];
                    }
                }
                ctx.putImageData(imageData, 0, 0);
            });
        }

        // تحويل HEX إلى RGB
        function hexToRgb(hex) {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return [r, g, b];
        }

        // التحقق من تطابق الألوان مع تفاوت بسيط
        function colorMatch(color1, color2) {
            const tolerance = 30;
            return Math.abs(color1[0] - color2[0]) < tolerance &&
                   Math.abs(color1[1] - color2[1]) < tolerance &&
                   Math.abs(color1[2] - color2[2]) < tolerance;
        }

        // تصدير الصور بأسمائها الأصلية
        function exportImages() {
            processedImages.forEach(image => {
                const link = document.createElement('a');
                link.download = image.fileName; // استخدام اسم الملف الأصلي
                link.href = image.canvas.toDataURL('image/png');
                link.click();
            });
        }
    </script>
</body>
</html>