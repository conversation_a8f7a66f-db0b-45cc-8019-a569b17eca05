<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قص الصور - Image Cropper</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .crop-container {
            position: relative;
            max-width: 100%;
            margin: 20px auto;
            overflow: hidden;
        }
        .crop-area {
            position: absolute;
            border: 2px solid var(--primary-color);
            background: rgba(255, 255, 255, 0.3);
            cursor: move;
            touch-action: none;
        }
        .resize-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-color);
        }
        .resize-handle.nw { top: -5px; left: -5px; cursor: nw-resize; }
        .resize-handle.ne { top: -5px; right: -5px; cursor: ne-resize; }
        .resize-handle.sw { bottom: -5px; left: -5px; cursor: sw-resize; }
        .resize-handle.se { bottom: -5px; right: -5px; cursor: se-resize; }
        #cropCanvas {
            max-width: 100%;
            display: block;
        }
        .drag-area {
            border: 2px dashed var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .drag-area:hover {
            border-color: var(--primary-color);
        }
        .drag-area i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        #fileInput {
            display: none;
        }
        .crop-dimensions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }
        .crop-dimensions input {
            width: 80px;
            padding: 5px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>قص الصور <span>Image Cropper</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="drag-area" id="dragArea">
            <i class="fas fa-cloud-upload-alt"></i>
            <p class="ar">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
            <p class="en">Drag & drop image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="crop-dimensions">
            <div>
                <label for="cropX" class="ar">X:</label>
                <label for="cropX" class="en">X:</label>
                <input type="number" id="cropX" readonly>
            </div>
            <div>
                <label for="cropY" class="ar">Y:</label>
                <label for="cropY" class="en">Y:</label>
                <input type="number" id="cropY" readonly>
            </div>
            <div>
                <label for="cropWidth" class="ar">العرض:</label>
                <label for="cropWidth" class="en">Width:</label>
                <input type="number" id="cropWidth" readonly>
            </div>
            <div>
                <label for="cropHeight" class="ar">الارتفاع:</label>
                <label for="cropHeight" class="en">Height:</label>
                <input type="number" id="cropHeight" readonly>
            </div>
        </div>

        <div class="crop-container" id="cropContainer" style="display: none;">
            <canvas id="cropCanvas"></canvas>
            <div class="crop-area" id="cropArea">
                <div class="resize-handle nw"></div>
                <div class="resize-handle ne"></div>
                <div class="resize-handle sw"></div>
                <div class="resize-handle se"></div>
            </div>
        </div>

        <div class="button-group">
            <button id="cropBtn" class="ar" disabled>قص الصورة</button>
            <button id="cropBtn" class="en" disabled>Crop Image</button>
            
            <button id="downloadBtn" class="ar" disabled>تحميل</button>
            <button id="downloadBtn" class="en" disabled>Download</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const cropContainer = document.getElementById('cropContainer');
            const cropCanvas = document.getElementById('cropCanvas');
            const cropArea = document.getElementById('cropArea');
            const cropBtn = document.getElementById('cropBtn');
            const downloadBtn = document.getElementById('downloadBtn');

            let originalImage = null;
            let isDragging = false;
            let isResizing = false;
            let currentResizeHandle = null;
            let startX, startY, cropStartX, cropStartY;

            // Handle file selection
            function handleFile(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = () => {
                            originalImage = img;
                            initializeCrop();
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }

            function initializeCrop() {
                // Set canvas dimensions
                const maxWidth = Math.min(800, window.innerWidth - 40);
                const scale = maxWidth / originalImage.width;
                cropCanvas.width = originalImage.width * scale;
                cropCanvas.height = originalImage.height * scale;

                // Draw image on canvas
                const ctx = cropCanvas.getContext('2d');
                ctx.drawImage(originalImage, 0, 0, cropCanvas.width, cropCanvas.height);

                // Show crop container and initialize crop area
                cropContainer.style.display = 'block';
                const initialSize = Math.min(cropCanvas.width, cropCanvas.height) * 0.8;
                const cropX = (cropCanvas.width - initialSize) / 2;
                const cropY = (cropCanvas.height - initialSize) / 2;
                
                cropArea.style.left = cropX + 'px';
                cropArea.style.top = cropY + 'px';
                cropArea.style.width = initialSize + 'px';
                cropArea.style.height = initialSize + 'px';

                updateCropDimensions();
                cropBtn.disabled = false;
            }

            // Update crop dimensions display
            function updateCropDimensions() {
                const rect = cropArea.getBoundingClientRect();
                const canvasRect = cropCanvas.getBoundingClientRect();
                const scale = originalImage.width / cropCanvas.width;

                const x = (rect.left - canvasRect.left) * scale;
                const y = (rect.top - canvasRect.top) * scale;
                const width = rect.width * scale;
                const height = rect.height * scale;

                document.getElementById('cropX').value = Math.round(x);
                document.getElementById('cropY').value = Math.round(y);
                document.getElementById('cropWidth').value = Math.round(width);
                document.getElementById('cropHeight').value = Math.round(height);
            }

            // Handle crop area dragging
            cropArea.addEventListener('mousedown', startDragging);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDragging);

            function startDragging(e) {
                if (e.target.classList.contains('resize-handle')) {
                    isResizing = true;
                    currentResizeHandle = e.target.classList[1];
                } else {
                    isDragging = true;
                }
                startX = e.clientX;
                startY = e.clientY;
                cropStartX = cropArea.offsetLeft;
                cropStartY = cropArea.offsetTop;
            }

            function drag(e) {
                if (!isDragging && !isResizing) return;

                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                const canvasRect = cropCanvas.getBoundingClientRect();

                if (isDragging) {
                    let newX = cropStartX + dx;
                    let newY = cropStartY + dy;

                    // Constrain to canvas bounds
                    newX = Math.max(0, Math.min(newX, canvasRect.width - cropArea.offsetWidth));
                    newY = Math.max(0, Math.min(newY, canvasRect.height - cropArea.offsetHeight));

                    cropArea.style.left = newX + 'px';
                    cropArea.style.top = newY + 'px';
                } else if (isResizing) {
                    const rect = cropArea.getBoundingClientRect();
                    let newWidth = rect.width;
                    let newHeight = rect.height;

                    if (currentResizeHandle.includes('e')) newWidth += dx;
                    if (currentResizeHandle.includes('w')) {
                        newWidth -= dx;
                        cropArea.style.left = (cropStartX + dx) + 'px';
                    }
                    if (currentResizeHandle.includes('s')) newHeight += dy;
                    if (currentResizeHandle.includes('n')) {
                        newHeight -= dy;
                        cropArea.style.top = (cropStartY + dy) + 'px';
                    }

                    cropArea.style.width = Math.max(50, newWidth) + 'px';
                    cropArea.style.height = Math.max(50, newHeight) + 'px';
                }

                updateCropDimensions();
            }

            function stopDragging() {
                isDragging = false;
                isResizing = false;
            }

            // Crop and download functions
            cropBtn.addEventListener('click', () => {
                const rect = cropArea.getBoundingClientRect();
                const canvasRect = cropCanvas.getBoundingClientRect();

                const scale = originalImage.width / cropCanvas.width;
                const x = (rect.left - canvasRect.left) * scale;
                const y = (rect.top - canvasRect.top) * scale;
                const width = rect.width * scale;
                const height = rect.height * scale;

                const resultCanvas = document.createElement('canvas');
                resultCanvas.width = width;
                resultCanvas.height = height;

                const ctx = resultCanvas.getContext('2d');
                ctx.drawImage(originalImage, x, y, width, height, 0, 0, width, height);

                cropCanvas.toDataURL = resultCanvas.toDataURL.bind(resultCanvas);
                downloadBtn.disabled = false;
            });

            downloadBtn.addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = 'cropped-image.png';
                link.href = cropCanvas.toDataURL('image/png');
                link.click();
            });

            // File input and drag & drop handlers
            fileInput.addEventListener('change', (e) => {
                handleFile(e.target.files[0]);
            });

            dragArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--primary-color)';
            });

            dragArea.addEventListener('dragleave', () => {
                dragArea.style.borderColor = 'var(--secondary-color)';
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--secondary-color)';
                handleFile(e.dataTransfer.files[0]);
            });

            dragArea.addEventListener('click', () => {
                fileInput.click();
            });
        });
    </script>
</body>
</html>