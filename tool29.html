<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد خطة يومية - Daily Schedule Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .scheduler {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .tasks-list {
            margin: 20px 0;
        }
        .task-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .task-item input {
            flex: 1;
        }
        .remove-task {
            background: none;
            border: none;
            color: #ff4444;
            cursor: pointer;
            padding: 5px;
        }
        .schedule {
            margin-top: 30px;
            border-radius: 5px;
            overflow: hidden;
        }
        .time-slot {
            display: grid;
            grid-template-columns: 100px 1fr;
            gap: 10px;
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        .time-slot:nth-child(odd) {
            background-color: var(--secondary-bg);
        }
        .time {
            color: var(--primary-color);
            font-weight: bold;
        }
        .activity {
            color: var(--text-color);
        }
        .break {
            color: #28a745;
            font-style: italic;
        }
        .export-button {
            margin-top: 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .export-button:hover {
            background-color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد خطة يومية <span>Daily Schedule Generator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="scheduler">
            <div class="input-section">
                <div class="input-group">
                    <label for="startTime">وقت البداية - Start Time</label>
                    <input type="time" id="startTime" value="09:00">
                </div>
                <div class="input-group">
                    <label for="hours">عدد الساعات المتاحة - Available Hours</label>
                    <input type="number" id="hours" min="1" max="24" value="8">
                </div>
                <div class="input-group">
                    <label for="breakInterval">وقت الراحة بين المهام (دقيقة) - Break Time (minutes)</label>
                    <input type="number" id="breakInterval" min="5" max="60" value="15">
                </div>
            </div>

            <div class="tasks-list" id="tasksList">
                <h3>المهام - Tasks</h3>
                <div class="task-item">
                    <input type="text" placeholder="اسم المهمة - Task name" class="task-input">
                    <input type="number" placeholder="المدة (دقيقة) - Duration (min)" class="duration-input" min="5">
                    <button class="remove-task">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>

            <button onclick="addTask()" class="primary-button">
                <i class="fas fa-plus"></i>
                <span class="ar">أضف مهمة</span>
                <span class="en">Add Task</span>
            </button>

            <button onclick="generateSchedule()" class="primary-button" style="margin-right: 10px;">
                <i class="fas fa-calendar-check"></i>
                <span class="ar">ولّد الجدول</span>
                <span class="en">Generate Schedule</span>
            </button>

            <div id="scheduleOutput" class="schedule"></div>
        </div>
    </main>

    <script>
        function addTask() {
            const tasksList = document.getElementById('tasksList');
            const taskItem = document.createElement('div');
            taskItem.className = 'task-item';
            taskItem.innerHTML = `
                <input type="text" placeholder="اسم المهمة - Task name" class="task-input">
                <input type="number" placeholder="المدة (دقيقة) - Duration (min)" class="duration-input" min="5">
                <button class="remove-task" onclick="removeTask(this)">
                    <i class="fas fa-trash-alt"></i>
                </button>
            `;
            tasksList.appendChild(taskItem);
        }

        function removeTask(button) {
            button.parentElement.remove();
        }

        function generateSchedule() {
            const startTime = document.getElementById('startTime').value;
            const hours = parseInt(document.getElementById('hours').value);
            const breakInterval = parseInt(document.getElementById('breakInterval').value);
            
            const tasks = Array.from(document.getElementsByClassName('task-item')).map(item => ({
                name: item.querySelector('.task-input').value,
                duration: parseInt(item.querySelector('.duration-input').value)
            })).filter(task => task.name && task.duration);

            if (tasks.length === 0) {
                alert('الرجاء إضافة مهام أولاً - Please add tasks first');
                return;
            }

            const schedule = [];
            let currentTime = new Date(`2025-01-01 ${startTime}`);
            const endTime = new Date(currentTime.getTime() + hours * 60 * 60 * 1000);

            tasks.forEach((task, index) => {
                if (currentTime >= endTime) return;

                // Add task
                schedule.push({
                    time: currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true }),
                    activity: task.name,
                    isBreak: false
                });

                // Update current time
                currentTime = new Date(currentTime.getTime() + task.duration * 60 * 1000);

                // Add break if not the last task and there's time
                if (index < tasks.length - 1 && currentTime < endTime) {
                    schedule.push({
                        time: currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true }),
                        activity: 'استراحة - Break',
                        isBreak: true
                    });
                    currentTime = new Date(currentTime.getTime() + breakInterval * 60 * 1000);
                }
            });

            displaySchedule(schedule);
        }

        function displaySchedule(schedule) {
            const output = document.getElementById('scheduleOutput');
            output.innerHTML = schedule.map(slot => `
                <div class="time-slot">
                    <div class="time">${slot.time}</div>
                    <div class="activity ${slot.isBreak ? 'break' : ''}">${slot.activity}</div>
                </div>
            `).join('');
        }

        // Add initial task input
        addTask();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>