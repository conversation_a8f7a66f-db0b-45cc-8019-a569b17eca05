<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محلل محتوى SEO - SEO Content Analyzer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .analyzer {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .input-group textarea {
            height: 300px;
            resize: vertical;
            direction: ltr;
        }
        .meta-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .analysis-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .result-section {
            background-color: var(--secondary-bg);
            padding: 20px;
            border-radius: 5px;
        }
        .section-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .score {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.9em;
        }
        .score.good {
            background-color: #28a745;
            color: white;
        }
        .score.warning {
            background-color: #ffc107;
            color: black;
        }
        .score.bad {
            background-color: #dc3545;
            color: white;
        }
        .analysis-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: 5px;
        }
        .analysis-item h4 {
            margin: 0 0 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .analysis-item p {
            margin: 5px 0;
            font-size: 0.9em;
        }
        .keyword-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        .keyword-item:last-child {
            border-bottom: none;
        }
        .keyword-count {
            color: var(--primary-color);
            font-weight: bold;
        }
        .heading-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .heading-item {
            padding: 8px;
            margin-bottom: 5px;
            background-color: var(--card-bg);
            border-radius: 3px;
        }
        .heading-tag {
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 5px;
        }
        .links-section {
            max-height: 300px;
            overflow-y: auto;
        }
        .link-item {
            word-break: break-all;
            padding: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        .link-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <header>
        <h1>محلل محتوى SEO <span>SEO Content Analyzer</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="analyzer">
            <div class="input-section">
                <div class="meta-inputs">
                    <div class="input-group">
                        <label for="titleInput">عنوان الصفحة - Page Title</label>
                        <input type="text" id="titleInput" placeholder="Enter page title">
                    </div>
                    <div class="input-group">
                        <label for="metaDescription">وصف ميتا - Meta Description</label>
                        <input type="text" id="metaDescription" placeholder="Enter meta description">
                    </div>
                    <div class="input-group">
                        <label for="focusKeyword">الكلمة المفتاحية الرئيسية - Focus Keyword</label>
                        <input type="text" id="focusKeyword" placeholder="Enter focus keyword">
                    </div>
                </div>

                <div class="input-group">
                    <label for="contentInput">المحتوى - Content (HTML)</label>
                    <textarea id="contentInput" placeholder="Enter your HTML content here..."></textarea>
                </div>

                <button onclick="analyzeContent()" class="primary-button">
                    <i class="fas fa-search"></i>
                    <span class="ar">تحليل</span>
                    <span class="en">Analyze</span>
                </button>
            </div>

            <div class="analysis-results" id="results">
                <!-- Results will be populated here via JavaScript -->
            </div>
        </div>
    </main>

    <script>
        function analyzeContent() {
            const title = document.getElementById('titleInput').value;
            const metaDesc = document.getElementById('metaDescription').value;
            const focusKeyword = document.getElementById('focusKeyword').value;
            const content = document.getElementById('contentInput').value;

            if (!content) {
                alert('الرجاء إدخال محتوى للتحليل - Please enter content to analyze');
                return;
            }

            const parser = new DOMParser();
            const doc = parser.parseFromString(content, 'text/html');
            
            const results = {
                titleAnalysis: analyzeTitle(title, focusKeyword),
                metaDescAnalysis: analyzeMetaDesc(metaDesc, focusKeyword),
                headingsAnalysis: analyzeHeadings(doc),
                contentAnalysis: analyzeContentText(doc, focusKeyword),
                keywordAnalysis: analyzeKeywordDensity(doc, focusKeyword),
                linksAnalysis: analyzeLinks(doc),
                imagesAnalysis: analyzeImages(doc)
            };

            displayResults(results);
        }

        function analyzeTitle(title, keyword) {
            const length = title.length;
            const hasKeyword = keyword && title.toLowerCase().includes(keyword.toLowerCase());
            
            return {
                score: length >= 40 && length <= 60 ? 'good' : 'warning',
                items: [
                    {
                        title: 'طول العنوان - Title Length',
                        status: length >= 40 && length <= 60 ? 'good' : 'warning',
                        message: `${length} حرف - characters (المثالي: 40-60)`
                    },
                    {
                        title: 'الكلمة المفتاحية - Focus Keyword',
                        status: hasKeyword ? 'good' : 'warning',
                        message: hasKeyword ? 'موجودة - Present' : 'غير موجودة - Missing'
                    }
                ]
            };
        }

        function analyzeMetaDesc(desc, keyword) {
            const length = desc.length;
            const hasKeyword = keyword && desc.toLowerCase().includes(keyword.toLowerCase());
            
            return {
                score: length >= 120 && length <= 160 ? 'good' : 'warning',
                items: [
                    {
                        title: 'طول الوصف - Description Length',
                        status: length >= 120 && length <= 160 ? 'good' : 'warning',
                        message: `${length} حرف - characters (المثالي: 120-160)`
                    },
                    {
                        title: 'الكلمة المفتاحية - Focus Keyword',
                        status: hasKeyword ? 'good' : 'warning',
                        message: hasKeyword ? 'موجودة - Present' : 'غير موجودة - Missing'
                    }
                ]
            };
        }

        function analyzeHeadings(doc) {
            const headings = {
                h1: Array.from(doc.getElementsByTagName('h1')),
                h2: Array.from(doc.getElementsByTagName('h2')),
                h3: Array.from(doc.getElementsByTagName('h3'))
            };

            return {
                score: headings.h1.length === 1 ? 'good' : 'warning',
                headings: headings
            };
        }

        function analyzeContentText(doc, keyword) {
            const text = doc.body.textContent;
            const wordCount = text.trim().split(/\s+/).length;
            const hasKeyword = keyword && text.toLowerCase().includes(keyword.toLowerCase());
            
            return {
                score: wordCount >= 300 ? 'good' : 'warning',
                items: [
                    {
                        title: 'عدد الكلمات - Word Count',
                        status: wordCount >= 300 ? 'good' : 'warning',
                        message: `${wordCount} كلمة - words (الحد الأدنى: 300)`
                    },
                    {
                        title: 'الكلمة المفتاحية - Focus Keyword',
                        status: hasKeyword ? 'good' : 'warning',
                        message: hasKeyword ? 'موجودة - Present' : 'غير موجودة - Missing'
                    }
                ]
            };
        }

        function analyzeKeywordDensity(doc, keyword) {
            if (!keyword) return { score: 'warning', density: 0, count: 0 };

            const text = doc.body.textContent.toLowerCase();
            const words = text.trim().split(/\s+/);
            const keywordCount = text.split(keyword.toLowerCase()).length - 1;
            const density = (keywordCount / words.length) * 100;

            return {
                score: density >= 0.5 && density <= 2.5 ? 'good' : 'warning',
                density: density.toFixed(2),
                count: keywordCount
            };
        }

        function analyzeLinks(doc) {
            const links = Array.from(doc.getElementsByTagName('a'));
            const internal = links.filter(link => !link.href.startsWith('http'));
            const external = links.filter(link => link.href.startsWith('http'));

            return {
                score: links.length > 0 ? 'good' : 'warning',
                internal: internal,
                external: external
            };
        }

        function analyzeImages(doc) {
            const images = Array.from(doc.getElementsByTagName('img'));
            const withAlt = images.filter(img => img.alt);
            
            return {
                score: images.length === withAlt.length ? 'good' : 'warning',
                total: images.length,
                withAlt: withAlt.length
            };
        }

        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-section">
                    <div class="section-header">
                        <h3>تحليل العنوان - Title Analysis</h3>
                        <span class="score ${results.titleAnalysis.score}">
                            ${results.titleAnalysis.score === 'good' ? 'جيد' : 'تحذير'}
                        </span>
                    </div>
                    ${results.titleAnalysis.items.map(item => `
                        <div class="analysis-item">
                            <h4>${item.title} 
                                <span class="score ${item.status}">${item.message}</span>
                            </h4>
                        </div>
                    `).join('')}
                </div>

                <div class="result-section">
                    <div class="section-header">
                        <h3>تحليل الوصف - Meta Description</h3>
                        <span class="score ${results.metaDescAnalysis.score}">
                            ${results.metaDescAnalysis.score === 'good' ? 'جيد' : 'تحذير'}
                        </span>
                    </div>
                    ${results.metaDescAnalysis.items.map(item => `
                        <div class="analysis-item">
                            <h4>${item.title}
                                <span class="score ${item.status}">${item.message}</span>
                            </h4>
                        </div>
                    `).join('')}
                </div>

                <div class="result-section">
                    <div class="section-header">
                        <h3>العناوين - Headings</h3>
                        <span class="score ${results.headingsAnalysis.score}">
                            ${results.headingsAnalysis.score === 'good' ? 'جيد' : 'تحذير'}
                        </span>
                    </div>
                    <ul class="heading-list">
                        ${Object.entries(results.headingsAnalysis.headings).map(([tag, headings]) => `
                            ${headings.map(h => `
                                <li class="heading-item">
                                    <span class="heading-tag">${tag.toUpperCase()}</span>
                                    ${h.textContent}
                                </li>
                            `).join('')}
                        `).join('')}
                    </ul>
                </div>

                <div class="result-section">
                    <div class="section-header">
                        <h3>كثافة الكلمات المفتاحية - Keyword Density</h3>
                        <span class="score ${results.keywordAnalysis.score}">
                            ${results.keywordAnalysis.density}%
                        </span>
                    </div>
                    <div class="analysis-item">
                        <p>عدد التكرارات - Occurrences: ${results.keywordAnalysis.count}</p>
                    </div>
                </div>

                <div class="result-section">
                    <div class="section-header">
                        <h3>الروابط - Links</h3>
                        <span class="score ${results.linksAnalysis.score}">
                            ${results.linksAnalysis.score === 'good' ? 'جيد' : 'تحذير'}
                        </span>
                    </div>
                    <div class="links-section">
                        <h4>روابط داخلية - Internal (${results.linksAnalysis.internal.length})</h4>
                        ${results.linksAnalysis.internal.map(link => `
                            <div class="link-item">${link.href}</div>
                        `).join('')}
                        <h4>روابط خارجية - External (${results.linksAnalysis.external.length})</h4>
                        ${results.linksAnalysis.external.map(link => `
                            <div class="link-item">${link.href}</div>
                        `).join('')}
                    </div>
                </div>

                <div class="result-section">
                    <div class="section-header">
                        <h3>الصور - Images</h3>
                        <span class="score ${results.imagesAnalysis.score}">
                            ${results.imagesAnalysis.score === 'good' ? 'جيد' : 'تحذير'}
                        </span>
                    </div>
                    <div class="analysis-item">
                        <p>إجمالي الصور - Total Images: ${results.imagesAnalysis.total}</p>
                        <p>صور مع Alt - With Alt Text: ${results.imagesAnalysis.withAlt}</p>
                    </div>
                </div>
            `;
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>