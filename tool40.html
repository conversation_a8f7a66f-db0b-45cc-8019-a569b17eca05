<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محلل تكرار الكلمات - Word Frequency Analyzer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .analyzer {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            margin-bottom: 30px;
        }
        .text-input {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            resize: vertical;
            direction: ltr;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .control-group label {
            color: var(--text-color);
        }
        .control-group input[type="number"] {
            width: 80px;
            padding: 5px;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .result-section {
            background-color: var(--secondary-bg);
            padding: 20px;
            border-radius: 5px;
        }
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        .result-header button {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
        }
        .frequency-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .frequency-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        .frequency-item:last-child {
            border-bottom: none;
        }
        .frequency-text {
            flex-grow: 1;
            margin-left: 10px;
            direction: ltr;
        }
        .frequency-count {
            color: var(--primary-color);
            font-weight: bold;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background-color: var(--secondary-bg);
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            color: var(--primary-color);
            margin: 5px 0;
        }
        .options {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .option-checkbox {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <header>
        <h1>محلل تكرار الكلمات <span>Word Frequency Analyzer</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="analyzer">
            <div class="input-section">
                <textarea class="text-input" id="textInput" 
                    placeholder="أدخل النص هنا للتحليل... - Enter text here for analysis..."></textarea>
                
                <div class="options">
                    <label class="option-checkbox">
                        <input type="checkbox" id="caseSensitive">
                        <span>مراعاة حالة الأحرف - Case Sensitive</span>
                    </label>
                    <label class="option-checkbox">
                        <input type="checkbox" id="ignoreNumbers">
                        <span>تجاهل الأرقام - Ignore Numbers</span>
                    </label>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <label>طول العبارة - Phrase Length:</label>
                        <input type="number" id="phraseLength" min="1" max="5" value="1">
                    </div>
                    <button onclick="analyzeText()" class="primary-button">
                        <i class="fas fa-search"></i>
                        <span class="ar">تحليل</span>
                        <span class="en">Analyze</span>
                    </button>
                </div>
            </div>

            <div class="stats" id="stats"></div>

            <div class="results">
                <div class="result-section">
                    <div class="result-header">
                        <h3>تكرار الكلمات - Word Frequency</h3>
                        <button onclick="exportResults('words')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                    <div class="frequency-list" id="wordFrequency"></div>
                </div>

                <div class="result-section">
                    <div class="result-header">
                        <h3>تكرار العبارات - Phrase Frequency</h3>
                        <button onclick="exportResults('phrases')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                    <div class="frequency-list" id="phraseFrequency"></div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function analyzeText() {
            const text = document.getElementById('textInput').value;
            const phraseLength = parseInt(document.getElementById('phraseLength').value);
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const ignoreNumbers = document.getElementById('ignoreNumbers').checked;

            if (!text.trim()) {
                alert('الرجاء إدخال نص للتحليل - Please enter text to analyze');
                return;
            }

            // Process text
            const processedText = caseSensitive ? text : text.toLowerCase();
            const words = processedText.match(/\b\w+\b/g) || [];
            const wordFreq = {};
            const phraseFreq = {};

            // Word frequency
            words.forEach(word => {
                if (ignoreNumbers && /^\d+$/.test(word)) return;
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            });

            // Phrase frequency
            if (phraseLength > 1) {
                for (let i = 0; i <= words.length - phraseLength; i++) {
                    const phrase = words.slice(i, i + phraseLength).join(' ');
                    if (ignoreNumbers && /\d/.test(phrase)) continue;
                    phraseFreq[phrase] = (phraseFreq[phrase] || 0) + 1;
                }
            }

            // Update statistics
            updateStats(words, wordFreq, phraseFreq);

            // Display frequencies
            displayFrequencies('wordFrequency', wordFreq);
            displayFrequencies('phraseFrequency', phraseFreq);
        }

        function updateStats(words, wordFreq, phraseFreq) {
            const stats = {
                'إجمالي الكلمات - Total Words': words.length,
                'الكلمات الفريدة - Unique Words': Object.keys(wordFreq).length,
                'متوسط تكرار الكلمات - Average Frequency': 
                    (words.length / Object.keys(wordFreq).length).toFixed(2),
                'العبارات الفريدة - Unique Phrases': Object.keys(phraseFreq).length
            };

            document.getElementById('stats').innerHTML = Object.entries(stats)
                .map(([label, value]) => `
                    <div class="stat-card">
                        <div>${label}</div>
                        <div class="stat-value">${value}</div>
                    </div>
                `).join('');
        }

        function displayFrequencies(elementId, frequencies) {
            const sorted = Object.entries(frequencies)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 100);

            document.getElementById(elementId).innerHTML = sorted
                .map(([text, count]) => `
                    <div class="frequency-item">
                        <div class="frequency-text">${text}</div>
                        <div class="frequency-count">${count}</div>
                    </div>
                `).join('');
        }

        function exportResults(type) {
            const text = document.getElementById('textInput').value;
            if (!text.trim()) return;

            const frequencies = type === 'words' ? 
                document.getElementById('wordFrequency').innerText :
                document.getElementById('phraseFrequency').innerText;

            const blob = new Blob([frequencies], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `frequency_${type}_${new Date().toISOString().slice(0,10)}.txt`;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>