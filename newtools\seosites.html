<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات SEO المفتوحة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            direction: rtl;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        p {
            color: #666;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        input[type="text"] {
            padding: 10px;
            width: 70%;
            margin-left: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>أدوات SEO المفتوحة</h1>
        <p>أدخل دومين (مثل example.com) واضغط "فتح" لعرض أدوات SEO لهذا الدومين.</p>
        <input type="text" id="domain" placeholder="أدخل الدومين (مثل example.com)">
        <button id="open">فتح</button>
    </div>

    <script>
        const urls = [
            "file:///C:/Users/<USER>/Desktop/seoreportupdated.html",
            "https://letmepost.com/check-da-pa",
            "https://www.wpthemedetector.com/",
            "https://builtwith.com/?https://{domain}/",
            "https://pagespeed.web.dev/analysis?url=https://{domain}/",
            "https://ahrefs.com/backlink-checker/?input=https://{domain}/&mode=subdomains",
            "https://www.semrush.com/analytics/overview/?q=https://{domain}/&searchType=domain&protocol=https&db=sa",
            "https://rankmath.com/tools/seo-analyzer/",
            "https://www.seoptimer.com/{domain}/",
            "https://www.seoptimer.com/top-keyword-rankings/{domain}",
            "https://{domain}/",
            "https://{domain}/sitemap.xml",
            "view-source:https://{domain}",
            "https://www.google.com/search?q=site:https://{domain}/",
            "https://socialsignalscheck.com/",
            "https://www.blogger.com/blog/post/edit/241751380174493708/3215432413936238049",
            "https://docs.google.com/document/u/0/",
            "https://docs.google.com/document/d/1cZAzczeHcR_6lbFUo1C4LSqqhmXz_LWO9mEKm9Hczlo/edit?tab=t.0",
            "https://chatgpt.com/",
            "https://grok.com/?1="
        ];

        document.getElementById('open').addEventListener('click', function() {
            let domain = document.getElementById('domain').value.trim();
            // إزالة البروتوكول (http:// أو https://) و www.
            domain = domain.replace(/(^\w+:|^)\/\//, '');
            domain = domain.replace(/^www\./, '');

            // إنشاء قائمة الروابط مع استبدال {domain} بالدومين المدخل
            const finalUrls = urls.map(url => {
                if (url.includes('{domain}')) {
                    return url.replace('{domain}', domain);
                }
                return url;
            });

            // فتح الروابط في تبويبات جديدة بالترتيب
            finalUrls.forEach(url => {
                window.open(url, '_blank');
            });
        });
    </script>
</body>
</html>