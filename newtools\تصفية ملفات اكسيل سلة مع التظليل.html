<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فلترة المنتجات من Excel</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h2>رفع ملفات Excel</h2>
    <input type="file" id="file1" accept=".xlsx, .xls"> <label>ملف المنتجات</label><br><br>
    <input type="file" id="file2" accept=".xlsx, .xls"> <label>ملف المنتجات المطلوب تعديلها</label><br><br>
    
    <label>حدد العمود في الملف الأول (اسم أو حرف العمود):</label>
    <input type="text" id="column1" placeholder="مثال: A أو اسم العمود"><br><br>
    
    <label>حدد العمود في الملف الثاني (اسم أو حرف العمود):</label>
    <input type="text" id="column2" placeholder="مثال: B أو اسم العمود"><br><br>
    
    <button onclick="processFiles()">بدء التصفية</button>
    <button onclick="exportHighlightedFile()">تصدير الملف مع التظليل</button>
    
    <script>
        function columnLetterToIndex(letter) {
            let column = letter.toUpperCase();
            let index = 0;
            for (let i = 0; i < column.length; i++) {
                index = index * 26 + (column.charCodeAt(i) - 64);
            }
            return index - 1;
        }

        let originalSheet;

        function processFiles() {
            let file1 = document.getElementById('file1').files[0];
            let file2 = document.getElementById('file2').files[0];
            let column1Input = document.getElementById('column1').value.trim();
            let column2Input = document.getElementById('column2').value.trim();
            
            if (!file1 || !file2) {
                alert("يرجى رفع كلا الملفين");
                return;
            }
            
            if (!column1Input || !column2Input) {
                alert("يرجى تحديد أسماء الأعمدة للمقارنة");
                return;
            }
            
            let reader1 = new FileReader();
            let reader2 = new FileReader();
            
            reader1.onload = function(e) {
                let data1 = new Uint8Array(e.target.result);
                let workbook1 = XLSX.read(data1, { type: 'array' });
                originalSheet = workbook1.Sheets[workbook1.SheetNames[0]];
                let sheet1 = XLSX.utils.sheet_to_json(originalSheet, { header: 1 });
                
                reader2.onload = function(e) {
                    let data2 = new Uint8Array(e.target.result);
                    let workbook2 = XLSX.read(data2, { type: 'array' });
                    let sheet2 = XLSX.utils.sheet_to_json(workbook2.Sheets[workbook2.SheetNames[0]], { header: 1 });
                    
                    let header1 = sheet1[0];
                    let header2 = sheet2[0];
                    
                    let col1Index = isNaN(column1Input) ? (header1.includes(column1Input) ? header1.indexOf(column1Input) : columnLetterToIndex(column1Input)) : parseInt(column1Input);
                    let col2Index = isNaN(column2Input) ? (header2.includes(column2Input) ? header2.indexOf(column2Input) : columnLetterToIndex(column2Input)) : parseInt(column2Input);
                    
                    if (col1Index < 0 || col2Index < 0) {
                        alert("تعذر العثور على الأعمدة المحددة، يرجى التأكد من صحة المدخلات.");
                        return;
                    }
                    
                    let productNames = new Set(sheet2.slice(1).map(row => row[col2Index]));
                    let filteredProducts = sheet1.map((row, index) => index === 0 ? [...row, "مطابق"] : [...row, productNames.has(row[col1Index]) ? "✔" : ""]);
                    
                    let newWorkbook = XLSX.utils.book_new();
                    let newSheet = XLSX.utils.aoa_to_sheet(filteredProducts);
                    XLSX.utils.book_append_sheet(newWorkbook, newSheet, "البيانات المصفاة");
                    XLSX.writeFile(newWorkbook, "البيانات_المصفاة.xlsx");
                };
                reader2.readAsArrayBuffer(file2);
            };
            reader1.readAsArrayBuffer(file1);
        }
    </script>
</body>
</html>
