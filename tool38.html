<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منتقي الألوان - Color Picker & Extractor</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .color-tool {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: var(--primary-color);
            color: white;
        }
        .picker-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .color-picker {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .color-display {
            height: 150px;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .color-input {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        .color-info {
            padding: 15px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
        }
        .color-values {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .color-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background-color: var(--card-bg);
            border-radius: 5px;
        }
        .copy-button {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            padding: 5px;
        }
        .copy-button:hover {
            color: var(--secondary-color);
        }
        .image-uploader {
            border: 2px dashed var(--border-color);
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .image-uploader:hover {
            border-color: var(--primary-color);
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 5px;
        }
        .extracted-colors {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .color-swatch {
            aspect-ratio: 1;
            border-radius: 5px;
            position: relative;
            cursor: pointer;
        }
        .color-swatch:hover .color-code {
            opacity: 1;
        }
        .color-code {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px;
            font-size: 0.8em;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin-top: 20px;
            padding: 15px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
        }
        .palette-color {
            aspect-ratio: 1;
            border-radius: 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .palette-color:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <header>
        <h1>منتقي الألوان <span>Color Picker & Extractor</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="color-tool">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('picker')">
                    <i class="fas fa-eye-dropper"></i> منتقي الألوان - Color Picker
                </div>
                <div class="tab" onclick="switchTab('extractor')">
                    <i class="fas fa-image"></i> مستخرج الألوان - Color Extractor
                </div>
            </div>

            <div id="pickerTab">
                <div class="picker-section">
                    <div class="color-picker">
                        <div class="color-display" id="colorDisplay">
                            <input type="color" id="colorInput" class="color-input" oninput="updateColor(this.value)">
                        </div>
                        <div class="color-info">
                            <div class="color-values">
                                <div class="color-value">
                                    <span>HEX</span>
                                    <div>
                                        <span id="hexValue">#000000</span>
                                        <button class="copy-button" onclick="copyColor('hex')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="color-value">
                                    <span>RGB</span>
                                    <div>
                                        <span id="rgbValue">rgb(0,0,0)</span>
                                        <button class="copy-button" onclick="copyColor('rgb')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="color-value">
                                    <span>HSL</span>
                                    <div>
                                        <span id="hslValue">hsl(0,0%,0%)</span>
                                        <button class="copy-button" onclick="copyColor('hsl')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="color-palette">
                        <!-- Common colors will be added here via JavaScript -->
                    </div>
                </div>
            </div>

            <div id="extractorTab" style="display: none;">
                <div class="image-uploader" id="imageUploader" onclick="document.getElementById('imageInput').click()">
                    <i class="fas fa-cloud-upload-alt fa-3x"></i>
                    <h3>اسحب صورة هنا أو انقر للاختيار</h3>
                    <p>Drag an image here or click to select</p>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImage(this.files)">
                </div>

                <div class="image-preview" id="imagePreview"></div>
                <div class="extracted-colors" id="extractedColors"></div>
            </div>
        </div>
    </main>

    <script>
        const commonColors = [
            '#ff0000', '#ff4d00', '#ff9900', '#ffcc00', '#ffff00', '#ccff00', 
            '#99ff00', '#4dff00', '#00ff00', '#00ff4d', '#00ff99', '#00ffcc',
            '#00ffff', '#00ccff', '#0099ff', '#004dff', '#0000ff', '#4d00ff',
            '#9900ff', '#cc00ff', '#ff00ff', '#ff00cc', '#ff0099', '#ff004d'
        ];

        function switchTab(tab) {
            document.querySelector('.tab.active').classList.remove('active');
            document.querySelector(`.tab[onclick="switchTab('${tab}')"]`).classList.add('active');
            document.getElementById('pickerTab').style.display = tab === 'picker' ? 'block' : 'none';
            document.getElementById('extractorTab').style.display = tab === 'extractor' ? 'block' : 'none';
        }

        function updateColor(color) {
            const display = document.getElementById('colorDisplay');
            display.style.backgroundColor = color;
            
            // Update color values
            document.getElementById('hexValue').textContent = color;
            
            // Convert to RGB
            const r = parseInt(color.slice(1,3), 16);
            const g = parseInt(color.slice(3,5), 16);
            const b = parseInt(color.slice(5,7), 16);
            document.getElementById('rgbValue').textContent = `rgb(${r},${g},${b})`;
            
            // Convert to HSL
            const [h, s, l] = rgbToHsl(r, g, b);
            document.getElementById('hslValue').textContent = 
                `hsl(${Math.round(h)},${Math.round(s)}%,${Math.round(l)}%)`;
        }

        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;
            const max = Math.max(r, g, b), min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }

            return [h * 360, s * 100, l * 100];
        }

        function copyColor(type) {
            const value = document.getElementById(`${type}Value`).textContent;
            navigator.clipboard.writeText(value);
            // Could add a toast notification here
        }

        function handleImage(files) {
            if (!files.length) return;
            
            const file = files[0];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    document.getElementById('imagePreview').innerHTML = `<img src="${e.target.result}" alt="Uploaded image">`;
                    extractColors(this);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function extractColors(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
            const colorCounts = {};
            
            for (let i = 0; i < imageData.length; i += 4) {
                const r = Math.round(imageData[i] / 16) * 16;
                const g = Math.round(imageData[i + 1] / 16) * 16;
                const b = Math.round(imageData[i + 2] / 16) * 16;
                const rgb = `rgb(${r},${g},${b})`;
                colorCounts[rgb] = (colorCounts[rgb] || 0) + 1;
            }
            
            const sortedColors = Object.entries(colorCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 12)
                .map(([color]) => color);
            
            displayExtractedColors(sortedColors);
        }

        function displayExtractedColors(colors) {
            const container = document.getElementById('extractedColors');
            container.innerHTML = colors.map(color => `
                <div class="color-swatch" style="background-color: ${color}" onclick="copyColor('${color}')">
                    <div class="color-code">${rgbToHex(color)}</div>
                </div>
            `).join('');
        }

        function rgbToHex(rgb) {
            const [r, g, b] = rgb.match(/\d+/g);
            return '#' + [r, g, b].map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
        }

        // Initialize color palette
        const palette = document.querySelector('.color-palette');
        commonColors.forEach(color => {
            const div = document.createElement('div');
            div.className = 'palette-color';
            div.style.backgroundColor = color;
            div.onclick = () => {
                document.getElementById('colorInput').value = color;
                updateColor(color);
            };
            palette.appendChild(div);
        });

        // Initialize with black
        updateColor('#000000');

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>