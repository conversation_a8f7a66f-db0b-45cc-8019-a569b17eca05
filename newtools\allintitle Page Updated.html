<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد كلمات مفتاحية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f9f9f9;
            margin: 0;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            resize: vertical;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            font-size: 16px;
            transition: background-color 0.3s, box-shadow 0.3s;
        }
        button.active {
            background-color: #0056b3;
            box-shadow: inset 0px 0px 5px rgba(0,0,0,0.3);
        }
        button:focus {
            outline: none;
        }
        .generated-text {
            margin-top: 20px;
        }
        .generated-text div {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0px 2px 5px rgba(0,0,0,0.1);
        }
        .generated-text button {
            margin-left: 10px;
        }
    </style>
</head>
<body>

    <h1>مولد كلمات مفتاحية</h1>
    <textarea id="keywords" placeholder="أدخل الكلمات المفتاحية هنا (كل كلمة في سطر جديد)"></textarea>
    <button onclick="generateText()">توليد</button>

    <div id="output" class="generated-text"></div>

    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            // Retrieve and apply the active button state from localStorage
            const activeButtonId = localStorage.getItem('activeButtonId');
            if (activeButtonId) {
                document.getElementById(activeButtonId)?.classList.add('active');
            }
        });

        function generateText() {
            const keywords = document.getElementById("keywords").value.split("\n").filter(k => k.trim() !== '');
            const outputDiv = document.getElementById("output");
            outputDiv.innerHTML = '';

            keywords.forEach((keyword, index) => {
                const formattedKeyword = `allintitle: ${keyword.trim()}`;
                const keywordDiv = document.createElement("div");

                const keywordText = document.createElement("span");
                keywordText.textContent = formattedKeyword;
                keywordDiv.appendChild(keywordText);

                const searchButton = document.createElement("button");
                searchButton.textContent = "بحث";
                searchButton.id = `searchButton${index}`;
                searchButton.onclick = () => {
                    window.open(`https://www.google.com/search?q=${encodeURIComponent(formattedKeyword)}`, '_blank');
                    localStorage.setItem('activeButtonId', searchButton.id); // Save the active button ID
                    searchButton.classList.add("active");
                };
                keywordDiv.appendChild(searchButton);

                const copyButton = document.createElement("button");
                copyButton.textContent = "نسخ";
                copyButton.onclick = () => {
                    navigator.clipboard.writeText(formattedKeyword).then(() => {
                        alert("تم نسخ النص!");
                    });
                };
                keywordDiv.appendChild(copyButton);

                outputDiv.appendChild(keywordDiv);
            });
        }
    </script>

</body>
</html>
