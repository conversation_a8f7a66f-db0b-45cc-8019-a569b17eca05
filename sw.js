// Service Worker for Useful Tools PWA
const CACHE_NAME = 'useful-tools-v1.0.0';
const urlsToCache = [
    '/',
    '/index.html',
    '/styles.css',
    '/enhanced-styles.css',
    '/scripts.js',
    '/site.webmanifest',
    '/favicon.ico',
    // Tool pages
    '/tool1.html',
    '/tool2.html',
    '/tool3.html',
    '/tool4.html',
    '/tool5.html',
    '/tool6.html',
    '/tool7.html',
    '/tool8.html',
    '/tool9.html',
    '/tool10.html',
    '/tool11.html',
    '/tool12.html',
    '/tool13.html',
    '/tool14.html',
    '/tool15.html',
    '/tool16.html',
    '/tool17.html',
    '/tool18.html',
    '/tool19.html',
    '/tool20.html',
    '/tool21.html',
    '/tool22.html',
    '/tool23.html',
    '/tool24.html',
    '/tool25.html',
    '/tool26.html',
    '/tool27.html',
    '/tool28.html',
    '/tool29.html',
    '/tool30.html',
    '/tool31.html',
    '/tool32.html',
    '/tool33.html',
    '/tool34.html',
    '/tool35.html',
    '/tool36.html',
    '/tool37.html',
    '/tool38.html',
    '/tool39.html',
    '/tool40.html',
    '/tool41.html',
    // External resources
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap'
];

// Install event
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Opened cache');
                return cache.addAll(urlsToCache);
            })
            .catch(error => {
                console.error('Failed to cache resources:', error);
            })
    );
});

// Fetch event
self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version or fetch from network
                if (response) {
                    return response;
                }
                
                return fetch(event.request).then(response => {
                    // Check if we received a valid response
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }

                    // Clone the response
                    const responseToCache = response.clone();

                    caches.open(CACHE_NAME)
                        .then(cache => {
                            cache.put(event.request, responseToCache);
                        });

                    return response;
                }).catch(() => {
                    // Return offline page for navigation requests
                    if (event.request.destination === 'document') {
                        return caches.match('/index.html');
                    }
                });
            })
    );
});

// Activate event
self.addEventListener('activate', event => {
    const cacheWhitelist = [CACHE_NAME];
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheWhitelist.indexOf(cacheName) === -1) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Background sync for offline functionality
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

function doBackgroundSync() {
    // Implement background sync logic here
    return Promise.resolve();
}

// Push notification handling
self.addEventListener('push', event => {
    const options = {
        body: event.data ? event.data.text() : 'New update available!',
        icon: '/android-chrome-192x192.png',
        badge: '/favicon-32x32.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'استكشف الأدوات',
                icon: '/icons/explore.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/icons/close.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('أدوات مفيدة', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Periodic background sync
self.addEventListener('periodicsync', event => {
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

function syncContent() {
    // Implement periodic content sync
    return fetch('/api/check-updates')
        .then(response => response.json())
        .then(data => {
            if (data.hasUpdates) {
                // Update cache with new content
                return updateCache();
            }
        })
        .catch(error => {
            console.error('Sync failed:', error);
        });
}

function updateCache() {
    return caches.open(CACHE_NAME)
        .then(cache => {
            return cache.addAll(urlsToCache);
        });
}
