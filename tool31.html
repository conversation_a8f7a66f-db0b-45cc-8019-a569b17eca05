<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة استهلاك الماء - Water Intake Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .calculator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .result {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            background-color: var(--secondary-bg);
            border-radius: 5px;
        }
        .result-value {
            font-size: 2em;
            color: var(--primary-color);
            margin: 10px 0;
        }
        .result i {
            font-size: 3em;
            color: #4a90e2;
            margin-bottom: 15px;
        }
        .recommendations {
            margin-top: 30px;
        }
        .recommendation-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .recommendation-item i {
            color: var(--primary-color);
            font-size: 1.5em;
        }
        .water-progress {
            margin-top: 20px;
            height: 20px;
            background-color: var(--secondary-bg);
            border-radius: 10px;
            overflow: hidden;
        }
        .water-level {
            height: 100%;
            background-color: #4a90e2;
            width: 0;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <header>
        <h1>حاسبة استهلاك الماء <span>Water Intake Calculator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="calculator">
            <div class="input-section">
                <div class="input-group">
                    <label for="weight">الوزن (كجم) - Weight (kg)</label>
                    <input type="number" id="weight" min="1" max="300" step="0.1" value="70">
                </div>
                <div class="input-group">
                    <label for="activity">مستوى النشاط - Activity Level</label>
                    <select id="activity">
                        <option value="sedentary">قليل النشاط - Sedentary</option>
                        <option value="moderate">نشاط متوسط - Moderate</option>
                        <option value="active">نشاط عالي - Active</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="climate">المناخ - Climate</label>
                    <select id="climate">
                        <option value="moderate">معتدل - Moderate</option>
                        <option value="hot">حار - Hot</option>
                        <option value="cold">بارد - Cold</option>
                    </select>
                </div>
            </div>

            <button onclick="calculateWaterIntake()" class="primary-button">
                <i class="fas fa-calculator"></i>
                <span class="ar">احسب</span>
                <span class="en">Calculate</span>
            </button>

            <div class="result" id="result" style="display: none;">
                <i class="fas fa-tint"></i>
                <h3>كمية الماء اليومية المطلوبة</h3>
                <h3>Daily Water Intake Required</h3>
                <div class="result-value" id="waterAmount"></div>
                <div class="water-progress">
                    <div class="water-level" id="waterLevel"></div>
                </div>
            </div>

            <div class="recommendations">
                <h3>نصائح للحفاظ على مستوى ترطيب جيد - Hydration Tips</h3>
                <div class="recommendation-item">
                    <i class="fas fa-clock"></i>
                    <div>اشرب كوباً من الماء عند الاستيقاظ - Drink a glass of water when you wake up</div>
                </div>
                <div class="recommendation-item">
                    <i class="fas fa-apple-alt"></i>
                    <div>تناول الفواكه والخضروات الغنية بالماء - Eat water-rich fruits and vegetables</div>
                </div>
                <div class="recommendation-item">
                    <i class="fas fa-running"></i>
                    <div>اشرب الماء قبل وأثناء وبعد التمارين - Drink water before, during, and after exercise</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function calculateWaterIntake() {
            const weight = parseFloat(document.getElementById('weight').value);
            const activity = document.getElementById('activity').value;
            const climate = document.getElementById('climate').value;

            if (!weight || weight <= 0) {
                alert('الرجاء إدخال وزن صحيح - Please enter a valid weight');
                return;
            }

            // Base calculation: 30ml per kg of body weight
            let waterAmount = weight * 30;

            // Activity level adjustments
            switch(activity) {
                case 'sedentary':
                    waterAmount *= 1;
                    break;
                case 'moderate':
                    waterAmount *= 1.2;
                    break;
                case 'active':
                    waterAmount *= 1.4;
                    break;
            }

            // Climate adjustments
            switch(climate) {
                case 'moderate':
                    waterAmount *= 1;
                    break;
                case 'hot':
                    waterAmount *= 1.2;
                    break;
                case 'cold':
                    waterAmount *= 0.9;
                    break;
            }

            // Convert to liters
            const liters = (waterAmount / 1000).toFixed(1);

            // Display results
            document.getElementById('result').style.display = 'block';
            document.getElementById('waterAmount').innerHTML = `
                <div class="ar">${liters} لتر</div>
                <div class="en">${liters} Liters</div>
            `;

            // Animate water level
            const waterLevel = document.getElementById('waterLevel');
            waterLevel.style.width = '0%';
            setTimeout(() => {
                waterLevel.style.width = '100%';
            }, 100);
        }

        // Calculate initial water intake
        calculateWaterIntake();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>