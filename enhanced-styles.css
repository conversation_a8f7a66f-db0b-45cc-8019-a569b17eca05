/* Enhanced Styles for Advanced Features */

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 44px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--card-hover-bg);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Enhanced Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-color);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    color: var(--text-color);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 175, 0.1);
}

/* Enhanced Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-color);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Enhanced Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-xl);
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Enhanced Alerts */
.alert {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--accent-color);
    color: var(--accent-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: var(--info-color);
}

/* Enhanced Progress Bars */
.progress {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Breadcrumbs */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--text-muted);
    margin-left: var(--spacing-sm);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.breadcrumb-item a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-content {
    text-align: center;
    color: var(--text-color);
}

.loading-content p {
    margin-top: var(--spacing-lg);
    font-size: var(--font-size-lg);
    font-weight: 500;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Tool Card Animations */
.tool-card {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition);
}

.tool-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .hero {
        min-height: 60vh;
        padding: var(--spacing-2xl) var(--spacing-md);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .stat-item {
        padding: var(--spacing-md);
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .back-to-top {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .hero {
        min-height: 50vh;
    }

    .search-box {
        padding: var(--spacing-md);
    }

    .tool-card {
        padding: var(--spacing-lg);
        min-height: 180px;
    }

    .breadcrumb {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    .main-header,
    .back-to-top,
    .loading-overlay,
    .search-container {
        display: none !important;
    }

    .hero {
        background: none !important;
        color: black !important;
        padding: var(--spacing-lg) 0;
    }

    .tool-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0066cc;
        --text-color: #000000;
        --bg-color: #ffffff;
        --border-color: #000000;
    }

    .tool-card {
        border: 2px solid var(--border-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero::before,
    .hero::after {
        animation: none;
    }
}



/* Tool Card External Link Icon */
.tool-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    margin-bottom: var(--spacing-md);
}

.tool-external-link {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    opacity: 0;
    transform: translateY(-10px);
}

.tool-card:hover .tool-external-link {
    opacity: 1;
    transform: translateY(0);
}

.tool-external-link:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.tool-external-link i {
    font-size: 0.9rem;
}

/* Enhanced Tool Card Layout */
.tool-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
    justify-content: center;
}

.tool-card-icon {
    margin-bottom: var(--spacing-lg);
}

.tool-card-info {
    text-align: center;
}

/* Grid and List View Toggle */
.tools-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.tools-grid.list-view .tool-card {
    flex-direction: row;
    text-align: left;
    min-height: 80px;
    padding: var(--spacing-lg);
}

.tools-grid.list-view .tool-card-header {
    margin-bottom: 0;
}

.tools-grid.list-view .tool-card-content {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-lg);
}

.tools-grid.list-view .tool-card-icon {
    margin-bottom: 0;
}

.tools-grid.list-view .tool-card i {
    font-size: 2rem;
}



/* Responsive Design for New Elements */
@media (max-width: 768px) {
    .tools-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-open-all,
    .btn-toggle-view {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .floating-actions {
        bottom: var(--spacing-lg);
        left: var(--spacing-lg);
    }

    .floating-btn {
        width: 48px;
        height: 48px;
        font-size: 1rem;
    }
}

/* Enhanced Tool Page Styles */
.tool-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

/* Tool Workspace Layout */
.tool-workspace {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-xl);
}

@media (min-width: 1024px) {
    .tool-workspace {
        grid-template-columns: 1fr 300px 1fr;
        grid-template-areas:
            "input controls output";
    }

    .input-section {
        grid-area: input;
    }

    .controls-section {
        grid-area: controls;
    }

    .output-section {
        grid-area: output;
    }
}

/* Section Styles */
.input-section,
.controls-section,
.output-section {
    background: var(--card-gradient);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: var(--transition);
}

.input-section:hover,
.controls-section:hover,
.output-section:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-2px);
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.section-header h3 i {
    font-size: 1.2rem;
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Button Icon Styles */
.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.btn-icon.success {
    background: var(--success-color);
}

.btn-icon.success:hover {
    background: var(--accent-dark);
}

/* Input and Output Groups */
.input-group,
.output-group {
    padding: var(--spacing-xl);
}

.input-group textarea,
.output-group textarea {
    width: 100%;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-color);
    color: var(--text-color);
    font-size: var(--font-size-base);
    font-family: inherit;
    resize: vertical;
    transition: var(--transition);
    line-height: 1.6;
}

.input-group textarea:focus,
.output-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.output-group textarea {
    background: var(--card-hover-bg);
    border-color: var(--success-color);
}

/* Stats Display */
.input-stats,
.output-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.stat i {
    color: var(--primary-color);
}

.stat.success {
    color: var(--success-color);
}

.stat.success i {
    color: var(--success-color);
}

/* Button Grid for Controls */
.button-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
}

.btn-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    text-align: center;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-action:active {
    transform: translateY(0);
}

/* Button Variants */
.btn-action.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-action.primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-action.secondary {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-action.secondary:hover {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
}

.btn-action.accent {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-action.accent:hover {
    background: linear-gradient(135deg, var(--accent-dark), var(--accent-color));
}

.btn-action.info {
    background: linear-gradient(135deg, var(--info-color), var(--blue-bright));
    color: white;
    box-shadow: var(--shadow);
}

.btn-action.info:hover {
    background: linear-gradient(135deg, #2563eb, var(--info-color));
}

.tool-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.tool-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    box-shadow: var(--shadow-lg);
    flex-shrink: 0;
}

.tool-info h1 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-color);
    font-size: var(--font-size-2xl);
    font-weight: 700;
}

.tool-info p {
    margin: 0;
    color: var(--text-light);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

/* Enhanced Form Styles for Tools */
.tool-container .input-group,
.tool-container .output-group {
    margin-bottom: var(--spacing-xl);
    background: var(--card-bg);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.tool-container .input-group label,
.tool-container .output-group label {
    display: block;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.tool-container .input-group textarea,
.tool-container .output-group textarea {
    width: 100%;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: var(--font-size-base);
    resize: vertical;
    min-height: 120px;
    transition: var(--transition);
    font-family: inherit;
}

.tool-container .input-group textarea:focus,
.tool-container .output-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 175, 0.1);
}

.tool-container .button-group {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    margin: var(--spacing-xl) 0;
    justify-content: center;
}

.tool-container .button-group button,
.tool-container .output-group button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    font-size: var(--font-size-base);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tool-container .button-group button::before,
.tool-container .output-group button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.tool-container .button-group button:hover::before,
.tool-container .output-group button:hover::before {
    left: 100%;
}

.tool-container .button-group button:hover,
.tool-container .output-group button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.tool-container .button-group button:active,
.tool-container .output-group button:active {
    transform: translateY(0);
}

/* Tool Footer */
.tool-footer {
    margin-top: var(--spacing-3xl);
    padding: var(--spacing-xl);
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    text-align: center;
}

.tool-footer a {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: var(--transition);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.tool-footer a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Responsive Design for Tools */
@media (max-width: 1024px) {
    .tool-workspace {
        grid-template-columns: 1fr;
        grid-template-areas:
            "input"
            "controls"
            "output";
    }
}

@media (max-width: 768px) {
    .tool-container {
        padding: var(--spacing-lg);
    }

    .tool-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }

    .tool-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .tool-info h1 {
        font-size: var(--font-size-xl);
    }

    .section-header {
        padding: var(--spacing-md) var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .section-header h3 {
        font-size: var(--font-size-base);
    }

    .input-group,
    .output-group,
    .button-grid {
        padding: var(--spacing-lg);
    }

    .input-stats,
    .output-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .btn-action {
        min-height: 48px;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .tool-container {
        padding: var(--spacing-md);
    }

    .tool-workspace {
        gap: var(--spacing-lg);
    }

    .section-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .input-group,
    .output-group,
    .button-grid {
        padding: var(--spacing-md);
    }

    .input-group textarea,
    .output-group textarea {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-action {
        padding: var(--spacing-md);
        min-height: 44px;
        gap: var(--spacing-sm);
    }

    .btn-icon {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
}

/* Dark Mode Enhancements for Tools */
.dark-mode .input-section,
.dark-mode .controls-section,
.dark-mode .output-section {
    background: linear-gradient(145deg, #2c3e50 0%, #34495e 100%);
    border-color: var(--border-color);
}

.dark-mode .section-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.dark-mode .input-group textarea,
.dark-mode .output-group textarea {
    background: var(--bg-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

.dark-mode .input-group textarea:focus,
.dark-mode .output-group textarea:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
}

/* Animation Enhancements */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.input-section {
    animation: slideInFromLeft 0.6s ease-out;
}

.controls-section {
    animation: slideInFromBottom 0.6s ease-out 0.2s both;
}

.output-section {
    animation: slideInFromRight 0.6s ease-out 0.4s both;
}

/* Success Animation */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.stat.success {
    animation: successPulse 0.6s ease-out;
}

/* Copy Button Success State */
.btn-icon.copied {
    background: var(--success-color) !important;
    animation: successPulse 0.6s ease-out;
}

/* Enhanced Stats Section */
.stats-section {
    background: var(--card-gradient);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: var(--transition);
}

.stats-section:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-2px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    width: 8px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex-grow: 1;
}

.stat-content h3 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.stat-content span {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-color);
    display: block;
}

/* Stat Card Variants */
.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-card.primary::before {
    background: var(--primary-color);
}

.stat-card.secondary .stat-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.stat-card.secondary::before {
    background: var(--secondary-color);
}

.stat-card.accent .stat-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.stat-card.accent::before {
    background: var(--accent-color);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, var(--info-color), var(--blue-bright));
}

.stat-card.info::before {
    background: var(--info-color);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), var(--green-bright));
}

.stat-card.success::before {
    background: var(--success-color);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), var(--yellow-bright));
}

.stat-card.warning::before {
    background: var(--warning-color);
}

/* Tool Workspace Layout for Stats */
@media (min-width: 1024px) {
    .tool-workspace {
        grid-template-columns: 1fr 1fr;
        grid-template-areas:
            "input stats";
    }

    .stats-section {
        grid-area: stats;
    }
}

/* Responsive Stats Grid */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-content span {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
        padding: var(--spacing-md);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .stat-card::before {
        width: 100%;
        height: 4px;
        top: 0;
        left: 0;
    }

    .stat-card:hover::before {
        height: 6px;
    }
}

/* Enhanced Floating Actions */
.floating-actions .floating-btn:nth-child(1) {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.floating-actions .floating-btn:nth-child(2) {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.floating-actions .floating-btn:nth-child(3) {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

/* Tool Card Hover Enhancements */
.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.tool-card:hover::before {
    opacity: 1;
}

/* Enhanced Search Results */
.search-results {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.search-result {
    transition: var(--transition);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-result:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(10px);
}

/* Enhanced Tab Buttons */
.tab-btn {
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition);
    transform-origin: left;
}

.tab-btn.active::before,
.tab-btn:hover::before {
    transform: scaleX(1);
}

/* Enhanced Hero Stats */
.stat-item {
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-slow);
}

.stat-item:hover::before {
    left: 100%;
}

/* Enhanced Loading Spinner */
.loading-spinner {
    border: 3px solid rgba(99, 102, 241, 0.3);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Tooltips */
[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* Enhanced Focus States */
.tool-card:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-action:focus-visible,
.btn-icon:focus-visible,
.floating-btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Enhanced Print Styles */
@media print {
    .floating-actions,
    .tools-actions,
    .section-actions {
        display: none !important;
    }

    .tool-workspace {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg) !important;
    }

    .section-header {
        background: none !important;
        color: black !important;
        border-bottom: 2px solid #000 !important;
    }
}
