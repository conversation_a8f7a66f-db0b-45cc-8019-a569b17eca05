<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استخراج الإيميلات من النص</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            padding: 10px;
            background-color: #f4f4f9;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            font-size: 16px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #fff;
        }
        button {
            display: block;
            width: 200px;
            margin: 10px auto;
            padding: 10px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #emailsList {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #fff;
            list-style-type: none;
        }
    </style>
</head>
<body>

    <h1>استخراج الإيميلات من النص</h1>
    <textarea id="inputText" placeholder="أدخل النص هنا..."></textarea>
    <button onclick="extractEmails()">استخراج الإيميلات</button>

    <ul id="emailsList"></ul>
    <button onclick="copyEmails()">نسخ الإيميلات</button>
    <button onclick="exportToTxt()">تصدير إلى ملف نصي</button>
    <button onclick="exportToExcel()">تصدير إلى ملف Excel</button>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>

    <script>
        var emails = [];

        function extractEmails() {
            // الحصول على النص من مربع النص
            var text = document.getElementById('inputText').value;

            // التعبير المنتظم لاستخراج الإيميلات
            var emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;

            // البحث عن الإيميلات في النص
            emails = text.match(emailPattern);

            // إذا تم العثور على إيميلات
            var emailList = document.getElementById('emailsList');
            emailList.innerHTML = ''; // مسح القائمة القديمة

            if (emails) {
                emails.forEach(function(email) {
                    var listItem = document.createElement('li');
                    listItem.textContent = email;
                    emailList.appendChild(listItem);
                });
            } else {
                // إذا لم يتم العثور على أي إيميل
                var listItem = document.createElement('li');
                listItem.textContent = 'لا يوجد إيميلات في النص';
                emailList.appendChild(listItem);
            }
        }

        function copyEmails() {
            // نسخ الإيميلات إلى الحافظة
            var emailText = emails.join('\n');
            navigator.clipboard.writeText(emailText)
                .then(() => alert('تم نسخ الإيميلات إلى الحافظة'))
                .catch(err => alert('حدث خطأ في النسخ: ' + err));
        }

        function exportToTxt() {
            // تصدير الإيميلات إلى ملف نصي
            var emailText = emails.join('\n');
            var blob = new Blob([emailText], { type: 'text/plain' });
            saveAs(blob, 'emails.txt');
        }

        function exportToExcel() {
            // تصدير الإيميلات إلى ملف Excel
            var emailText = 'الإيميل\n' + emails.join('\n');
            var blob = new Blob([emailText], { type: 'application/vnd.ms-excel' });
            saveAs(blob, 'emails.xls');
        }
    </script>

</body>
</html>
