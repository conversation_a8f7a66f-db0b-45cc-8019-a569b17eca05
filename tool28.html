<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محلل الألوان من الصور - Image Color Analyzer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .analyzer {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: var(--primary-color);
        }
        .upload-area i {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        .preview-area {
            margin: 20px 0;
            text-align: center;
        }
        .preview-area img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 5px;
        }
        .colors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .color-card {
            padding: 10px;
            border-radius: 5px;
            background-color: var(--secondary-bg);
            text-align: center;
        }
        .color-preview {
            width: 100%;
            height: 100px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .color-info {
            font-family: monospace;
            color: var(--text-color);
        }
        .copy-button {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            padding: 5px;
            margin-right: 5px;
        }
        .copy-button:hover {
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>محلل الألوان من الصور <span>Image Color Analyzer</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="analyzer">
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>اسحب صورة هنا أو انقر للاختيار</h3>
                <p>Drag an image here or click to select</p>
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
            </div>

            <div class="preview-area" id="previewArea" style="display: none;">
                <img id="imagePreview" alt="Preview">
            </div>

            <div class="colors-grid" id="colorsGrid"></div>
        </div>
    </main>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const previewArea = document.getElementById('previewArea');
        const imagePreview = document.getElementById('imagePreview');
        const colorsGrid = document.getElementById('colorsGrid');

        // Handle drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--primary-color)';
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = 'var(--border-color)';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--border-color)';
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                processImage(file);
            }
        });

        uploadArea.addEventListener('click', () => {
            imageInput.click();
        });

        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                processImage(file);
            }
        });

        function processImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                previewArea.style.display = 'block';
                
                // Create a temporary image to analyze colors
                const img = new Image();
                img.onload = function() {
                    analyzeColors(img);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function analyzeColors(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Resize canvas to reasonable size for analysis
            const maxSize = 150;
            const scale = Math.min(maxSize / img.width, maxSize / img.height);
            canvas.width = img.width * scale;
            canvas.height = img.height * scale;
            
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;
            
            // Count color occurrences
            const colorCounts = {};
            for (let i = 0; i < pixels.length; i += 4) {
                const r = Math.round(pixels[i] / 16) * 16;
                const g = Math.round(pixels[i + 1] / 16) * 16;
                const b = Math.round(pixels[i + 2] / 16) * 16;
                const rgb = `rgb(${r},${g},${b})`;
                colorCounts[rgb] = (colorCounts[rgb] || 0) + 1;
            }
            
            // Sort colors by frequency
            const sortedColors = Object.entries(colorCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 6)
                .map(([color]) => color);
            
            displayColors(sortedColors);
        }

        function displayColors(colors) {
            colorsGrid.innerHTML = '';
            colors.forEach(color => {
                const hex = rgbToHex(color);
                const card = document.createElement('div');
                card.className = 'color-card';
                card.innerHTML = `
                    <div class="color-preview" style="background-color: ${color}"></div>
                    <div class="color-info">
                        <button class="copy-button" onclick="copyToClipboard('${hex}')">
                            <i class="fas fa-copy"></i>
                        </button>
                        ${hex}
                    </div>
                `;
                colorsGrid.appendChild(card);
            });
        }

        function rgbToHex(rgb) {
            const match = rgb.match(/^rgb\((\d+),(\d+),(\d+)\)$/);
            if (!match) return rgb;
            
            const r = parseInt(match[1]);
            const g = parseInt(match[2]);
            const b = parseInt(match[3]);
            
            return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Could add a toast notification here
            });
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>