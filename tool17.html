<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الوقت الحالي - Current Time Display</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .clock-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .digital-clock {
            background-color: var(--card-bg);
            padding: 30px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow);
        }
        .time-display {
            font-size: 3.5rem;
            font-family: monospace;
            color: var(--primary-color);
            margin: 20px 0;
        }
        .date-display {
            font-size: 1.2rem;
            color: var(--text-color);
            margin: 10px 0;
        }
        .timezone-display {
            color: var(--secondary-color);
            margin-top: 10px;
        }
        .timezone-select {
            width: 100%;
            padding: 10px;
            margin-top: 15px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .time-formats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .format-box {
            background-color: var(--bg-color);
            padding: 15px;
            border-radius: var(--border-radius);
            text-align: center;
        }
        .format-label {
            color: var(--secondary-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .format-value {
            color: var(--text-color);
            font-family: monospace;
        }
        .timezone-info {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .info-item {
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            text-align: center;
        }
    </style>
</head>
<body>
    <header>
        <h1>عرض الوقت الحالي <span>Current Time Display</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="clock-container">
            <div class="digital-clock">
                <div class="time-display" id="timeDisplay">00:00:00</div>
                <div class="date-display" id="dateDisplay"></div>
                <div class="timezone-display" id="timezoneDisplay"></div>
                <select id="timezoneSelect" class="timezone-select">
                    <option value="Africa/Cairo">القاهرة / Cairo (UTC+2)</option>
                    <option value="Asia/Riyadh">الرياض / Riyadh (UTC+3)</option>
                    <option value="Asia/Dubai">دبي / Dubai (UTC+4)</option>
                    <option value="Europe/London">لندن / London (UTC+1)</option>
                    <option value="Europe/Paris">باريس / Paris (UTC+2)</option>
                    <option value="America/New_York">نيويورك / New York (UTC-4)</option>
                    <option value="Asia/Tokyo">طوكيو / Tokyo (UTC+9)</option>
                    <option value="Australia/Sydney">سيدني / Sydney (UTC+10)</option>
                </select>
            </div>

            <div class="digital-clock">
                <div class="time-display" id="localTimeDisplay">00:00:00</div>
                <div class="date-display" id="localDateDisplay"></div>
                <div class="timezone-display" id="localTimezoneDisplay"></div>
                <div class="ar">التوقيت المحلي</div>
                <div class="en">Local Time</div>
            </div>
        </div>

        <div class="time-formats">
            <div class="format-box">
                <div class="format-label ar">التنسيق 12 ساعة</div>
                <div class="format-label en">12-hour Format</div>
                <div class="format-value" id="format12"></div>
            </div>

            <div class="format-box">
                <div class="format-label ar">التنسيق 24 ساعة</div>
                <div class="format-label en">24-hour Format</div>
                <div class="format-value" id="format24"></div>
            </div>

            <div class="format-box">
                <div class="format-label ar">التنسيق ISO</div>
                <div class="format-label en">ISO Format</div>
                <div class="format-value" id="formatISO"></div>
            </div>

            <div class="format-box">
                <div class="format-label ar">التنسيق UTC</div>
                <div class="format-label en">UTC Format</div>
                <div class="format-value" id="formatUTC"></div>
            </div>
        </div>

        <div class="timezone-info">
            <div class="info-grid">
                <div class="info-item">
                    <div class="ar">فارق التوقيت عن GMT</div>
                    <div class="en">GMT Offset</div>
                    <div id="gmtOffset"></div>
                </div>
                <div class="info-item">
                    <div class="ar">التوقيت الصيفي</div>
                    <div class="en">Daylight Saving</div>
                    <div id="dstInfo"></div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const timeDisplay = document.getElementById('timeDisplay');
            const dateDisplay = document.getElementById('dateDisplay');
            const timezoneDisplay = document.getElementById('timezoneDisplay');
            const timezoneSelect = document.getElementById('timezoneSelect');
            const localTimeDisplay = document.getElementById('localTimeDisplay');
            const localDateDisplay = document.getElementById('localDateDisplay');
            const localTimezoneDisplay = document.getElementById('localTimezoneDisplay');
            const format12 = document.getElementById('format12');
            const format24 = document.getElementById('format24');
            const formatISO = document.getElementById('formatISO');
            const formatUTC = document.getElementById('formatUTC');
            const gmtOffset = document.getElementById('gmtOffset');
            const dstInfo = document.getElementById('dstInfo');

            // Load saved timezone
            const savedTimezone = localStorage.getItem('currentTime_timezone');
            if (savedTimezone) {
                timezoneSelect.value = savedTimezone;
            }

            function updateTime() {
                const now = new Date();
                const options = {
                    timeZone: timezoneSelect.value,
                    hour12: true,
                    hour: 'numeric',
                    minute: 'numeric',
                    second: 'numeric'
                };

                // Selected timezone time
                timeDisplay.textContent = now.toLocaleTimeString(undefined, options);
                dateDisplay.textContent = now.toLocaleDateString(undefined, {
                    timeZone: timezoneSelect.value,
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                timezoneDisplay.textContent = timezoneSelect.value.replace('_', ' ');

                // Local time
                localTimeDisplay.textContent = now.toLocaleTimeString();
                localDateDisplay.textContent = now.toLocaleDateString(undefined, {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                localTimezoneDisplay.textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;

                // Different formats
                format12.textContent = now.toLocaleTimeString(undefined, { hour12: true });
                format24.textContent = now.toLocaleTimeString(undefined, { hour12: false });
                formatISO.textContent = now.toISOString();
                formatUTC.textContent = now.toUTCString();

                // Timezone info
                const offset = -(now.getTimezoneOffset() / 60);
                const offsetStr = offset >= 0 ? `+${offset}` : `${offset}`;
                gmtOffset.textContent = `GMT${offsetStr}`;

                const isDST = now.getTimezoneOffset() < Math.max(
                    new Date(now.getFullYear(), 0, 1).getTimezoneOffset(),
                    new Date(now.getFullYear(), 6, 1).getTimezoneOffset()
                );
                dstInfo.textContent = isDST ? '✓' : '✗';
            }

            // Update every second
            updateTime();
            setInterval(updateTime, 1000);

            // Save timezone preference
            timezoneSelect.addEventListener('change', () => {
                localStorage.setItem('currentTime_timezone', timezoneSelect.value);
                updateTime();
            });
        });
    </script>
</body>
</html>