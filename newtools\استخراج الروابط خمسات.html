<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Extract Links Tool</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    .result-box {
      background-color: #fff;
      border: 1px solid #dee2e6;
      padding: 10px;
      border-radius: 5px;
      height: 150px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="text-center mb-4">Extract Links Tool</h1>
    <div class="row">
      <!-- Input Section -->
      <div class="col-md-6">
        <label for="inputHtml" class="form-label">Enter HTML Code:</label>
        <textarea id="inputHtml" class="form-control" rows="10" placeholder="Paste your HTML code here..."></textarea>
      </div>

      <!-- Output Section -->
      <div class="col-md-6">
        <label for="outputLinks" class="form-label">Extracted Links:</label>
        <div id="outputLinks" class="result-box"></div>
        <button id="extractBtn" class="btn btn-primary mt-3 w-100">Extract Links</button>
        <div class="d-flex justify-content-between mt-3">
          <button id="copyBtn" class="btn btn-secondary">Copy to Clipboard</button>
          <button id="downloadBtn" class="btn btn-success">Download as .txt</button>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    document.getElementById('extractBtn').addEventListener('click', function () {
      const inputHtml = document.getElementById('inputHtml').value;
      const parser = new DOMParser();
      const doc = parser.parseFromString(inputHtml, 'text/html');
      const links = doc.querySelectorAll('a');
      const outputBox = document.getElementById('outputLinks');
      
      outputBox.innerHTML = ''; // Clear previous results
      const extractedLinks = [];

      if (links.length > 0) {
        links.forEach(link => {
          const href = link.getAttribute('href');
          if (href) {
            const linkElement = document.createElement('div');
            linkElement.textContent = href;
            outputBox.appendChild(linkElement);
            extractedLinks.push(href);
          }
        });
      } else {
        outputBox.textContent = 'No links found!';
      }

      // Store the extracted links globally for use in other buttons
      window.extractedLinks = extractedLinks;
    });

    // Copy to Clipboard
    document.getElementById('copyBtn').addEventListener('click', function () {
      const links = window.extractedLinks || [];
      if (links.length > 0) {
        navigator.clipboard.writeText(links.join('\n')).then(() => {
          alert('Links copied to clipboard!');
        }).catch(err => {
          alert('Failed to copy links: ' + err);
        });
      } else {
        alert('No links to copy!');
      }
    });

    // Download as .txt
    document.getElementById('downloadBtn').addEventListener('click', function () {
      const links = window.extractedLinks || [];
      if (links.length > 0) {
        const blob = new Blob([links.join('\n')], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'extracted_links.txt';
        a.click();
        URL.revokeObjectURL(url);
      } else {
        alert('No links to download!');
      }
    });
  </script>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
