// Modern Tools Website JavaScript

// Tools Data
const toolsData = [
    // Text Tools
    {
        id: 'text-case-converter',
        title: { ar: 'محول حالة النصوص', en: 'Text Case Converter' },
        description: { ar: 'تحويل النصوص إلى حروف كبيرة/صغيرة/كل كلمة بحرف كبير', en: 'Convert text to uppercase, lowercase, or title case' },
        category: 'text',
        icon: 'fas fa-font',
        url: 'tool1.html',
        tags: { ar: ['نص', 'تحويل', 'حروف'], en: ['text', 'convert', 'case'] }
    },
    {
        id: 'word-counter',
        title: { ar: 'عدّاد الكلمات والحروف', en: 'Word & Character Counter' },
        description: { ar: 'حساب عدد الكلمات والحروف والمسافات', en: 'Count words, characters, and spaces in text' },
        category: 'text',
        icon: 'fas fa-calculator',
        url: 'tool2.html',
        tags: { ar: ['عداد', 'كلمات', 'حروف'], en: ['counter', 'words', 'characters'] }
    },
    {
        id: 'random-text-generator',
        title: { ar: 'مولد نصوص عشوائية', en: 'Random Text Generator' },
        description: { ar: 'إنشاء نصوص عشوائية بطول محدد', en: 'Generate random text with specified length' },
        category: 'text',
        icon: 'fas fa-dice',
        url: 'tool3.html',
        tags: { ar: ['عشوائي', 'نص', 'مولد'], en: ['random', 'text', 'generator'] }
    },
    {
        id: 'text-reverser',
        title: { ar: 'عكس النصوص', en: 'Text Reverser' },
        description: { ar: 'قلب ترتيب الحروف أو الكلمات في النص', en: 'Reverse the order of characters or words in text' },
        category: 'text',
        icon: 'fas fa-exchange-alt',
        url: 'tool4.html',
        tags: { ar: ['عكس', 'نص', 'ترتيب'], en: ['reverse', 'text', 'order'] }
    },
    
    // Email Tools
    {
        id: 'email-validator',
        title: { ar: 'مدقق تنسيق الإيميل', en: 'Email Format Validator' },
        description: { ar: 'التحقق من صحة تنسيق البريد الإلكتروني', en: 'Validate email address format' },
        category: 'email',
        icon: 'fas fa-check-circle',
        url: 'tool5.html',
        tags: { ar: ['إيميل', 'تحقق', 'تنسيق'], en: ['email', 'validate', 'format'] }
    },
    {
        id: 'email-extractor',
        title: { ar: 'مستخرج الإيميلات', en: 'Email Extractor' },
        description: { ar: 'استخراج عناوين البريد من النص', en: 'Extract email addresses from text' },
        category: 'email',
        icon: 'fas fa-search',
        url: 'tool6.html',
        tags: { ar: ['استخراج', 'إيميل', 'نص'], en: ['extract', 'email', 'text'] }
    },
    
    // Image Tools
    {
        id: 'image-resizer',
        title: { ar: 'تغيير حجم الصور', en: 'Image Resizer' },
        description: { ar: 'تكبير أو تصغير صورة معاينة', en: 'Resize images to different dimensions' },
        category: 'image',
        icon: 'fas fa-compress-arrows-alt',
        url: 'tool8.html',
        tags: { ar: ['صورة', 'حجم', 'تغيير'], en: ['image', 'resize', 'dimensions'] }
    },
    {
        id: 'image-grayscale',
        title: { ar: 'تحويل الصور إلى رمادي', en: 'Image to Grayscale' },
        description: { ar: 'تطبيق فلتر Grayscale على الصور', en: 'Apply grayscale filter to images' },
        category: 'image',
        icon: 'fas fa-adjust',
        url: 'tool9.html',
        tags: { ar: ['صورة', 'رمادي', 'فلتر'], en: ['image', 'grayscale', 'filter'] }
    },
    
    // Number Tools
    {
        id: 'currency-converter',
        title: { ar: 'محول العملات', en: 'Currency Converter' },
        description: { ar: 'تحويل بين العملات المختلفة', en: 'Convert between different currencies' },
        category: 'number',
        icon: 'fas fa-dollar-sign',
        url: 'tool12.html',
        tags: { ar: ['عملة', 'تحويل', 'مال'], en: ['currency', 'convert', 'money'] }
    },
    {
        id: 'calculator',
        title: { ar: 'آلة حاسبة', en: 'Calculator' },
        description: { ar: 'عمليات حسابية بسيطة', en: 'Simple mathematical calculations' },
        category: 'number',
        icon: 'fas fa-calculator',
        url: 'tool13.html',
        tags: { ar: ['حاسبة', 'رياضيات', 'حساب'], en: ['calculator', 'math', 'calculation'] }
    },
    
    // Time Tools
    {
        id: 'time-difference',
        title: { ar: 'حاسبة الفرق الزمني', en: 'Time Difference Calculator' },
        description: { ar: 'حساب الفارق بين وقتين', en: 'Calculate the difference between two times' },
        category: 'time',
        icon: 'fas fa-hourglass-half',
        url: 'tool16.html',
        tags: { ar: ['وقت', 'فرق', 'حساب'], en: ['time', 'difference', 'calculate'] }
    },
    {
        id: 'current-time',
        title: { ar: 'عرض الوقت الحالي', en: 'Current Time Display' },
        description: { ar: 'ساعة رقمية حسب المنطقة الزمنية', en: 'Digital clock by timezone' },
        category: 'time',
        icon: 'fas fa-clock',
        url: 'tool17.html',
        tags: { ar: ['ساعة', 'وقت', 'منطقة'], en: ['clock', 'time', 'timezone'] }
    },
    
    // Daily Tools
    {
        id: 'budget-calculator',
        title: { ar: 'حاسبة الميزانية', en: 'Budget Calculator' },
        description: { ar: 'إدخال المصروفات والدخل لحساب الرصيد بسرعة', en: 'Track expenses and income to calculate balance' },
        category: 'daily',
        icon: 'fas fa-wallet',
        url: 'tool21.html',
        tags: { ar: ['ميزانية', 'مال', 'حساب'], en: ['budget', 'money', 'finance'] }
    },
    {
        id: 'todo-list',
        title: { ar: 'قائمة المهام', en: 'Todo List' },
        description: { ar: 'إضافة وحذف المهام اليومية مع حفظها في المتصفح', en: 'Add and manage daily tasks with browser storage' },
        category: 'daily',
        icon: 'fas fa-tasks',
        url: 'tool23.html',
        tags: { ar: ['مهام', 'قائمة', 'تنظيم'], en: ['tasks', 'list', 'organize'] }
    },
    
    // Developer Tools
    {
        id: 'color-picker',
        title: { ar: 'منتقي وملتقط الألوان', en: 'Color Picker & Extractor' },
        description: { ar: 'انتقاء الألوان واستخراجها من الصور مع أكوادها', en: 'Pick colors and extract them from images with codes' },
        category: 'developer',
        icon: 'fas fa-eye-dropper',
        url: 'tool38.html',
        tags: { ar: ['ألوان', 'انتقاء', 'كود'], en: ['colors', 'picker', 'code'] }
    },
    {
        id: 'markdown-converter',
        title: { ar: 'محول مارك داون', en: 'Markdown Converter' },
        description: { ar: 'تحويل نص المارك داون إلى HTML مع معاينة مباشرة', en: 'Convert Markdown text to HTML with live preview' },
        category: 'developer',
        icon: 'fab fa-markdown',
        url: 'tool39.html',
        tags: { ar: ['مارك داون', 'HTML', 'تحويل'], en: ['markdown', 'HTML', 'convert'] }
    }
];

// Application State
let currentLanguage = 'ar';
let currentTheme = 'light';
let currentCategory = 'all';
let currentView = 'grid';

// DOM Elements
const header = document.getElementById('header');
const searchInput = document.getElementById('searchInput');
const searchInputEn = document.getElementById('searchInputEn');
const searchResults = document.getElementById('searchResults');
const navTabs = document.getElementById('navTabs');
const toolsGrid = document.getElementById('toolsGrid');
const themeToggle = document.getElementById('themeToggle');
const langToggle = document.getElementById('langToggle');
const openAllTools = document.getElementById('openAllTools');
const toggleView = document.getElementById('toggleView');
const backToTop = document.getElementById('backToTop');

// Initialize Application
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    loadTools();
    setupEventListeners();
    setupScrollEffects();
});

// Initialize App
function initializeApp() {
    // Load saved preferences
    currentLanguage = localStorage.getItem('language') || 'ar';
    currentTheme = localStorage.getItem('theme') || 'light';
    currentView = localStorage.getItem('view') || 'grid';
    
    // Apply preferences
    updateLanguage();
    updateTheme();
    updateView();
}

// Setup Event Listeners
function setupEventListeners() {
    // Theme toggle
    themeToggle.addEventListener('click', toggleTheme);
    
    // Language toggle
    langToggle.addEventListener('click', toggleLanguage);
    
    // Search
    searchInput.addEventListener('input', handleSearch);
    searchInputEn.addEventListener('input', handleSearch);
    
    // Navigation tabs
    navTabs.addEventListener('click', handleTabClick);
    
    // Action buttons
    openAllTools.addEventListener('click', handleOpenAllTools);
    toggleView.addEventListener('click', handleToggleView);
    
    // Back to top
    backToTop.addEventListener('click', scrollToTop);
    
    // Close search results when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.header-search')) {
            hideSearchResults();
        }
    });
}

// Setup Scroll Effects
function setupScrollEffects() {
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        // Header scroll effect
        if (currentScrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Back to top button
        if (currentScrollY > 500) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
        
        lastScrollY = currentScrollY;
    });
}

// Load Tools
function loadTools() {
    const filteredTools = currentCategory === 'all' 
        ? toolsData 
        : toolsData.filter(tool => tool.category === currentCategory);
    
    toolsGrid.innerHTML = filteredTools.map(tool => createToolCard(tool)).join('');
    
    // Add staggered animation
    const cards = toolsGrid.querySelectorAll('.tool-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
    });
}

// Create Tool Card
function createToolCard(tool) {
    const title = tool.title[currentLanguage];
    const description = tool.description[currentLanguage];
    const tags = tool.tags[currentLanguage];
    
    return `
        <div class="tool-card" data-category="${tool.category}" onclick="location.href='${tool.url}'">
            <div class="tool-card-header">
                <div class="tool-icon">
                    <i class="${tool.icon}"></i>
                </div>
                <button class="tool-external-btn" onclick="event.stopPropagation(); openInNewTab('${tool.url}')" title="فتح في تبويب جديد">
                    <i class="fas fa-external-link-alt"></i>
                </button>
            </div>
            
            <h3 class="tool-title">${title}</h3>
            <p class="tool-description">${description}</p>
            
            <div class="tool-tags">
                ${tags.map(tag => `<span class="tool-tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
}

// Handle Search
function handleSearch(e) {
    const query = e.target.value.toLowerCase().trim();
    
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    const results = toolsData.filter(tool => {
        const title = tool.title[currentLanguage].toLowerCase();
        const description = tool.description[currentLanguage].toLowerCase();
        const tags = tool.tags[currentLanguage].join(' ').toLowerCase();
        
        return title.includes(query) || description.includes(query) || tags.includes(query);
    });
    
    displaySearchResults(results);
}

// Display Search Results
function displaySearchResults(results) {
    if (results.length === 0) {
        searchResults.innerHTML = `
            <div class="search-result">
                <h4>${currentLanguage === 'ar' ? 'لا توجد نتائج' : 'No results found'}</h4>
            </div>
        `;
    } else {
        searchResults.innerHTML = results.map(tool => `
            <div class="search-result" onclick="location.href='${tool.url}'">
                <h4>${tool.title[currentLanguage]}</h4>
                <p>${tool.description[currentLanguage]}</p>
            </div>
        `).join('');
    }
    
    searchResults.classList.add('active');
}

// Hide Search Results
function hideSearchResults() {
    searchResults.classList.remove('active');
    searchInput.value = '';
    searchInputEn.value = '';
}

// Handle Tab Click
function handleTabClick(e) {
    const tab = e.target.closest('.nav-tab');
    if (!tab) return;
    
    // Update active tab
    navTabs.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
    tab.classList.add('active');
    
    // Update category
    currentCategory = tab.dataset.category;
    
    // Reload tools
    loadTools();
}

// Toggle Theme
function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    updateTheme();
    localStorage.setItem('theme', currentTheme);
}

// Update Theme
function updateTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    themeToggle.classList.toggle('dark', currentTheme === 'dark');
}

// Toggle Language
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    updateLanguage();
    localStorage.setItem('language', currentLanguage);
}

// Update Language
function updateLanguage() {
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    
    // Show/hide language-specific elements
    document.querySelectorAll('.ar').forEach(el => {
        el.style.display = currentLanguage === 'ar' ? '' : 'none';
    });
    
    document.querySelectorAll('.en').forEach(el => {
        el.style.display = currentLanguage === 'en' ? '' : 'none';
    });
    
    // Reload tools with new language
    loadTools();
}

// Handle Open All Tools
function handleOpenAllTools() {
    const confirmMessage = currentLanguage === 'ar' 
        ? `هل تريد فتح ${toolsData.length} أداة في تبويبات منفصلة؟`
        : `Do you want to open ${toolsData.length} tools in separate tabs?`;
    
    if (confirm(confirmMessage)) {
        toolsData.forEach((tool, index) => {
            setTimeout(() => {
                window.open(tool.url, '_blank');
            }, index * 100);
        });
    }
}

// Handle Toggle View
function handleToggleView() {
    currentView = currentView === 'grid' ? 'list' : 'grid';
    updateView();
    localStorage.setItem('view', currentView);
}

// Update View
function updateView() {
    const icon = toggleView.querySelector('i');
    const arText = toggleView.querySelector('.ar');
    const enText = toggleView.querySelector('.en');
    
    if (currentView === 'list') {
        toolsGrid.style.gridTemplateColumns = '1fr';
        icon.className = 'fas fa-th-large';
        arText.textContent = 'الشبكة';
        enText.textContent = 'Grid';
    } else {
        toolsGrid.style.gridTemplateColumns = '';
        icon.className = 'fas fa-list';
        arText.textContent = 'القائمة';
        enText.textContent = 'List';
    }
}

// Open in New Tab
function openInNewTab(url) {
    window.open(url, '_blank');
}

// Scroll to Top
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
