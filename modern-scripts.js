// Modern Tools Website JavaScript

// Tools Data
const toolsData = [
    // Text Tools
    {
        id: 'text-case-converter',
        title: { ar: 'محول حالة النصوص', en: 'Text Case Converter' },
        description: { ar: 'تحويل النصوص إلى حروف كبيرة/صغيرة/كل كلمة بحرف كبير', en: 'Convert text to uppercase, lowercase, or title case' },
        category: 'text',
        icon: 'fas fa-font',
        url: 'tool1.html',
        tags: { ar: ['نص', 'تحويل', 'حروف'], en: ['text', 'convert', 'case'] }
    },
    {
        id: 'word-counter',
        title: { ar: 'عدّاد الكلمات والحروف', en: 'Word & Character Counter' },
        description: { ar: 'حساب عدد الكلمات والحروف والمسافات', en: 'Count words, characters, and spaces in text' },
        category: 'text',
        icon: 'fas fa-calculator',
        url: 'tool2.html',
        tags: { ar: ['عداد', 'كلمات', 'حروف'], en: ['counter', 'words', 'characters'] }
    },
    {
        id: 'random-text-generator',
        title: { ar: 'مولد نصوص عشوائية', en: 'Random Text Generator' },
        description: { ar: 'إنشاء نصوص عشوائية بطول محدد', en: 'Generate random text with specified length' },
        category: 'text',
        icon: 'fas fa-dice',
        url: 'tool3.html',
        tags: { ar: ['عشوائي', 'نص', 'مولد'], en: ['random', 'text', 'generator'] }
    },
    {
        id: 'text-reverser',
        title: { ar: 'عكس النصوص', en: 'Text Reverser' },
        description: { ar: 'قلب ترتيب الحروف أو الكلمات في النص', en: 'Reverse the order of characters or words in text' },
        category: 'text',
        icon: 'fas fa-exchange-alt',
        url: 'tool4.html',
        tags: { ar: ['عكس', 'نص', 'ترتيب'], en: ['reverse', 'text', 'order'] }
    },
    
    // Email Tools
    {
        id: 'email-validator',
        title: { ar: 'مدقق تنسيق الإيميل', en: 'Email Format Validator' },
        description: { ar: 'التحقق من صحة تنسيق البريد الإلكتروني', en: 'Validate email address format' },
        category: 'email',
        icon: 'fas fa-check-circle',
        url: 'tool5.html',
        tags: { ar: ['إيميل', 'تحقق', 'تنسيق'], en: ['email', 'validate', 'format'] }
    },
    {
        id: 'email-extractor',
        title: { ar: 'مستخرج الإيميلات', en: 'Email Extractor' },
        description: { ar: 'استخراج عناوين البريد من النص', en: 'Extract email addresses from text' },
        category: 'email',
        icon: 'fas fa-search',
        url: 'tool6.html',
        tags: { ar: ['استخراج', 'إيميل', 'نص'], en: ['extract', 'email', 'text'] }
    },
    
    // Image Tools
    {
        id: 'image-resizer',
        title: { ar: 'تغيير حجم الصور', en: 'Image Resizer' },
        description: { ar: 'تكبير أو تصغير صورة معاينة', en: 'Resize images to different dimensions' },
        category: 'image',
        icon: 'fas fa-compress-arrows-alt',
        url: 'tool8.html',
        tags: { ar: ['صورة', 'حجم', 'تغيير'], en: ['image', 'resize', 'dimensions'] }
    },
    {
        id: 'image-grayscale',
        title: { ar: 'تحويل الصور إلى رمادي', en: 'Image to Grayscale' },
        description: { ar: 'تطبيق فلتر Grayscale على الصور', en: 'Apply grayscale filter to images' },
        category: 'image',
        icon: 'fas fa-adjust',
        url: 'tool9.html',
        tags: { ar: ['صورة', 'رمادي', 'فلتر'], en: ['image', 'grayscale', 'filter'] }
    },
    
    // Number Tools
    {
        id: 'currency-converter',
        title: { ar: 'محول العملات', en: 'Currency Converter' },
        description: { ar: 'تحويل بين العملات المختلفة', en: 'Convert between different currencies' },
        category: 'number',
        icon: 'fas fa-dollar-sign',
        url: 'tool12.html',
        tags: { ar: ['عملة', 'تحويل', 'مال'], en: ['currency', 'convert', 'money'] }
    },
    {
        id: 'calculator',
        title: { ar: 'آلة حاسبة', en: 'Calculator' },
        description: { ar: 'عمليات حسابية بسيطة', en: 'Simple mathematical calculations' },
        category: 'number',
        icon: 'fas fa-calculator',
        url: 'tool13.html',
        tags: { ar: ['حاسبة', 'رياضيات', 'حساب'], en: ['calculator', 'math', 'calculation'] }
    },
    
    // Time Tools
    {
        id: 'time-difference',
        title: { ar: 'حاسبة الفرق الزمني', en: 'Time Difference Calculator' },
        description: { ar: 'حساب الفارق بين وقتين', en: 'Calculate the difference between two times' },
        category: 'time',
        icon: 'fas fa-hourglass-half',
        url: 'tool16.html',
        tags: { ar: ['وقت', 'فرق', 'حساب'], en: ['time', 'difference', 'calculate'] }
    },
    {
        id: 'current-time',
        title: { ar: 'عرض الوقت الحالي', en: 'Current Time Display' },
        description: { ar: 'ساعة رقمية حسب المنطقة الزمنية', en: 'Digital clock by timezone' },
        category: 'time',
        icon: 'fas fa-clock',
        url: 'tool17.html',
        tags: { ar: ['ساعة', 'وقت', 'منطقة'], en: ['clock', 'time', 'timezone'] }
    },
    
    // Daily Tools
    {
        id: 'budget-calculator',
        title: { ar: 'حاسبة الميزانية', en: 'Budget Calculator' },
        description: { ar: 'إدخال المصروفات والدخل لحساب الرصيد بسرعة', en: 'Track expenses and income to calculate balance' },
        category: 'daily',
        icon: 'fas fa-wallet',
        url: 'tool21.html',
        tags: { ar: ['ميزانية', 'مال', 'حساب'], en: ['budget', 'money', 'finance'] }
    },
    {
        id: 'todo-list',
        title: { ar: 'قائمة المهام', en: 'Todo List' },
        description: { ar: 'إضافة وحذف المهام اليومية مع حفظها في المتصفح', en: 'Add and manage daily tasks with browser storage' },
        category: 'daily',
        icon: 'fas fa-tasks',
        url: 'tool23.html',
        tags: { ar: ['مهام', 'قائمة', 'تنظيم'], en: ['tasks', 'list', 'organize'] }
    },
    
    // More Text Tools
    {
        id: 'text-merger',
        title: { ar: 'دمج النصوص', en: 'Text Merger' },
        description: { ar: 'دمج عدة نصوص في نص واحد', en: 'Merge multiple texts into one' },
        category: 'text',
        icon: 'fas fa-object-group',
        url: 'tool19.html',
        tags: { ar: ['دمج', 'نص', 'ربط'], en: ['merge', 'text', 'combine'] }
    },

    // More Email Tools
    {
        id: 'email-link-generator',
        title: { ar: 'مولد روابط الإيميل', en: 'Email Link Generator' },
        description: { ar: 'إنشاء رابط mailto تلقائيًا', en: 'Generate mailto links automatically' },
        category: 'email',
        icon: 'fas fa-link',
        url: 'tool7.html',
        tags: { ar: ['رابط', 'إيميل', 'mailto'], en: ['link', 'email', 'mailto'] }
    },

    // More Image Tools
    {
        id: 'image-cropper',
        title: { ar: 'قص الصور', en: 'Image Cropper' },
        description: { ar: 'قص جزء من الصورة', en: 'Crop part of an image' },
        category: 'image',
        icon: 'fas fa-crop',
        url: 'tool10.html',
        tags: { ar: ['قص', 'صورة', 'تحرير'], en: ['crop', 'image', 'edit'] }
    },
    {
        id: 'text-on-image',
        title: { ar: 'إضافة نص على الصور', en: 'Text on Image' },
        description: { ar: 'كتابة نص على صورة', en: 'Add text overlay to images' },
        category: 'image',
        icon: 'fas fa-image',
        url: 'tool11.html',
        tags: { ar: ['نص', 'صورة', 'إضافة'], en: ['text', 'image', 'overlay'] }
    },
    {
        id: 'image-color-converter',
        title: { ar: 'محول ألوان الصور', en: 'Image Color Converter' },
        description: { ar: 'تعديل وتحويل ألوان الصور بطرق مختلفة', en: 'Edit and convert image colors in different ways' },
        category: 'image',
        icon: 'fas fa-palette',
        url: 'newtools/تحويل الوان الصور.html',
        tags: { ar: ['ألوان', 'صورة', 'تحويل'], en: ['colors', 'image', 'convert'] }
    },

    // More Number Tools
    {
        id: 'random-number-generator',
        title: { ar: 'مولد أرقام عشوائية', en: 'Random Number Generator' },
        description: { ar: 'إنشاء رقم عشوائي بين نطاق محدد', en: 'Generate random numbers within a specified range' },
        category: 'number',
        icon: 'fas fa-random',
        url: 'tool14.html',
        tags: { ar: ['عشوائي', 'رقم', 'مولد'], en: ['random', 'number', 'generator'] }
    },
    {
        id: 'number-to-words',
        title: { ar: 'تحويل الأرقام إلى كلمات', en: 'Number to Words' },
        description: { ar: 'تحويل الأرقام إلى كلمات مكتوبة', en: 'Convert numbers to written words' },
        category: 'number',
        icon: 'fas fa-spell-check',
        url: 'tool15.html',
        tags: { ar: ['رقم', 'كلمات', 'تحويل'], en: ['number', 'words', 'convert'] }
    },
    {
        id: 'percentage-calculator',
        title: { ar: 'حاسبة النسبة المئوية', en: 'Percentage Calculator' },
        description: { ar: 'حساب النسب المئوية بسهولة (مثل: كم 20% من 500)', en: 'Calculate percentages easily (e.g., what is 20% of 500)' },
        category: 'number',
        icon: 'fas fa-percent',
        url: 'tool25.html',
        tags: { ar: ['نسبة', 'مئوية', 'حساب'], en: ['percentage', 'calculate', 'ratio'] }
    },
    {
        id: 'unit-converter',
        title: { ar: 'محول وحدات متطور', en: 'Advanced Unit Converter' },
        description: { ar: 'تحويل بين مختلف أنواع الوحدات', en: 'Convert between different types of units' },
        category: 'number',
        icon: 'fas fa-exchange-alt',
        url: 'tool26.html',
        tags: { ar: ['وحدات', 'تحويل', 'قياس'], en: ['units', 'convert', 'measurement'] }
    },

    // More Time Tools
    {
        id: 'countdown-timer',
        title: { ar: 'مؤقت عكسي', en: 'Countdown Timer' },
        description: { ar: 'تايمر تنازلي مع تنبيه صوتي', en: 'Countdown timer with audio alert' },
        category: 'time',
        icon: 'fas fa-stopwatch',
        url: 'tool18.html',
        tags: { ar: ['مؤقت', 'عكسي', 'تنبيه'], en: ['timer', 'countdown', 'alert'] }
    },

    // URL Tools
    {
        id: 'url-extractor',
        title: { ar: 'مستخرج الروابط', en: 'URL Extractor' },
        description: { ar: 'استخراج الروابط من النص', en: 'Extract URLs from text' },
        category: 'url',
        icon: 'fas fa-link',
        url: 'tool20.html',
        tags: { ar: ['رابط', 'استخراج', 'URL'], en: ['URL', 'extract', 'link'] }
    },

    // More Daily Tools
    {
        id: 'currency-converter-daily',
        title: { ar: 'محول العملات', en: 'Currency Converter' },
        description: { ar: 'تحويل بين العملات المختلفة', en: 'Convert between different currencies' },
        category: 'daily',
        icon: 'fas fa-exchange-alt',
        url: 'tool22.html',
        tags: { ar: ['عملة', 'تحويل', 'مال'], en: ['currency', 'convert', 'money'] }
    },
    {
        id: 'password-generator',
        title: { ar: 'مولد كلمات المرور', en: 'Password Generator' },
        description: { ar: 'إنشاء كلمات مرور قوية بطول ونوعية محددة', en: 'Generate strong passwords with specified length and type' },
        category: 'daily',
        icon: 'fas fa-key',
        url: 'tool24.html',
        tags: { ar: ['كلمة مرور', 'أمان', 'مولد'], en: ['password', 'security', 'generator'] }
    },

    // Miscellaneous Tools
    {
        id: 'baby-names-generator',
        title: { ar: 'مولد أسماء الأطفال', en: 'Baby Names Generator' },
        description: { ar: 'اقتراحات لأسماء الأطفال مع معانيها', en: 'Baby name suggestions with meanings' },
        category: 'daily',
        icon: 'fas fa-baby',
        url: 'tool27.html',
        tags: { ar: ['أسماء', 'أطفال', 'معاني'], en: ['names', 'baby', 'meanings'] }
    },
    {
        id: 'image-color-analyzer',
        title: { ar: 'محلل الألوان من الصور', en: 'Image Color Analyzer' },
        description: { ar: 'استخراج الألوان الرئيسية من الصور مع أكواد الألوان', en: 'Extract main colors from images with color codes' },
        category: 'image',
        icon: 'fas fa-palette',
        url: 'tool28.html',
        tags: { ar: ['ألوان', 'تحليل', 'صورة'], en: ['colors', 'analyze', 'image'] }
    },
    {
        id: 'daily-schedule-generator',
        title: { ar: 'مولد خطة يومية', en: 'Daily Schedule Generator' },
        description: { ar: 'تنظيم المهام اليومية وتوزيع الوقت تلقائياً', en: 'Organize daily tasks and distribute time automatically' },
        category: 'daily',
        icon: 'fas fa-calendar-alt',
        url: 'tool29.html',
        tags: { ar: ['جدولة', 'مهام', 'تنظيم'], en: ['schedule', 'tasks', 'organize'] }
    },
    {
        id: 'emoji-translator',
        title: { ar: 'مترجم الرموز التعبيرية', en: 'Emoji Translator' },
        description: { ar: 'تحويل النص إلى رموز تعبيرية والعكس', en: 'Convert text to emojis and vice versa' },
        category: 'text',
        icon: 'fas fa-smile',
        url: 'tool30.html',
        tags: { ar: ['رموز', 'تعبيرية', 'ترجمة'], en: ['emoji', 'translate', 'convert'] }
    },
    {
        id: 'water-intake-calculator',
        title: { ar: 'حاسبة استهلاك الماء', en: 'Water Intake Calculator' },
        description: { ar: 'حساب كمية الماء اليومية المطلوبة', en: 'Calculate daily water intake requirements' },
        category: 'daily',
        icon: 'fas fa-tint',
        url: 'tool31.html',
        tags: { ar: ['ماء', 'صحة', 'حساب'], en: ['water', 'health', 'calculate'] }
    },
    {
        id: 'greeting-card-generator',
        title: { ar: 'مولد بطاقات تهنئة', en: 'Greeting Card Generator' },
        description: { ar: 'إنشاء بطاقات تهنئة مخصصة للمناسبات', en: 'Create custom greeting cards for occasions' },
        category: 'daily',
        icon: 'fas fa-birthday-cake',
        url: 'tool32.html',
        tags: { ar: ['بطاقات', 'تهنئة', 'مناسبات'], en: ['cards', 'greeting', 'occasions'] }
    },
    {
        id: 'typing-speed-test',
        title: { ar: 'اختبار سرعة الكتابة', en: 'Typing Speed Test' },
        description: { ar: 'قياس سرعة الكتابة والأخطاء', en: 'Measure typing speed and errors' },
        category: 'daily',
        icon: 'fas fa-keyboard',
        url: 'tool33.html',
        tags: { ar: ['كتابة', 'سرعة', 'اختبار'], en: ['typing', 'speed', 'test'] }
    },
    {
        id: 'age-calculator',
        title: { ar: 'حاسبة العمر الحقيقي', en: 'Real Age Calculator' },
        description: { ar: 'حساب عمرك بالسنوات والأشهر والأيام', en: 'Calculate your age in years, months, and days' },
        category: 'daily',
        icon: 'fas fa-hourglass',
        url: 'tool34.html',
        tags: { ar: ['عمر', 'حساب', 'تاريخ'], en: ['age', 'calculate', 'date'] }
    },
    {
        id: 'shopping-list-generator',
        title: { ar: 'مولد قوائم التسوق', en: 'Shopping List Generator' },
        description: { ar: 'إنشاء وتنظيم قوائم التسوق', en: 'Create and organize shopping lists' },
        category: 'daily',
        icon: 'fas fa-shopping-cart',
        url: 'tool35.html',
        tags: { ar: ['تسوق', 'قائمة', 'تنظيم'], en: ['shopping', 'list', 'organize'] }
    },
    {
        id: 'nature-sounds-player',
        title: { ar: 'محاكي أصوات الطبيعة', en: 'Nature Sounds Player' },
        description: { ar: 'تشغيل أصوات طبيعية للاسترخاء', en: 'Play natural sounds for relaxation' },
        category: 'daily',
        icon: 'fas fa-volume-up',
        url: 'tool36.html',
        tags: { ar: ['أصوات', 'طبيعة', 'استرخاء'], en: ['sounds', 'nature', 'relaxation'] }
    },

    // Developer Tools
    {
        id: 'emoji-icons-library',
        title: { ar: 'مكتبة الرموز والأيقونات', en: 'Emoji & Icons Library' },
        description: { ar: 'مجموعة كبيرة من الرموز التعبيرية والأيقونات للنسخ', en: 'Large collection of emojis and icons for copying' },
        category: 'developer',
        icon: 'far fa-smile',
        url: 'tool37.html',
        tags: { ar: ['رموز', 'أيقونات', 'مكتبة'], en: ['emoji', 'icons', 'library'] }
    },
    {
        id: 'color-picker',
        title: { ar: 'منتقي وملتقط الألوان', en: 'Color Picker & Extractor' },
        description: { ar: 'انتقاء الألوان واستخراجها من الصور مع أكوادها', en: 'Pick colors and extract them from images with codes' },
        category: 'developer',
        icon: 'fas fa-eye-dropper',
        url: 'tool38.html',
        tags: { ar: ['ألوان', 'انتقاء', 'كود'], en: ['colors', 'picker', 'code'] }
    },
    {
        id: 'markdown-converter',
        title: { ar: 'محول مارك داون', en: 'Markdown Converter' },
        description: { ar: 'تحويل نص المارك داون إلى HTML مع معاينة مباشرة', en: 'Convert Markdown text to HTML with live preview' },
        category: 'developer',
        icon: 'fab fa-markdown',
        url: 'tool39.html',
        tags: { ar: ['مارك داون', 'HTML', 'تحويل'], en: ['markdown', 'HTML', 'convert'] }
    },
    {
        id: 'word-frequency-analyzer',
        title: { ar: 'محلل تكرار الكلمات', en: 'Word Frequency Analyzer' },
        description: { ar: 'تحليل تكرار الكلمات والعبارات في النص', en: 'Analyze word and phrase frequency in text' },
        category: 'developer',
        icon: 'fas fa-text-width',
        url: 'tool40.html',
        tags: { ar: ['تكرار', 'كلمات', 'تحليل'], en: ['frequency', 'words', 'analyze'] }
    },
    {
        id: 'seo-content-analyzer',
        title: { ar: 'محلل محتوى SEO', en: 'SEO Content Analyzer' },
        description: { ar: 'تحليل شامل للمحتوى من ناحية تحسين محركات البحث', en: 'Comprehensive content analysis for SEO optimization' },
        category: 'seo',
        icon: 'fas fa-search',
        url: 'tool41.html',
        tags: { ar: ['SEO', 'تحليل', 'محتوى'], en: ['SEO', 'analyze', 'content'] }
    },

    // Investment Tools
    {
        id: 'investment-calculator',
        title: { ar: 'حاسبة الاستثمار', en: 'Investment Calculator' },
        description: { ar: 'حساب عوائد الاستثمار والفوائد المركبة', en: 'Calculate investment returns and compound interest' },
        category: 'investment',
        icon: 'fas fa-chart-line',
        url: 'newtools/حاسبة الاستثمار.html',
        tags: { ar: ['استثمار', 'عوائد', 'فوائد'], en: ['investment', 'returns', 'interest'] }
    },
    {
        id: 'loan-calculator',
        title: { ar: 'حاسبة القروض', en: 'Loan Calculator' },
        description: { ar: 'حساب أقساط القروض والفوائد', en: 'Calculate loan payments and interest' },
        category: 'investment',
        icon: 'fas fa-hand-holding-usd',
        url: 'newtools/حاسبة القروض.html',
        tags: { ar: ['قرض', 'أقساط', 'فوائد'], en: ['loan', 'payments', 'interest'] }
    },
    {
        id: 'retirement-calculator',
        title: { ar: 'حاسبة التقاعد', en: 'Retirement Calculator' },
        description: { ar: 'تخطيط مدخرات التقاعد والمعاش', en: 'Plan retirement savings and pension' },
        category: 'investment',
        icon: 'fas fa-piggy-bank',
        url: 'newtools/حاسبة التقاعد.html',
        tags: { ar: ['تقاعد', 'مدخرات', 'معاش'], en: ['retirement', 'savings', 'pension'] }
    },

    // Data Tools
    {
        id: 'json-formatter',
        title: { ar: 'منسق JSON', en: 'JSON Formatter' },
        description: { ar: 'تنسيق وتجميل ملفات JSON', en: 'Format and beautify JSON files' },
        category: 'data',
        icon: 'fas fa-code',
        url: 'newtools/منسق JSON.html',
        tags: { ar: ['JSON', 'تنسيق', 'بيانات'], en: ['JSON', 'format', 'data'] }
    },
    {
        id: 'csv-converter',
        title: { ar: 'محول CSV', en: 'CSV Converter' },
        description: { ar: 'تحويل ملفات CSV إلى تنسيقات أخرى', en: 'Convert CSV files to other formats' },
        category: 'data',
        icon: 'fas fa-table',
        url: 'newtools/محول CSV.html',
        tags: { ar: ['CSV', 'تحويل', 'جداول'], en: ['CSV', 'convert', 'tables'] }
    },
    {
        id: 'xml-formatter',
        title: { ar: 'منسق XML', en: 'XML Formatter' },
        description: { ar: 'تنسيق وتجميل ملفات XML', en: 'Format and beautify XML files' },
        category: 'data',
        icon: 'fas fa-file-code',
        url: 'newtools/منسق XML.html',
        tags: { ar: ['XML', 'تنسيق', 'بيانات'], en: ['XML', 'format', 'data'] }
    },

    // More SEO Tools
    {
        id: 'meta-tags-generator',
        title: { ar: 'مولد Meta Tags', en: 'Meta Tags Generator' },
        description: { ar: 'إنشاء meta tags لتحسين SEO', en: 'Generate meta tags for SEO optimization' },
        category: 'seo',
        icon: 'fas fa-tags',
        url: 'newtools/مولد Meta Tags.html',
        tags: { ar: ['meta', 'tags', 'SEO'], en: ['meta', 'tags', 'SEO'] }
    },
    {
        id: 'robots-txt-generator',
        title: { ar: 'مولد ملف Robots.txt', en: 'Robots.txt Generator' },
        description: { ar: 'إنشاء ملف robots.txt للمواقع', en: 'Generate robots.txt file for websites' },
        category: 'seo',
        icon: 'fas fa-robot',
        url: 'newtools/مولد Robots.txt.html',
        tags: { ar: ['robots', 'SEO', 'مواقع'], en: ['robots', 'SEO', 'websites'] }
    },
    {
        id: 'sitemap-generator',
        title: { ar: 'مولد خريطة الموقع', en: 'Sitemap Generator' },
        description: { ar: 'إنشاء خريطة XML للموقع', en: 'Generate XML sitemap for website' },
        category: 'seo',
        icon: 'fas fa-sitemap',
        url: 'newtools/مولد خريطة الموقع.html',
        tags: { ar: ['خريطة', 'موقع', 'XML'], en: ['sitemap', 'website', 'XML'] }
    },

    // Additional Tools from newtools folder
    {
        id: 'qr-code-generator',
        title: { ar: 'مولد رموز QR', en: 'QR Code Generator' },
        description: { ar: 'إنشاء رموز QR للنصوص والروابط', en: 'Generate QR codes for text and links' },
        category: 'daily',
        icon: 'fas fa-qrcode',
        url: 'newtools/مولد رموز QR.html',
        tags: { ar: ['QR', 'رمز', 'مولد'], en: ['QR', 'code', 'generator'] }
    },
    {
        id: 'barcode-generator',
        title: { ar: 'مولد الباركود', en: 'Barcode Generator' },
        description: { ar: 'إنشاء رموز الباركود بأنواع مختلفة', en: 'Generate barcodes in different formats' },
        category: 'daily',
        icon: 'fas fa-barcode',
        url: 'newtools/مولد الباركود.html',
        tags: { ar: ['باركود', 'رمز', 'مولد'], en: ['barcode', 'code', 'generator'] }
    },
    {
        id: 'hash-generator',
        title: { ar: 'مولد Hash', en: 'Hash Generator' },
        description: { ar: 'إنشاء hash للنصوص بخوارزميات مختلفة', en: 'Generate hash for text using different algorithms' },
        category: 'developer',
        icon: 'fas fa-hashtag',
        url: 'newtools/مولد Hash.html',
        tags: { ar: ['hash', 'تشفير', 'أمان'], en: ['hash', 'encryption', 'security'] }
    },
    {
        id: 'base64-encoder',
        title: { ar: 'مشفر Base64', en: 'Base64 Encoder' },
        description: { ar: 'تشفير وفك تشفير Base64', en: 'Encode and decode Base64' },
        category: 'developer',
        icon: 'fas fa-lock',
        url: 'newtools/مشفر Base64.html',
        tags: { ar: ['base64', 'تشفير', 'ترميز'], en: ['base64', 'encode', 'decode'] }
    },
    {
        id: 'url-shortener',
        title: { ar: 'مختصر الروابط', en: 'URL Shortener' },
        description: { ar: 'اختصار الروابط الطويلة', en: 'Shorten long URLs' },
        category: 'url',
        icon: 'fas fa-compress',
        url: 'newtools/مختصر الروابط.html',
        tags: { ar: ['رابط', 'اختصار', 'URL'], en: ['URL', 'shorten', 'link'] }
    },
    {
        id: 'url-analyzer',
        title: { ar: 'محلل الروابط', en: 'URL Analyzer' },
        description: { ar: 'تحليل وفحص الروابط', en: 'Analyze and inspect URLs' },
        category: 'url',
        icon: 'fas fa-search-plus',
        url: 'newtools/محلل الروابط.html',
        tags: { ar: ['رابط', 'تحليل', 'فحص'], en: ['URL', 'analyze', 'inspect'] }
    },
    {
        id: 'domain-checker',
        title: { ar: 'فاحص النطاقات', en: 'Domain Checker' },
        description: { ar: 'فحص توفر أسماء النطاقات', en: 'Check domain name availability' },
        category: 'url',
        icon: 'fas fa-globe',
        url: 'newtools/فاحص النطاقات.html',
        tags: { ar: ['نطاق', 'فحص', 'توفر'], en: ['domain', 'check', 'availability'] }
    },
    {
        id: 'whois-lookup',
        title: { ar: 'بحث Whois', en: 'Whois Lookup' },
        description: { ar: 'البحث عن معلومات النطاق', en: 'Look up domain information' },
        category: 'url',
        icon: 'fas fa-info-circle',
        url: 'newtools/بحث Whois.html',
        tags: { ar: ['whois', 'نطاق', 'معلومات'], en: ['whois', 'domain', 'information'] }
    },

    // Additional Text Tools
    {
        id: 'text-to-speech',
        title: { ar: 'تحويل النص إلى صوت', en: 'Text to Speech' },
        description: { ar: 'تحويل النص المكتوب إلى كلام مسموع', en: 'Convert written text to spoken audio' },
        category: 'text',
        icon: 'fas fa-volume-up',
        url: 'newtools/تحويل النص إلى صوت.html',
        tags: { ar: ['نص', 'صوت', 'كلام'], en: ['text', 'speech', 'audio'] }
    },
    {
        id: 'text-diff-checker',
        title: { ar: 'مقارن النصوص', en: 'Text Diff Checker' },
        description: { ar: 'مقارنة نصين وإظهار الاختلافات', en: 'Compare two texts and show differences' },
        category: 'text',
        icon: 'fas fa-not-equal',
        url: 'newtools/مقارن النصوص.html',
        tags: { ar: ['مقارنة', 'نص', 'اختلاف'], en: ['compare', 'text', 'diff'] }
    },
    {
        id: 'lorem-ipsum-generator',
        title: { ar: 'مولد نص Lorem Ipsum', en: 'Lorem Ipsum Generator' },
        description: { ar: 'إنشاء نص Lorem Ipsum للتصميم', en: 'Generate Lorem Ipsum text for design' },
        category: 'text',
        icon: 'fas fa-paragraph',
        url: 'newtools/مولد Lorem Ipsum.html',
        tags: { ar: ['lorem', 'ipsum', 'تصميم'], en: ['lorem', 'ipsum', 'design'] }
    },

    // Additional Image Tools
    {
        id: 'image-compressor',
        title: { ar: 'ضاغط الصور', en: 'Image Compressor' },
        description: { ar: 'ضغط الصور لتقليل حجمها', en: 'Compress images to reduce file size' },
        category: 'image',
        icon: 'fas fa-compress-alt',
        url: 'newtools/ضاغط الصور.html',
        tags: { ar: ['ضغط', 'صورة', 'حجم'], en: ['compress', 'image', 'size'] }
    },
    {
        id: 'image-format-converter',
        title: { ar: 'محول تنسيق الصور', en: 'Image Format Converter' },
        description: { ar: 'تحويل الصور بين التنسيقات المختلفة', en: 'Convert images between different formats' },
        category: 'image',
        icon: 'fas fa-exchange-alt',
        url: 'newtools/محول تنسيق الصور.html',
        tags: { ar: ['تحويل', 'صورة', 'تنسيق'], en: ['convert', 'image', 'format'] }
    },
    {
        id: 'image-watermark',
        title: { ar: 'إضافة علامة مائية', en: 'Image Watermark' },
        description: { ar: 'إضافة علامة مائية للصور', en: 'Add watermark to images' },
        category: 'image',
        icon: 'fas fa-copyright',
        url: 'newtools/علامة مائية.html',
        tags: { ar: ['علامة', 'مائية', 'حماية'], en: ['watermark', 'protection', 'copyright'] }
    },

    // Additional Number Tools
    {
        id: 'bmi-calculator',
        title: { ar: 'حاسبة مؤشر كتلة الجسم', en: 'BMI Calculator' },
        description: { ar: 'حساب مؤشر كتلة الجسم والوزن المثالي', en: 'Calculate BMI and ideal weight' },
        category: 'number',
        icon: 'fas fa-weight',
        url: 'newtools/حاسبة BMI.html',
        tags: { ar: ['BMI', 'وزن', 'صحة'], en: ['BMI', 'weight', 'health'] }
    },
    {
        id: 'tip-calculator',
        title: { ar: 'حاسبة البقشيش', en: 'Tip Calculator' },
        description: { ar: 'حساب البقشيش وتقسيم الفاتورة', en: 'Calculate tip and split bill' },
        category: 'number',
        icon: 'fas fa-receipt',
        url: 'newtools/حاسبة البقشيش.html',
        tags: { ar: ['بقشيش', 'فاتورة', 'حساب'], en: ['tip', 'bill', 'calculate'] }
    },
    {
        id: 'grade-calculator',
        title: { ar: 'حاسبة الدرجات', en: 'Grade Calculator' },
        description: { ar: 'حساب المعدل والدرجات الدراسية', en: 'Calculate GPA and academic grades' },
        category: 'number',
        icon: 'fas fa-graduation-cap',
        url: 'newtools/حاسبة الدرجات.html',
        tags: { ar: ['درجات', 'معدل', 'دراسة'], en: ['grades', 'GPA', 'academic'] }
    },

    // Additional Time Tools
    {
        id: 'world-clock',
        title: { ar: 'ساعة عالمية', en: 'World Clock' },
        description: { ar: 'عرض الوقت في مدن مختلفة حول العالم', en: 'Display time in different cities worldwide' },
        category: 'time',
        icon: 'fas fa-globe-americas',
        url: 'newtools/ساعة عالمية.html',
        tags: { ar: ['ساعة', 'عالمية', 'مناطق'], en: ['clock', 'world', 'timezone'] }
    },
    {
        id: 'date-calculator',
        title: { ar: 'حاسبة التواريخ', en: 'Date Calculator' },
        description: { ar: 'حساب الفرق بين التواريخ وإضافة أيام', en: 'Calculate date differences and add days' },
        category: 'time',
        icon: 'fas fa-calendar-plus',
        url: 'newtools/حاسبة التواريخ.html',
        tags: { ar: ['تاريخ', 'حساب', 'أيام'], en: ['date', 'calculate', 'days'] }
    },
    {
        id: 'pomodoro-timer',
        title: { ar: 'مؤقت بومودورو', en: 'Pomodoro Timer' },
        description: { ar: 'تقنية بومودورو لإدارة الوقت والإنتاجية', en: 'Pomodoro technique for time management and productivity' },
        category: 'time',
        icon: 'fas fa-tomato-alt',
        url: 'newtools/مؤقت بومودورو.html',
        tags: { ar: ['بومودورو', 'إنتاجية', 'تركيز'], en: ['pomodoro', 'productivity', 'focus'] }
    },

    // Additional Daily Tools
    {
        id: 'habit-tracker',
        title: { ar: 'متتبع العادات', en: 'Habit Tracker' },
        description: { ar: 'تتبع العادات اليومية والأهداف', en: 'Track daily habits and goals' },
        category: 'daily',
        icon: 'fas fa-check-double',
        url: 'newtools/متتبع العادات.html',
        tags: { ar: ['عادات', 'تتبع', 'أهداف'], en: ['habits', 'tracker', 'goals'] }
    },
    {
        id: 'mood-tracker',
        title: { ar: 'متتبع المزاج', en: 'Mood Tracker' },
        description: { ar: 'تتبع الحالة المزاجية اليومية', en: 'Track daily mood and emotions' },
        category: 'daily',
        icon: 'fas fa-smile-beam',
        url: 'newtools/متتبع المزاج.html',
        tags: { ar: ['مزاج', 'مشاعر', 'تتبع'], en: ['mood', 'emotions', 'tracker'] }
    },
    {
        id: 'expense-tracker',
        title: { ar: 'متتبع المصروفات', en: 'Expense Tracker' },
        description: { ar: 'تتبع المصروفات اليومية والشهرية', en: 'Track daily and monthly expenses' },
        category: 'daily',
        icon: 'fas fa-money-bill-wave',
        url: 'newtools/متتبع المصروفات.html',
        tags: { ar: ['مصروفات', 'مال', 'تتبع'], en: ['expenses', 'money', 'tracker'] }
    },

    // Additional Developer Tools
    {
        id: 'regex-tester',
        title: { ar: 'مختبر التعبيرات النمطية', en: 'Regex Tester' },
        description: { ar: 'اختبار وتطوير التعبيرات النمطية', en: 'Test and develop regular expressions' },
        category: 'developer',
        icon: 'fas fa-code',
        url: 'newtools/مختبر Regex.html',
        tags: { ar: ['regex', 'تعبيرات', 'اختبار'], en: ['regex', 'expressions', 'test'] }
    },
    {
        id: 'css-minifier',
        title: { ar: 'مضغط CSS', en: 'CSS Minifier' },
        description: { ar: 'ضغط وتصغير ملفات CSS', en: 'Compress and minify CSS files' },
        category: 'developer',
        icon: 'fab fa-css3-alt',
        url: 'newtools/مضغط CSS.html',
        tags: { ar: ['CSS', 'ضغط', 'تصغير'], en: ['CSS', 'compress', 'minify'] }
    },
    {
        id: 'js-minifier',
        title: { ar: 'مضغط JavaScript', en: 'JavaScript Minifier' },
        description: { ar: 'ضغط وتصغير ملفات JavaScript', en: 'Compress and minify JavaScript files' },
        category: 'developer',
        icon: 'fab fa-js-square',
        url: 'newtools/مضغط JavaScript.html',
        tags: { ar: ['JavaScript', 'ضغط', 'تصغير'], en: ['JavaScript', 'compress', 'minify'] }
    },
    {
        id: 'html-formatter',
        title: { ar: 'منسق HTML', en: 'HTML Formatter' },
        description: { ar: 'تنسيق وتجميل كود HTML', en: 'Format and beautify HTML code' },
        category: 'developer',
        icon: 'fab fa-html5',
        url: 'newtools/منسق HTML.html',
        tags: { ar: ['HTML', 'تنسيق', 'كود'], en: ['HTML', 'format', 'code'] }
    },

    // Miscellaneous Tools
    {
        id: 'random-quote-generator',
        title: { ar: 'مولد الاقتباسات العشوائية', en: 'Random Quote Generator' },
        description: { ar: 'عرض اقتباسات ملهمة عشوائية', en: 'Display random inspirational quotes' },
        category: 'misc',
        icon: 'fas fa-quote-right',
        url: 'newtools/مولد الاقتباسات.html',
        tags: { ar: ['اقتباسات', 'إلهام', 'عشوائي'], en: ['quotes', 'inspiration', 'random'] }
    },
    {
        id: 'dice-roller',
        title: { ar: 'رمي النرد', en: 'Dice Roller' },
        description: { ar: 'رمي نرد افتراضي بأعداد مختلفة', en: 'Roll virtual dice with different numbers' },
        category: 'misc',
        icon: 'fas fa-dice',
        url: 'newtools/رمي النرد.html',
        tags: { ar: ['نرد', 'عشوائي', 'لعبة'], en: ['dice', 'random', 'game'] }
    },
    {
        id: 'coin-flip',
        title: { ar: 'قذف العملة', en: 'Coin Flip' },
        description: { ar: 'قذف عملة افتراضية للاختيار العشوائي', en: 'Flip virtual coin for random choice' },
        category: 'misc',
        icon: 'fas fa-coins',
        url: 'newtools/قذف العملة.html',
        tags: { ar: ['عملة', 'عشوائي', 'اختيار'], en: ['coin', 'random', 'choice'] }
    },
    {
        id: 'decision-maker',
        title: { ar: 'صانع القرارات', en: 'Decision Maker' },
        description: { ar: 'مساعد في اتخاذ القرارات العشوائية', en: 'Help make random decisions' },
        category: 'misc',
        icon: 'fas fa-balance-scale',
        url: 'newtools/صانع القرارات.html',
        tags: { ar: ['قرار', 'اختيار', 'مساعد'], en: ['decision', 'choice', 'helper'] }
    },
    {
        id: 'random-name-generator',
        title: { ar: 'مولد الأسماء العشوائية', en: 'Random Name Generator' },
        description: { ar: 'إنشاء أسماء عشوائية للأشخاص والشركات', en: 'Generate random names for people and companies' },
        category: 'misc',
        icon: 'fas fa-user-tag',
        url: 'newtools/مولد الأسماء.html',
        tags: { ar: ['أسماء', 'عشوائي', 'مولد'], en: ['names', 'random', 'generator'] }
    },
    {
        id: 'color-palette-generator',
        title: { ar: 'مولد لوحات الألوان', en: 'Color Palette Generator' },
        description: { ar: 'إنشاء لوحات ألوان متناسقة للتصميم', en: 'Generate harmonious color palettes for design' },
        category: 'misc',
        icon: 'fas fa-palette',
        url: 'newtools/مولد لوحات الألوان.html',
        tags: { ar: ['ألوان', 'لوحة', 'تصميم'], en: ['colors', 'palette', 'design'] }
    },
    {
        id: 'meme-generator',
        title: { ar: 'مولد الميمز', en: 'Meme Generator' },
        description: { ar: 'إنشاء ميمز مضحكة بالنصوص والصور', en: 'Create funny memes with text and images' },
        category: 'misc',
        icon: 'fas fa-laugh',
        url: 'newtools/مولد الميمز.html',
        tags: { ar: ['ميم', 'مضحك', 'صورة'], en: ['meme', 'funny', 'image'] }
    },
    {
        id: 'weather-widget',
        title: { ar: 'ودجت الطقس', en: 'Weather Widget' },
        description: { ar: 'عرض حالة الطقس الحالية والتوقعات', en: 'Display current weather and forecasts' },
        category: 'misc',
        icon: 'fas fa-cloud-sun',
        url: 'newtools/ودجت الطقس.html',
        tags: { ar: ['طقس', 'توقعات', 'مناخ'], en: ['weather', 'forecast', 'climate'] }
    },
    {
        id: 'unit-converter-advanced',
        title: { ar: 'محول الوحدات المتقدم', en: 'Advanced Unit Converter' },
        description: { ar: 'تحويل شامل بين جميع أنواع الوحدات', en: 'Comprehensive conversion between all unit types' },
        category: 'misc',
        icon: 'fas fa-exchange-alt',
        url: 'newtools/محول الوحدات المتقدم.html',
        tags: { ar: ['وحدات', 'تحويل', 'قياس'], en: ['units', 'convert', 'measurement'] }
    },
    {
        id: 'random-team-generator',
        title: { ar: 'مولد الفرق العشوائية', en: 'Random Team Generator' },
        description: { ar: 'تقسيم الأشخاص إلى فرق عشوائية', en: 'Divide people into random teams' },
        category: 'misc',
        icon: 'fas fa-users',
        url: 'newtools/مولد الفرق.html',
        tags: { ar: ['فرق', 'تقسيم', 'عشوائي'], en: ['teams', 'divide', 'random'] }
    }
];

// Application State
let currentLanguage = 'ar';
let currentTheme = 'light';
let currentCategory = 'all';
let currentView = 'grid';

// DOM Elements
const header = document.getElementById('header');
const searchInput = document.getElementById('searchInput');
const searchInputEn = document.getElementById('searchInputEn');
const searchResults = document.getElementById('searchResults');
const navTabs = document.getElementById('navTabs');
const toolsGrid = document.getElementById('toolsGrid');
const themeToggle = document.getElementById('themeToggle');
const langToggle = document.getElementById('langToggle');
const openAllTools = document.getElementById('openAllTools');
const toggleView = document.getElementById('toggleView');
const backToTop = document.getElementById('backToTop');

// Initialize Application
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    loadTools();
    setupEventListeners();
    setupScrollEffects();
});

// Initialize App
function initializeApp() {
    // Load saved preferences
    currentLanguage = localStorage.getItem('language') || 'ar';
    currentTheme = localStorage.getItem('theme') || 'light';
    currentView = localStorage.getItem('view') || 'grid';
    
    // Apply preferences
    updateLanguage();
    updateTheme();
    updateView();
}

// Setup Event Listeners
function setupEventListeners() {
    // Theme toggle
    themeToggle.addEventListener('click', toggleTheme);
    
    // Language toggle
    langToggle.addEventListener('click', toggleLanguage);
    
    // Search
    searchInput.addEventListener('input', handleSearch);
    searchInputEn.addEventListener('input', handleSearch);
    
    // Navigation tabs
    navTabs.addEventListener('click', handleTabClick);
    
    // Action buttons
    openAllTools.addEventListener('click', handleOpenAllTools);
    toggleView.addEventListener('click', handleToggleView);
    
    // Back to top
    backToTop.addEventListener('click', scrollToTop);
    
    // Close search results when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.header-search')) {
            hideSearchResults();
        }
    });
}

// Setup Scroll Effects
function setupScrollEffects() {
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        // Header scroll effect
        if (currentScrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Back to top button
        if (currentScrollY > 500) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
        
        lastScrollY = currentScrollY;
    });
}

// Load Tools
function loadTools() {
    const filteredTools = currentCategory === 'all' 
        ? toolsData 
        : toolsData.filter(tool => tool.category === currentCategory);
    
    toolsGrid.innerHTML = filteredTools.map(tool => createToolCard(tool)).join('');
    
    // Add staggered animation
    const cards = toolsGrid.querySelectorAll('.tool-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
    });
}

// Create Tool Card
function createToolCard(tool) {
    const title = tool.title[currentLanguage];
    const description = tool.description[currentLanguage];
    const tags = tool.tags[currentLanguage];
    
    return `
        <div class="tool-card" data-category="${tool.category}" onclick="location.href='${tool.url}'">
            <div class="tool-card-header">
                <div class="tool-icon">
                    <i class="${tool.icon}"></i>
                </div>
                <button class="tool-external-btn" onclick="event.stopPropagation(); openInNewTab('${tool.url}')" title="فتح في تبويب جديد">
                    <i class="fas fa-external-link-alt"></i>
                </button>
            </div>
            
            <h3 class="tool-title">${title}</h3>
            <p class="tool-description">${description}</p>
            
            <div class="tool-tags">
                ${tags.map(tag => `<span class="tool-tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
}

// Handle Search
function handleSearch(e) {
    const query = e.target.value.toLowerCase().trim();
    
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    const results = toolsData.filter(tool => {
        const title = tool.title[currentLanguage].toLowerCase();
        const description = tool.description[currentLanguage].toLowerCase();
        const tags = tool.tags[currentLanguage].join(' ').toLowerCase();
        
        return title.includes(query) || description.includes(query) || tags.includes(query);
    });
    
    displaySearchResults(results);
}

// Display Search Results
function displaySearchResults(results) {
    if (results.length === 0) {
        searchResults.innerHTML = `
            <div class="search-result">
                <h4>${currentLanguage === 'ar' ? 'لا توجد نتائج' : 'No results found'}</h4>
            </div>
        `;
    } else {
        searchResults.innerHTML = results.map(tool => `
            <div class="search-result" onclick="location.href='${tool.url}'">
                <h4>${tool.title[currentLanguage]}</h4>
                <p>${tool.description[currentLanguage]}</p>
            </div>
        `).join('');
    }
    
    searchResults.classList.add('active');
}

// Hide Search Results
function hideSearchResults() {
    searchResults.classList.remove('active');
    searchInput.value = '';
    searchInputEn.value = '';
}

// Handle Tab Click
function handleTabClick(e) {
    const tab = e.target.closest('.nav-tab');
    if (!tab) return;
    
    // Update active tab
    navTabs.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
    tab.classList.add('active');
    
    // Update category
    currentCategory = tab.dataset.category;
    
    // Reload tools
    loadTools();
}

// Toggle Theme
function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    updateTheme();
    localStorage.setItem('theme', currentTheme);
}

// Update Theme
function updateTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    themeToggle.classList.toggle('dark', currentTheme === 'dark');
}

// Toggle Language
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    updateLanguage();
    localStorage.setItem('language', currentLanguage);
}

// Update Language
function updateLanguage() {
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    
    // Show/hide language-specific elements
    document.querySelectorAll('.ar').forEach(el => {
        el.style.display = currentLanguage === 'ar' ? '' : 'none';
    });
    
    document.querySelectorAll('.en').forEach(el => {
        el.style.display = currentLanguage === 'en' ? '' : 'none';
    });
    
    // Reload tools with new language
    loadTools();
}

// Handle Open All Tools
function handleOpenAllTools() {
    const confirmMessage = currentLanguage === 'ar' 
        ? `هل تريد فتح ${toolsData.length} أداة في تبويبات منفصلة؟`
        : `Do you want to open ${toolsData.length} tools in separate tabs?`;
    
    if (confirm(confirmMessage)) {
        toolsData.forEach((tool, index) => {
            setTimeout(() => {
                window.open(tool.url, '_blank');
            }, index * 100);
        });
    }
}

// Handle Toggle View
function handleToggleView() {
    currentView = currentView === 'grid' ? 'list' : 'grid';
    updateView();
    localStorage.setItem('view', currentView);
}

// Update View
function updateView() {
    const icon = toggleView.querySelector('i');
    const arText = toggleView.querySelector('.ar');
    const enText = toggleView.querySelector('.en');
    
    if (currentView === 'list') {
        toolsGrid.style.gridTemplateColumns = '1fr';
        icon.className = 'fas fa-th-large';
        arText.textContent = 'الشبكة';
        enText.textContent = 'Grid';
    } else {
        toolsGrid.style.gridTemplateColumns = '';
        icon.className = 'fas fa-list';
        arText.textContent = 'القائمة';
        enText.textContent = 'List';
    }
}

// Open in New Tab
function openInNewTab(url) {
    window.open(url, '_blank');
}

// Scroll to Top
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
