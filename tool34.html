<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة العمر الحقيقي - Real Age Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .calculator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .result-card {
            background-color: var(--secondary-bg);
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .result-card i {
            font-size: 2em;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .result-value {
            font-size: 1.5em;
            color: var(--primary-color);
            margin: 5px 0;
        }
        .result-label {
            color: var(--text-color);
            font-size: 0.9em;
        }
        .next-birthday {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
            text-align: center;
        }
        .birthday-countdown {
            font-size: 1.2em;
            margin-top: 10px;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>حاسبة العمر الحقيقي <span>Real Age Calculator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="calculator">
            <div class="input-section">
                <div class="input-group">
                    <label for="birthDate">تاريخ الميلاد - Birth Date</label>
                    <input type="date" id="birthDate" onchange="calculateAge()">
                </div>
                <div class="input-group">
                    <label for="birthTime">وقت الميلاد (اختياري) - Birth Time (Optional)</label>
                    <input type="time" id="birthTime" onchange="calculateAge()">
                </div>
            </div>

            <button onclick="calculateAge()" class="primary-button">
                <i class="fas fa-calculator"></i>
                <span class="ar">احسب العمر</span>
                <span class="en">Calculate Age</span>
            </button>

            <div class="results" id="results">
                <div class="result-card">
                    <i class="fas fa-calendar-alt"></i>
                    <div class="result-value" id="years">0</div>
                    <div class="result-label">سنوات - Years</div>
                </div>
                <div class="result-card">
                    <i class="fas fa-calendar-day"></i>
                    <div class="result-value" id="months">0</div>
                    <div class="result-label">شهور - Months</div>
                </div>
                <div class="result-card">
                    <i class="fas fa-calendar-week"></i>
                    <div class="result-value" id="weeks">0</div>
                    <div class="result-label">أسابيع - Weeks</div>
                </div>
                <div class="result-card">
                    <i class="fas fa-clock"></i>
                    <div class="result-value" id="days">0</div>
                    <div class="result-label">أيام - Days</div>
                </div>
                <div class="result-card">
                    <i class="fas fa-hourglass-half"></i>
                    <div class="result-value" id="hours">0</div>
                    <div class="result-label">ساعات - Hours</div>
                </div>
                <div class="result-card">
                    <i class="fas fa-stopwatch"></i>
                    <div class="result-value" id="minutes">0</div>
                    <div class="result-label">دقائق - Minutes</div>
                </div>
            </div>

            <div class="next-birthday" id="nextBirthday">
                <h3>عيد ميلادك القادم - Your Next Birthday</h3>
                <div class="birthday-countdown" id="birthdayCountdown"></div>
            </div>
        </div>
    </main>

    <script>
        function calculateAge() {
            const birthDate = new Date(document.getElementById('birthDate').value);
            const birthTime = document.getElementById('birthTime').value;
            
            if (birthTime) {
                const [hours, minutes] = birthTime.split(':');
                birthDate.setHours(parseInt(hours));
                birthDate.setMinutes(parseInt(minutes));
            }

            const now = new Date();
            
            if (isNaN(birthDate.getTime())) {
                alert('الرجاء إدخال تاريخ صحيح - Please enter a valid date');
                return;
            }

            const diff = now - birthDate;
            const years = Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25));
            const months = Math.floor(diff / (1000 * 60 * 60 * 24 * 30.44));
            const weeks = Math.floor(diff / (1000 * 60 * 60 * 24 * 7));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor(diff / (1000 * 60));

            document.getElementById('years').textContent = years;
            document.getElementById('months').textContent = months;
            document.getElementById('weeks').textContent = weeks;
            document.getElementById('days').textContent = days;
            document.getElementById('hours').textContent = hours;
            document.getElementById('minutes').textContent = minutes;

            // Calculate next birthday
            const nextBirthday = new Date(birthDate);
            nextBirthday.setFullYear(now.getFullYear());
            if (nextBirthday < now) {
                nextBirthday.setFullYear(now.getFullYear() + 1);
            }

            const daysUntilBirthday = Math.ceil((nextBirthday - now) / (1000 * 60 * 60 * 24));
            document.getElementById('birthdayCountdown').innerHTML = `
                <div class="ar">باقي ${daysUntilBirthday} يوم</div>
                <div class="en">${daysUntilBirthday} days remaining</div>
            `;
        }

        // Set max date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('birthDate').max = today;

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>