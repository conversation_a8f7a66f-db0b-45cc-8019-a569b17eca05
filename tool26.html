<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول وحدات متطور - Advanced Unit Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .converter {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .unit-type-selector {
            margin-bottom: 20px;
        }
        .unit-type-selector select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .conversion-panel {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
        }
        .conversion-panel i {
            color: var(--primary-color);
            font-size: 1.5em;
            text-align: center;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 5px;
            text-align: center;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <header>
        <h1>محول وحدات متطور <span>Advanced Unit Converter</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="converter">
            <div class="unit-type-selector">
                <label for="unitType">اختر نوع التحويل - Select Conversion Type</label>
                <select id="unitType" onchange="updateUnitOptions()">
                    <option value="length">الطول - Length</option>
                    <option value="weight">الوزن - Weight</option>
                    <option value="temperature">درجة الحرارة - Temperature</option>
                    <option value="area">المساحة - Area</option>
                    <option value="volume">الحجم - Volume</option>
                    <option value="speed">السرعة - Speed</option>
                </select>
            </div>

            <div class="conversion-panel">
                <div class="input-group">
                    <label for="fromValue">القيمة - Value</label>
                    <input type="number" id="fromValue" onkeyup="convert()">
                    <select id="fromUnit" onchange="convert()"></select>
                </div>
                <i class="fas fa-exchange-alt"></i>
                <div class="input-group">
                    <label for="toUnit">التحويل إلى - Convert to</label>
                    <select id="toUnit" onchange="convert()"></select>
                </div>
            </div>

            <div class="result" id="result">
                النتيجة ستظهر هنا - Result will appear here
            </div>
        </div>
    </main>

    <script>
        const units = {
            length: {
                meter: { ar: "متر", en: "Meter", factor: 1 },
                kilometer: { ar: "كيلومتر", en: "Kilometer", factor: 1000 },
                centimeter: { ar: "سنتيمتر", en: "Centimeter", factor: 0.01 },
                millimeter: { ar: "ميليمتر", en: "Millimeter", factor: 0.001 },
                inch: { ar: "إنش", en: "Inch", factor: 0.0254 },
                foot: { ar: "قدم", en: "Foot", factor: 0.3048 },
                yard: { ar: "ياردة", en: "Yard", factor: 0.9144 },
                mile: { ar: "ميل", en: "Mile", factor: 1609.34 }
            },
            weight: {
                kilogram: { ar: "كيلوغرام", en: "Kilogram", factor: 1 },
                gram: { ar: "غرام", en: "Gram", factor: 0.001 },
                milligram: { ar: "ميليغرام", en: "Milligram", factor: 0.000001 },
                pound: { ar: "باوند", en: "Pound", factor: 0.453592 },
                ounce: { ar: "أونصة", en: "Ounce", factor: 0.0283495 }
            },
            temperature: {
                celsius: { ar: "سيلسيوس", en: "Celsius" },
                fahrenheit: { ar: "فهرنهايت", en: "Fahrenheit" },
                kelvin: { ar: "كلفن", en: "Kelvin" }
            },
            area: {
                squareMeter: { ar: "متر مربع", en: "Square Meter", factor: 1 },
                squareKilometer: { ar: "كيلومتر مربع", en: "Square Kilometer", factor: 1000000 },
                squareFoot: { ar: "قدم مربع", en: "Square Foot", factor: 0.092903 },
                acre: { ar: "فدان", en: "Acre", factor: 4046.86 },
                hectare: { ar: "هكتار", en: "Hectare", factor: 10000 }
            },
            volume: {
                liter: { ar: "لتر", en: "Liter", factor: 1 },
                milliliter: { ar: "ميليلتر", en: "Milliliter", factor: 0.001 },
                cubicMeter: { ar: "متر مكعب", en: "Cubic Meter", factor: 1000 },
                gallon: { ar: "غالون", en: "Gallon", factor: 3.78541 }
            },
            speed: {
                meterPerSecond: { ar: "متر/ثانية", en: "Meter/Second", factor: 1 },
                kilometerPerHour: { ar: "كم/ساعة", en: "Km/Hour", factor: 0.277778 },
                milePerHour: { ar: "ميل/ساعة", en: "Mile/Hour", factor: 0.44704 }
            }
        };

        function updateUnitOptions() {
            const unitType = document.getElementById('unitType').value;
            const fromUnit = document.getElementById('fromUnit');
            const toUnit = document.getElementById('toUnit');
            
            fromUnit.innerHTML = '';
            toUnit.innerHTML = '';
            
            for (const unit in units[unitType]) {
                const unitData = units[unitType][unit];
                const optionText = `${unitData.ar} - ${unitData.en}`;
                
                fromUnit.add(new Option(optionText, unit));
                toUnit.add(new Option(optionText, unit));
            }
            
            convert();
        }

        function convert() {
            const unitType = document.getElementById('unitType').value;
            const fromValue = parseFloat(document.getElementById('fromValue').value);
            const fromUnit = document.getElementById('fromUnit').value;
            const toUnit = document.getElementById('toUnit').value;

            if (isNaN(fromValue)) {
                document.getElementById('result').innerHTML = 'الرجاء إدخال رقم صحيح - Please enter a valid number';
                return;
            }

            let result;
            if (unitType === 'temperature') {
                result = convertTemperature(fromValue, fromUnit, toUnit);
            } else {
                const fromFactor = units[unitType][fromUnit].factor;
                const toFactor = units[unitType][toUnit].factor;
                result = (fromValue * fromFactor) / toFactor;
            }

            document.getElementById('result').innerHTML = `
                <div class="ar">${fromValue} ${units[unitType][fromUnit].ar} = ${result.toFixed(3)} ${units[unitType][toUnit].ar}</div>
                <div class="en">${fromValue} ${units[unitType][fromUnit].en} = ${result.toFixed(3)} ${units[unitType][toUnit].en}</div>
            `;
        }

        function convertTemperature(value, from, to) {
            let celsius;
            
            // Convert to Celsius first
            switch(from) {
                case 'celsius':
                    celsius = value;
                    break;
                case 'fahrenheit':
                    celsius = (value - 32) * 5/9;
                    break;
                case 'kelvin':
                    celsius = value - 273.15;
                    break;
            }
            
            // Convert from Celsius to target unit
            switch(to) {
                case 'celsius':
                    return celsius;
                case 'fahrenheit':
                    return (celsius * 9/5) + 32;
                case 'kelvin':
                    return celsius + 273.15;
            }
        }

        // Initialize unit options
        updateUnitOptions();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>