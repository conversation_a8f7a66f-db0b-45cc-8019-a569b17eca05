<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Extractor</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            margin: 20px;
        }
        .container {
            max-width: 800px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">Data Extractor from Text</h1>
        <div class="form-group">
            <label for="inputText">Paste the extracted text here:</label>
            <textarea class="form-control" id="inputText" rows="10" placeholder="Paste your text here..."></textarea>
        </div>
        <button id="processBtn" class="btn btn-primary">Process Data</button>
        <button id="downloadBtn" class="btn btn-success mt-3" style="display: none;">Download Excel</button>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
    <script>
        document.getElementById('processBtn').addEventListener('click', function() {
            const inputText = document.getElementById('inputText').value;
            const data = extractData(inputText);
            if (data.length > 0) {
                generateExcel(data);
                document.getElementById('downloadBtn').style.display = 'block';
            } else {
                alert('No valid data found.');
            }
        });

function extractData(text) {
    const data = [];
    const lines = text.split('\n');

    // تعبيرات منتظمة محسنة لاستخراج البيانات
    const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z]+)/g;
    const phoneRegex = /(\+?\d{1,4}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?[\d\s]{7,}/g;
    const linkedinProfileRegex = /https?:\/\/([a-z]{2,3}\.)?linkedin\.com\/in\/[a-zA-Z0-9-_]+/g;
    const instagramProfileRegex = /https?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9._-]+/g;

    lines.forEach(line => {
        const emails = line.match(emailRegex);
        const phones = line.match(phoneRegex);
        const linkedinProfiles = line.match(linkedinProfileRegex);
        const instagramProfiles = line.match(instagramProfileRegex);

        // محاولات لاستخراج الأسماء والوظائف
        const name = extractName(line);
        const job = extractJob(line);

        // إذا تم العثور على أي من البيانات، قم بإضافتها إلى المصفوفة
        if (emails || phones || linkedinProfiles || instagramProfiles || name || job) {
            data.push({
                Name: name || 'N/A',
                Email: emails ? emails.join(', ') : 'N/A',
                Phone: phones ? phones.join(', ') : 'N/A',
                LinkedIn: linkedinProfiles ? linkedinProfiles.join(', ') : 'N/A',
                Instagram: instagramProfiles ? instagramProfiles.join(', ') : 'N/A',
                Job: job || 'N/A'
            });
        }
    });

    return data;
}

// دالة محسنة لاستخراج الأسماء
function extractName(line) {
    // محاولات لاستخراج الأسماء بناءً على وجودها قبل الوظيفة أو كلمات مثل " - " أو "@"
    const nameMatch = line.match(/^([A-Z][a-zA-Z\s]+)(?=\s-\s|\s\|\s|,|@|:)/);
    return nameMatch ? nameMatch[1].trim() : null;
}

// دالة محسنة لاستخراج الوظائف
function extractJob(line) {
    // محاولات لاستخراج الوظيفة بناءً على الكلمات التي تتبع الأنماط الشائعة
    const jobMatch = line.match(/-\s([A-Za-z\s]+)(?=\s-\s|\s\|\s|,)/);
    return jobMatch ? jobMatch[1].trim() : null;
}



        function generateExcel(data) {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');
            
            document.getElementById('downloadBtn').addEventListener('click', function() {
                XLSX.writeFile(workbook, 'extracted_data.xlsx');
            });
        }
    </script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>

</body>
</html>
