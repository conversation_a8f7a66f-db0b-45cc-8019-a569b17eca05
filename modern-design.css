/* Modern Professional Design System */
:root {
    /* Modern Color Palette */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    --secondary-50: #f0fdf4;
    --secondary-100: #dcfce7;
    --secondary-200: #bbf7d0;
    --secondary-300: #86efac;
    --secondary-400: #4ade80;
    --secondary-500: #22c55e;
    --secondary-600: #16a34a;
    --secondary-700: #15803d;
    --secondary-800: #166534;
    --secondary-900: #14532d;
    
    --accent-50: #fef7ff;
    --accent-100: #fce7ff;
    --accent-200: #f8d4fe;
    --accent-300: #f0abfc;
    --accent-400: #e879f9;
    --accent-500: #d946ef;
    --accent-600: #c026d3;
    --accent-700: #a21caf;
    --accent-800: #86198f;
    --accent-900: #701a75;
    
    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Semantic Colors */
    --success: var(--secondary-500);
    --warning: #f59e0b;
    --error: #ef4444;
    --info: var(--primary-500);
    
    /* Typography */
    --font-sans: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Layout */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
    
    /* Z-Index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
}

/* Reset & Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container System */
.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

@media (min-width: 640px) {
    .sm\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .sm\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
    .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .lg\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Spacing Utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

.m-4 { margin: var(--space-4); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Text Utilities */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

/* Color Utilities */
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

.text-primary-600 { color: var(--primary-600); }
.text-secondary-600 { color: var(--secondary-600); }

.bg-white { background-color: white; }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }

/* Border Utilities */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transition Utilities */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Display Utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Width & Height */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

/* ===== MODERN COMPONENTS ===== */

/* Modern Header */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-normal);
}

.modern-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--gray-900);
    text-decoration: none;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-lg);
}

/* Modern Search */
.header-search {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--space-8);
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-full);
    background: var(--gray-50);
    font-size: var(--text-sm);
    transition: var(--transition-normal);
    outline: none;
}

.search-input:focus {
    border-color: var(--primary-500);
    background: white;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--space-4);
    color: var(--gray-400);
    font-size: var(--text-sm);
    pointer-events: none;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--gray-700);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
}

.action-btn:hover {
    border-color: var(--primary-500);
    color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.primary {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

/* Theme Toggle */
.theme-toggle {
    width: 48px;
    height: 24px;
    background: var(--gray-300);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
    transition: var(--transition-normal);
}

.theme-toggle.dark {
    background: var(--primary-500);
}

.theme-toggle-thumb {
    width: 20px;
    height: 20px;
    background: white;
    border-radius: var(--radius-full);
    position: absolute;
    top: 2px;
    left: 2px;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.theme-toggle.dark .theme-toggle-thumb {
    transform: translateX(24px);
}

/* Modern Navigation */
.modern-nav {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 80px;
    z-index: var(--z-sticky);
    margin-top: 80px;
}

.nav-content {
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.nav-tabs {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    gap: var(--space-2);
    padding: var(--space-4) 0;
}

.nav-tabs::-webkit-scrollbar {
    display: none;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    background: transparent;
    border: none;
    color: var(--gray-600);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    white-space: nowrap;
    position: relative;
}

.nav-tab:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

.nav-tab.active {
    background: var(--primary-50);
    color: var(--primary-700);
}

.nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: var(--primary-500);
    border-radius: var(--radius-full);
}

.nav-tab-icon {
    font-size: var(--text-base);
}

/* Modern Hero Section */
.modern-hero {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    padding: var(--space-20) 0 var(--space-16);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.modern-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23e0f2fe" fill-opacity="0.3"><circle cx="30" cy="30" r="2"/></g></svg>');
    opacity: 0.5;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: var(--container-lg);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    display: block;
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--primary-600);
    margin-bottom: var(--space-1);
}

.hero-stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    font-weight: 500;
}

/* Modern Tool Cards */
.tools-section {
    padding: var(--space-16) 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.tool-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    group: hover;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    transform: scaleX(0);
    transition: var(--transition-normal);
    transform-origin: left;
}

.tool-card:hover::before {
    transform: scaleX(1);
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.tool-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.tool-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    transition: var(--transition-normal);
}

.tool-card:hover .tool-icon {
    transform: scale(1.1) rotate(5deg);
}

.tool-external-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-300);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    cursor: pointer;
    transition: var(--transition-normal);
    opacity: 0;
}

.tool-card:hover .tool-external-btn {
    opacity: 1;
}

.tool-external-btn:hover {
    border-color: var(--primary-500);
    color: var(--primary-600);
    background: var(--primary-50);
}

.tool-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.3;
}

.tool-description {
    font-size: var(--text-sm);
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: var(--space-4);
}

.tool-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.tool-tag {
    padding: var(--space-1) var(--space-3);
    background: var(--gray-100);
    color: var(--gray-700);
    font-size: var(--text-xs);
    font-weight: 500;
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

.tool-card:hover .tool-tag {
    background: var(--primary-100);
    color: var(--primary-700);
}

/* Category Icons */
.tool-card[data-category="text"] .tool-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.tool-card[data-category="email"] .tool-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.tool-card[data-category="image"] .tool-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.tool-card[data-category="number"] .tool-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.tool-card[data-category="time"] .tool-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.tool-card[data-category="url"] .tool-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.tool-card[data-category="daily"] .tool-icon {
    background: linear-gradient(135deg, #84cc16, #65a30d);
}

.tool-card[data-category="developer"] .tool-icon {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.tool-card[data-category="investment"] .tool-icon {
    background: linear-gradient(135deg, #059669, #047857);
}

.tool-card[data-category="data"] .tool-icon {
    background: linear-gradient(135deg, #0891b2, #0e7490);
}

.tool-card[data-category="seo"] .tool-icon {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.tool-card[data-category="misc"] .tool-icon {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

/* Modern Footer */
.modern-footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
}

.footer-section h3 {
    color: white;
    font-size: var(--text-lg);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--space-2);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: var(--transition-normal);
    font-size: var(--text-sm);
}

.footer-links a:hover {
    color: var(--primary-400);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    text-align: center;
    color: var(--gray-500);
    font-size: var(--text-sm);
}

/* Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-height: 400px;
    overflow-y: auto;
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: var(--transition-normal);
}

.search-results.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-result {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: var(--transition-normal);
}

.search-result:hover {
    background: var(--gray-50);
}

.search-result:last-child {
    border-bottom: none;
}

.search-result h4 {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.search-result p {
    font-size: var(--text-xs);
    color: var(--gray-600);
    margin: 0;
}

/* Loading States */
.loading {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--gray-500);
    font-size: var(--text-sm);
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary-500);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 640px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
        padding: var(--space-4) var(--space-4);
    }

    .header-search {
        order: 2;
        margin: 0;
        max-width: 100%;
    }

    .header-actions {
        order: 3;
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .modern-nav {
        top: 120px;
        margin-top: 120px;
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .nav-tabs {
        padding: var(--space-3) 0;
    }

    .nav-tab {
        padding: var(--space-2) var(--space-4);
        font-size: var(--text-xs);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-3);
    }

    .tools-section {
        padding: var(--space-12) 0;
    }

    .section-title {
        font-size: var(--text-2xl);
    }

    .section-subtitle {
        font-size: var(--text-base);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }
}

@media (min-width: 1024px) {
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }

    .hero-stats {
        gap: var(--space-12);
    }
}

/* Dark Mode */
[data-theme="dark"] {
    color-scheme: dark;
}

[data-theme="dark"] body {
    background-color: var(--gray-900);
    color: var(--gray-100);
}

[data-theme="dark"] .modern-header {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: var(--gray-800);
}

[data-theme="dark"] .modern-header.scrolled {
    background: rgba(15, 23, 42, 0.98);
}

[data-theme="dark"] .logo {
    color: var(--gray-100);
}

[data-theme="dark"] .search-input {
    background: var(--gray-800);
    border-color: var(--gray-700);
    color: var(--gray-100);
}

[data-theme="dark"] .search-input:focus {
    background: var(--gray-700);
    border-color: var(--primary-500);
}

[data-theme="dark"] .action-btn {
    background: var(--gray-800);
    border-color: var(--gray-700);
    color: var(--gray-300);
}

[data-theme="dark"] .action-btn:hover {
    border-color: var(--primary-500);
    color: var(--primary-400);
}

[data-theme="dark"] .modern-nav {
    background: var(--gray-900);
    border-bottom-color: var(--gray-800);
}

[data-theme="dark"] .nav-tab {
    color: var(--gray-400);
}

[data-theme="dark"] .nav-tab:hover {
    background: var(--gray-800);
    color: var(--gray-200);
}

[data-theme="dark"] .nav-tab.active {
    background: var(--primary-900);
    color: var(--primary-300);
}

[data-theme="dark"] .modern-hero {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
}

[data-theme="dark"] .hero-title {
    color: var(--gray-100);
}

[data-theme="dark"] .hero-subtitle {
    color: var(--gray-400);
}

[data-theme="dark"] .tools-section {
    background: var(--gray-900);
}

[data-theme="dark"] .section-title {
    color: var(--gray-100);
}

[data-theme="dark"] .section-subtitle {
    color: var(--gray-400);
}

[data-theme="dark"] .tool-card {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-theme="dark"] .tool-card:hover {
    border-color: var(--primary-600);
}

[data-theme="dark"] .tool-title {
    color: var(--gray-100);
}

[data-theme="dark"] .tool-description {
    color: var(--gray-400);
}

[data-theme="dark"] .tool-tag {
    background: var(--gray-700);
    color: var(--gray-300);
}

[data-theme="dark"] .tool-card:hover .tool-tag {
    background: var(--primary-800);
    color: var(--primary-200);
}

[data-theme="dark"] .search-results {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-theme="dark"] .search-result:hover {
    background: var(--gray-700);
}

[data-theme="dark"] .search-result h4 {
    color: var(--gray-100);
}

[data-theme="dark"] .search-result p {
    color: var(--gray-400);
}
