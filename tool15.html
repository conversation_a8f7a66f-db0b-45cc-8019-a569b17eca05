<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل الأرقام إلى كلمات - Number to Words</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .converter-form {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
        .number-input {
            width: 100%;
            padding: 10px;
            font-size: 1.2rem;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            margin-bottom: 15px;
        }
        .result-container {
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
        }
        .result-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
        }
        .result-box h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .result-text {
            color: var(--text-color);
            line-height: 1.6;
            font-size: 1.1rem;
        }
        .currency-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>تحويل الأرقام إلى كلمات <span>Number to Words</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="converter-form">
            <input type="text" id="numberInput" class="number-input" placeholder="أدخل الرقم / Enter number">
            
            <select id="currencySelect" class="currency-select">
                <option value="none">بدون عملة / No Currency</option>
                <option value="USD">دولار أمريكي / US Dollar</option>
                <option value="EUR">يورو / Euro</option>
                <option value="GBP">جنيه إسترليني / British Pound</option>
                <option value="SAR">ريال سعودي / Saudi Riyal</option>
                <option value="EGP">جنيه مصري / Egyptian Pound</option>
            </select>

            <div class="button-group">
                <button id="convertBtn" class="ar">تحويل</button>
                <button id="convertBtn" class="en">Convert</button>
                
                <button id="copyBtn" class="ar" disabled>نسخ النتائج</button>
                <button id="copyBtn" class="en" disabled>Copy Results</button>
            </div>
        </div>

        <div class="result-container" style="display: none;">
            <div class="result-box">
                <h3 class="ar">بالعربية</h3>
                <h3 class="en">In Arabic</h3>
                <div id="arabicResult" class="result-text"></div>
            </div>

            <div class="result-box">
                <h3 class="ar">بالإنجليزية</h3>
                <h3 class="en">In English</h3>
                <div id="englishResult" class="result-text"></div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const numberInput = document.getElementById('numberInput');
            const currencySelect = document.getElementById('currencySelect');
            const convertBtn = document.getElementById('convertBtn');
            const copyBtn = document.getElementById('copyBtn');
            const resultContainer = document.querySelector('.result-container');
            const arabicResult = document.getElementById('arabicResult');
            const englishResult = document.getElementById('englishResult');

            // Currency configurations
            const currencies = {
                USD: { ar: 'دولار أمريكي', en: 'US Dollars', arFraction: 'سنت', enFraction: 'cents' },
                EUR: { ar: 'يورو', en: 'Euros', arFraction: 'سنت', enFraction: 'cents' },
                GBP: { ar: 'جنيه إسترليني', en: 'British Pounds', arFraction: 'بنس', enFraction: 'pence' },
                SAR: { ar: 'ريال سعودي', en: 'Saudi Riyals', arFraction: 'هللة', enFraction: 'halalas' },
                EGP: { ar: 'جنيه مصري', en: 'Egyptian Pounds', arFraction: 'قرش', enFraction: 'piastres' }
            };

            // Arabic number words
            const arabicWords = {
                ones: ['صفر', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'],
                tens: ['', 'عشرة', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'],
                hundreds: ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'],
                thousands: ['', 'ألف', 'مليون', 'مليار'],
                and: 'و'
            };

            // English number words
            const englishWords = {
                ones: ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'],
                tens: ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'],
                teens: ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'],
                scales: ['', 'thousand', 'million', 'billion']
            };

            function convertToArabicWords(number) {
                if (number === 0) return 'صفر';
                
                let words = '';
                let scale = 0;

                while (number > 0) {
                    const segment = number % 1000;
                    if (segment !== 0) {
                        const segmentWords = convertSegmentToArabic(segment);
                        if (scale > 0) {
                            words = segmentWords + ' ' + arabicWords.thousands[scale] + ' ' + words;
                        } else {
                            words = segmentWords;
                        }
                    }
                    number = Math.floor(number / 1000);
                    scale++;
                }

                return words.trim();
            }

            function convertSegmentToArabic(number) {
                const hundreds = Math.floor(number / 100);
                const tens = Math.floor((number % 100) / 10);
                const ones = number % 10;

                let words = '';

                if (hundreds > 0) {
                    words += arabicWords.hundreds[hundreds];
                }

                if (tens > 0 || ones > 0) {
                    if (words !== '') words += ' ' + arabicWords.and + ' ';
                    
                    if (tens === 1) {
                        words += arabicWords.ones[ones] + ' ' + arabicWords.tens[1];
                    } else if (tens > 1) {
                        if (ones > 0) {
                            words += arabicWords.ones[ones] + ' ' + arabicWords.and + ' ';
                        }
                        words += arabicWords.tens[tens];
                    } else if (ones > 0) {
                        words += arabicWords.ones[ones];
                    }
                }

                return words;
            }

            function convertToEnglishWords(number) {
                if (number === 0) return 'zero';
                
                let words = '';
                let scale = 0;

                while (number > 0) {
                    const segment = number % 1000;
                    if (segment !== 0) {
                        const segmentWords = convertSegmentToEnglish(segment);
                        if (scale > 0) {
                            words = segmentWords + ' ' + englishWords.scales[scale] + ' ' + words;
                        } else {
                            words = segmentWords;
                        }
                    }
                    number = Math.floor(number / 1000);
                    scale++;
                }

                return words.trim();
            }

            function convertSegmentToEnglish(number) {
                const hundreds = Math.floor(number / 100);
                const remainder = number % 100;
                const tens = Math.floor(remainder / 10);
                const ones = remainder % 10;

                let words = '';

                if (hundreds > 0) {
                    words += englishWords.ones[hundreds] + ' hundred';
                    if (remainder > 0) words += ' and ';
                }

                if (tens === 1) {
                    words += englishWords.teens[ones];
                } else {
                    if (tens > 1) {
                        words += englishWords.tens[tens];
                        if (ones > 0) words += '-';
                    }
                    if (ones > 0 || tens === 0) words += englishWords.ones[ones];
                }

                return words;
            }

            function formatWithCurrency(number, words, currency) {
                const parts = number.toString().split('.');
                const mainPart = convertToWords(parseInt(parts[0]));
                let fractionPart = '';

                if (parts.length > 1) {
                    const fraction = parseInt(parts[1].padEnd(2, '0').substr(0, 2));
                    if (fraction > 0) {
                        fractionPart = convertToWords(fraction);
                    }
                }

                const currencyInfo = currencies[currency];
                return mainPart + ' ' + currencyInfo[words] + 
                       (fractionPart ? ' و ' + fractionPart + ' ' + currencyInfo[words + 'Fraction'] : '');
            }

            function convertToWords(number) {
                const lang = document.documentElement.lang;
                return lang === 'ar' ? convertToArabicWords(number) : convertToEnglishWords(number);
            }

            function convert() {
                const number = parseFloat(numberInput.value.replace(/[^0-9.]/g, ''));
                if (isNaN(number)) {
                    alert('Please enter a valid number');
                    return;
                }

                const currency = currencySelect.value;
                
                if (currency === 'none') {
                    arabicResult.textContent = convertToArabicWords(Math.floor(number));
                    englishResult.textContent = convertToEnglishWords(Math.floor(number));
                } else {
                    arabicResult.textContent = formatWithCurrency(number, 'ar', currency);
                    englishResult.textContent = formatWithCurrency(number, 'en', currency);
                }

                resultContainer.style.display = 'block';
                copyBtn.disabled = false;
            }

            // Event listeners
            convertBtn.addEventListener('click', convert);
            
            numberInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') convert();
            });

            copyBtn.addEventListener('click', () => {
                const lang = document.documentElement.lang;
                const text = lang === 'ar' ? arabicResult.textContent : englishResult.textContent;
                window.utils.copyToClipboard(text);
            });

            // Load saved values
            numberInput.value = localStorage.getItem('numberToWords_number') || '';
            currencySelect.value = localStorage.getItem('numberToWords_currency') || 'none';

            // Save values on change
            numberInput.addEventListener('change', () => {
                localStorage.setItem('numberToWords_number', numberInput.value);
            });
            currencySelect.addEventListener('change', () => {
                localStorage.setItem('numberToWords_currency', currencySelect.value);
            });
        });
    </script>
</body>
</html>