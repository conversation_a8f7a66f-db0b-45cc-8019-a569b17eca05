<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير SEO شامل</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
body {
    font-family: '<PERSON><PERSON>wal', Arial, sans-serif;
    background: linear-gradient(135deg, #f0f4f8, #d9e2ec);
    color: #2c3e50;
    margin: 0;
    padding: 30px;
    line-height: 1.8;
}

h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-weight: 700;
}

h3 {
    color: #3498db;
    font-size: 1.8rem;
    text-align: right;
    margin-top: 20px;
    font-weight: 600;
}

h4 {
    border-bottom: 3px solid #3498db;
    padding-bottom: 12px;
    text-align: right;
    font-size: 1.4rem;
    color: #2980b9;
    font-weight: 600;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    background: #fff;
}

th, td {
    padding: 15px;
    text-align: right;
    border: 1px solid #e0e6ed;
    font-size: 1.1rem;
}

th {
    background: linear-gradient(to right, #3498db, #2980b9);
    color: white;
    font-weight: 700;
}

td {
    background: #f9fafc;
}

ul {
    list-style-type: square;
    padding-left: 25px;
    text-align: right;
    direction: rtl;
    margin-bottom: 20px;
}

li {
    margin-bottom: 12px;
    font-size: 1.1rem;
    color: #34495e;
}

#seo-report {
    background: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    margin-top: 30px;
}

#seo-report h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 20px;
}

#seo-report h3 {
    font-size: 1.8rem;
    margin-top: 25px;
    margin-bottom: 15px;
}

#seo-report h4 {
    font-size: 1.4rem;
    margin-top: 20px;
    margin-bottom: 10px;
}

#seo-report p {
    font-size: 1.2rem;
    color: #34495e;
    margin-bottom: 10px;
}

#seo-report ul {
    padding-left: 40px;
}

#seo-report li {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

#seo-report ol {
    padding-left: 40px;
}

#seo-report ol li {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

label {
    text-align: right;
    direction: rtl;
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.container {
    max-width: 900px;
    margin: 60px auto;
    padding: 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.form-group {
    background: #f9fafc;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.form-group:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e0e6ed;
    padding: 12px;
    font-size: 1rem;
}

.btn {
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(to right, #3498db, #2980b9);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(to right, #2980b9, #1f6390);
}

.btn-secondary {
    background: #7f8c8d;
    border: none;
}

.btn-secondary:hover {
    background: #6c7778;
}

.btn-success {
    background: #27ae60;
    border: none;
}

.btn-success:hover {
    background: #219653;
}

.btn-info {
    background: #00bcd4;
    border: none;
}

.btn-info:hover {
    background: #00acc1;
}

.button-container {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    background: #fff;
}

.card h4 {
    margin-bottom: 15px;
}

.accordion .card {
    margin-bottom: 10px;
}

.accordion .card-header {
    background: linear-gradient(to right, #3498db, #2980b9);
    color: white;
    cursor: pointer;
    padding: 15px;
    border-radius: 8px;
}

.accordion .card-header h4 {
    margin: 0;
    border: none;
    color: white;
}

.accordion .card-body {
    background: #f9fafc;
    border-radius: 0 0 8px 8px;
}

.icon {
    margin-left: 10px;
    font-size: 1.2rem;
    color: #3498db;
}

.export-report h1 {
    font-size: 36pt;
    color: #2c3e50;
    text-align: center;
}

.export-report h3 {
    font-size: 24pt;
    color: #3498db;
}

.export-report h4 {
    font-size: 18pt;
    color: #2980b9;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.export-report p, .export-report li {
    font-size: 14pt;
    color: #34495e;
}

.export-report .icon {
    font-size: 14pt;
    vertical-align: middle;
    margin-left: 10px;
}

.progress-bar {
    width: 100%;
    background: #e0e6ed;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.progress {
    height: 100%;
    background: linear-gradient(to right, #27ae60, #219653);
    transition: width 1s ease-in-out;
}
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
</head>

<body>
    <div class="container mt-5">
        <h1 class="text-center">تحليل SEO للموقع</h1>
        <div class="accordion mt-4" id="seoAccordion">
            <div class="card">
                <div class="card-header" id="headingBasic" data-toggle="collapse" data-target="#collapseBasic" aria-expanded="true" aria-controls="collapseBasic">
                    <h4>معلومات أساسية</h4>
                </div>
                <div id="collapseBasic" class="collapse show" aria-labelledby="headingBasic">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="domain"><i class="fas fa-globe icon"></i> رابط الموقع:</label>
                            <input type="text" class="form-control" id="domain" placeholder="أدخل رابط الموقع" value="google.com" required>
                        </div>
                        <div class="form-group">
                            <label for="siteAge"><i class="fas fa-calendar-alt icon"></i> عمر الموقع (بالسنوات):</label>
                            <input type="number" class="form-control" id="siteAge" placeholder="أدخل عمر الموقع" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="isWordPress"><i class="fab fa-wordpress icon"></i> هل الموقع يستخدم ووردبريس؟</label>
                            <select id="isWordPress" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header" id="headingPerformance" data-toggle="collapse" data-target="#collapsePerformance" aria-expanded="false" aria-controls="collapsePerformance">
                    <h4>الأداء والسرعة</h4>
                </div>
                <div id="collapsePerformance" class="collapse" aria-labelledby="headingPerformance">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="desktopSpeed"><i class="fas fa-desktop icon"></i> سرعة الموقع على الديسكتوب بالثواني:</label>
                            <input type="text" class="form-control" id="desktopSpeed" placeholder="أدخل سرعة الموقع (ثواني)" value="2" required>
                        </div>
                        <div class="form-group">
                            <label for="mobileSpeed"><i class="fas fa-mobile-alt icon"></i> سرعة الموقع على الموبايل بالثواني:</label>
                            <input type="text" class="form-control" id="mobileSpeed" placeholder="أدخل سرعة الموقع (ثواني)" value="2" required>
                        </div>
                        <div class="form-group">
                            <label for="coreWebVitals"><i class="fas fa-tachometer-alt icon"></i> هل يحقق الموقع معايير Core Web Vitals؟</label>
                            <select id="coreWebVitals" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header" id="headingAuthority" data-toggle="collapse" data-target="#collapseAuthority" aria-expanded="false" aria-controls="collapseAuthority">
                    <h4>السلطة والترتيب</h4>
                </div>
                <div id="collapseAuthority" class="collapse" aria-labelledby="headingAuthority">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="mozDomainAuthority"><i class="fas fa-chart-line icon"></i> ترتيب Moz Domain Authority:</label>
                            <input type="number" class="form-control" id="mozDomainAuthority" placeholder="أدخل ترتيب Moz Domain Authority" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="ahrefsDomainRank"><i class="fas fa-link icon"></i> ترتيب Ahrefs Domain Rank:</label>
                            <input type="number" class="form-control" id="ahrefsDomainRank" placeholder="أدخل ترتيب Ahrefs Domain Rank" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="semrushAS"><i class="fas fa-signal icon"></i> ترتيب Semrush AS:</label>
                            <input type="number" class="form-control" id="semrushAS" placeholder="أدخل ترتيب Semrush AS" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="majesticTrustFlow"><i class="fas fa-shield-alt icon"></i> ترتيب Majestic Trust Flow:</label>
                            <input type="number" class="form-control" id="majesticTrustFlow" placeholder="أدخل ترتيب Majestic Trust Flow" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="backlinkQuality"><i class="fas fa-backward icon"></i> هل عدد الباك لينك التي يحصل عليها الموقع مناسب وجودته عالية؟</label>
                            <select id="backlinkQuality" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header" id="headingContent" data-toggle="collapse" data-target="#collapseContent" aria-expanded="false" aria-controls="collapseContent">
                    <h4>المحتوى والهيكلة</h4>
                </div>
                <div id="collapseContent" class="collapse" aria-labelledby="headingContent">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="hasH1Tag"><i class="fas fa-heading icon"></i> هل يوجد وسم H1 في الرئيسية؟</label>
                            <select id="hasH1Tag" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="repeatedH1"><i class="fas fa-clone icon"></i> هل الوسم H1 متكرر في الصفحة الرئيسية؟</label>
                            <select id="repeatedH1" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="keywordInDomain"><i class="fas fa-key icon"></i> هل يحتوي اسم الدومين على الكلمة المفتاحية المستهدفة؟</label>
                            <select id="keywordInDomain" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="siteTitle"><i class="fas fa-tag icon"></i> هل عنوان الموقع مناسب ويحتوي على الكلمة المفتاحية المستهدفة؟</label>
                            <select id="siteTitle" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="titleLength"><i class="fas fa-ruler icon"></i> هل طول العنوان أقل من 60 حرف؟</label>
                            <select id="titleLength" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="descriptionLength"><i class="fas fa-ruler-horizontal icon"></i> هل طول الوصف أقل من 160 حرف؟</label>
                            <select id="descriptionLength" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="hasAltTags"><i class="fas fa-image icon"></i> هل alt tag للصور مضاف جيداً؟</label>
                            <select id="hasAltTags" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="goodSectionDescription"><i class="fas fa-folder-open icon"></i> هل يوجد وصف جيد للأقسام الرئيسية في الموقع؟</label>
                            <select id="goodSectionDescription" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="goodProductDescriptions"><i class="fas fa-box icon"></i> هل تحتوي المنتجات في الموقع على وصف وعناوين جيدة؟</label>
                            <select id="goodProductDescriptions" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contentMarketing"><i class="fas fa-blog icon"></i> هل توجد مدونة للموقع بحيث يمكن العمل على التسويق بالمحتوى واستهداف المزيد من الكلمات المفتاحية؟</label>
                            <select id="contentMarketing" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="articlesSEO"><i class="fas fa-file-alt icon"></i> هل المقالات مهيئة جيدا للـ SEO من ناحية العنوان والوسوم والوصف؟</label>
                            <select id="articlesSEO" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="pagesSEO"><i class="fas fa-file icon"></i> هل الصفحات مهيئة جيدا للـ SEO من ناحية العنوان والوسوم والوصف؟</label>
                            <select id="pagesSEO" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contentQuantity"><i class="fas fa-boxes icon"></i> هل يحتوي الموقع على عدد جيد من المقالات والمحتوى؟</label>
                            <select id="contentQuantity" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header" id="headingTechnical" data-toggle="collapse" data-target="#collapseTechnical" aria-expanded="false" aria-controls="collapseTechnical">
                    <h4>التكامل التقني</h4>
                </div>
                <div id="collapseTechnical" class="collapse" aria-labelledby="headingTechnical">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="sslIssues"><i class="fas fa-lock icon"></i> هل توجد مشكلات تتعلق بـ SSL أو HTTPS Redirect؟</label>
                            <select id="sslIssues" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="noindexBrokenlinks"><i class="fas fa-unlink icon"></i> هل توجد في الموقع مشكلة Noindex أو Brokenlinks؟</label>
                            <select id="noindexBrokenlinks" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="linkStructureIssues"><i class="fas fa-sitemap icon"></i> هل توجد مشكلة في بنية الروابط؟</label>
                            <select id="linkStructureIssues" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="navTags"><i class="fas fa-bars icon"></i> هل القوائم موسومة بالوسم Nav؟</label>
                            <select id="navTags" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="essentialPages"><i class="fas fa-file-contract icon"></i> هل يوجد بالموقع الصفحات الأساسية "الخصوصية" و "شروط الاستخدام"؟</label>
                            <select id="essentialPages" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="sitemap"><i class="fas fa-map icon"></i> هل يوجد خريطة للموقع تعمل جيدا sitemap.xml؟</label>
                            <select id="sitemap" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="googleConsole"><i class="fab fa-google icon"></i> هل الموقع مضاف إلى Google Console؟</label>
                            <select id="googleConsole" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="googleAnalytics"><i class="fas fa-chart-bar icon"></i> هل احصائيات جوجل اناليتكس مربوطة بالموقع؟</label>
                            <select id="googleAnalytics" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="openGraph"><i class="fas fa-share-alt icon"></i> هل الـ OpenGraph مضاف جيدا للموقع؟</label>
                            <select id="openGraph" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="favicon"><i class="fas fa-icon icon"></i> هل لدى الموقع Favicon؟</label>
                            <select id="favicon" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="robotsTxt"><i class="fas fa-robot icon"></i> هل يحتوي الموقع على ملف robots.txt صحيح؟</label>
                            <select id="robotsTxt" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header" id="headingMarketing" data-toggle="collapse" data-target="#collapseMarketing" aria-expanded="false" aria-controls="collapseMarketing">
                    <h4>التسويق والكلمات المفتاحية</h4>
                </div>
                <div id="collapseMarketing" class="collapse" aria-labelledby="headingMarketing">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="competitionLevel"><i class="fas fa-trophy icon"></i> هل الموقع باللغة الانجليزية صعب المنافسة ويصعب إنشاء المحتوى فيه؟</label>
                            <select id="competitionLevel" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="socialSignals"><i class="fas fa-share-square icon"></i> هل يحصل الموقع على Social signals مناسبة؟</label>
                            <select id="socialSignals" class="form-control" required>
                                <option value="no">لا</option>
                                <option value="yes">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="keywordResearch"><i class="fas fa-search icon"></i> هل يحتاج الموقع إلى بحث كلمات مفتاحية؟</label>
                            <select id="keywordResearch" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="targetedKeywords"><i class="fas fa-bullseye icon"></i> هل يتم استهداف شبكة كلمات مفتاحية ذات صلة بالكلمة الرئيسية؟</label>
                            <select id="targetedKeywords" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="domainKeyword"><i class="fas fa-domain icon"></i> هل يحتوي الدومين على الكلمة المفتاحية الرئيسية أو جزء منها؟</label>
                            <select id="domainKeyword" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contentFreshness"><i class="fas fa-sync-alt icon"></i> هل المحتوى يتم تحديثه بشكل دوري؟</label>
                            <select id="contentFreshness" class="form-control" required>
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form id="seo-form" class="mt-4">
            <button type="submit" class="btn btn-primary btn-block">إرسال التقرير</button>
        </form>

        <div class="x">
            <div id="seo-report" class="d-none"></div>
            <div class="button-container">
                <br />
                <button id="copy-html" class="btn btn-primary">نسخ التقرير ككود HTML</button>
                <button id="copy-general" class="btn btn-secondary">نسخ التقرير بشكل عام</button>
                <button id="export-word" class="btn btn-success">تصدير كملف Word</button>
                <button id="export-gdocs" class="btn btn-info">تصدير إلى Google Docs</button>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        document.getElementById('seo-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // قراءة المدخلات
            const domain = document.getElementById('domain').value;
            const siteAge = document.getElementById('siteAge').value;
            const isWordPress = document.getElementById('isWordPress').value;
            const desktopSpeed = document.getElementById('desktopSpeed').value;
            const mobileSpeed = document.getElementById('mobileSpeed').value;
            const mozDomainAuthority = document.getElementById('mozDomainAuthority').value;
            const ahrefsDomainRank = document.getElementById('ahrefsDomainRank').value;
            const semrushAS = document.getElementById('semrushAS').value;
            const majesticTrustFlow = document.getElementById('majesticTrustFlow').value;
            const hasH1Tag = document.getElementById('hasH1Tag').value;
            const titleLength = document.getElementById('titleLength').value;
            const descriptionLength = document.getElementById('descriptionLength').value;
            const hasAltTags = document.getElementById('hasAltTags').value;
            const sslIssues = document.getElementById('sslIssues').value;
            const goodSectionDescription = document.getElementById('goodSectionDescription').value;
            const goodProductDescriptions = document.getElementById('goodProductDescriptions').value;
            const articlesSEO = document.getElementById('articlesSEO').value;
            const pagesSEO = document.getElementById('pagesSEO').value;
            const contentQuantity = document.getElementById('contentQuantity').value;
            const linkStructureIssues = document.getElementById('linkStructureIssues').value;
            const navTags = document.getElementById('navTags').value;
            const essentialPages = document.getElementById('essentialPages').value;
            const sitemap = document.getElementById('sitemap').value;
            const googleConsole = document.getElementById('googleConsole').value;
            const googleAnalytics = document.getElementById('googleAnalytics').value;
            const openGraph = document.getElementById('openGraph').value;
            const favicon = document.getElementById('favicon').value;
            const keywordResearch = document.getElementById('keywordResearch').value;
            const targetedKeywords = document.getElementById('targetedKeywords').value;
            const domainKeyword = document.getElementById('domainKeyword').value;
            const socialSignals = document.getElementById('socialSignals').value;
            const repeatedH1 = document.getElementById('repeatedH1').value;
            const siteTitle = document.getElementById('siteTitle').value;
            const keywordInDomain = document.getElementById('keywordInDomain').value;
            const noindexBrokenlinks = document.getElementById('noindexBrokenlinks').value;
            const contentMarketing = document.getElementById('contentMarketing').value;
            const competitionLevel = document.getElementById('competitionLevel').value;
            const backlinkQuality = document.getElementById('backlinkQuality').value;
            const coreWebVitals = document.getElementById('coreWebVitals').value;
            const robotsTxt = document.getElementById('robotsTxt').value;
            const contentFreshness = document.getElementById('contentFreshness').value;

            // بناء التقرير
            let advantages = {
                basic: '',
                performance: '',
                authority: '',
                content: '',
                technical: '',
                marketing: ''
            };
            let disadvantages = {
                basic: '',
                performance: '',
                authority: '',
                content: '',
                technical: '',
                marketing: ''
            };

            // تصنيف النتائج إلى مزايا وعيوب
            if (isWordPress === 'yes') {
                advantages.basic += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>الموقع مبني على ووردبريس:</strong> هذا النظام يعد من الأنظمة الأكثر شيوعاً في إدارة المحتوى، حيث يوفر واجهة سهلة الاستخدام ومجموعة واسعة من القوالب والإضافات، مما يسهل عملية إدارة الموقع وتخصيصه بالنسبة للـ SEO.<br>\n';
            } else {
                disadvantages.basic += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>الموقع لا يستخدم ووردبريس:</strong> الإعتماد على البرمجيات الخاصة وأنظمة إدارة المحتوى الأخرى قد يؤدي إلى صعوبة في إدارة المحتوى وتخصيص الموقع داخليا للسيو بالكفاءة العالية المطلوبة، قد نحتاج إلى إرسال بعض التوصيات للمطورين لعمل بعض التعديلات البرمجية المطلوبة للسيو.<br>\n';
            }

            if (competitionLevel === 'no') {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>الموقع باللغة العربية:</strong> مما يجعل التوسع في المحتوى أسهل وفعّال، كما أن المنافسة في المحتوى العربي تعتبر منافسة متوسطة ويمكن الوصول من خلالها إلى نتائج أسرع.<br>\n';
            } else {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>الموقع بلغة أجنبية:</strong> قد يزيد هذا من تحديات إنشاء المحتوى، وصعوبة المنافسة.<br>\n';
            }

            if (desktopSpeed < 3) {
                advantages.performance += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>سرعة الموقع على الديسكتوب جيدة:</strong> سرعة التحميل السريعة تعزز تجربة المستخدم وتساعد في تقليل معدل الارتداد، مما يزيد من بقاء الزوار على الموقع ويحسن ذلك من ظهور الموقع أكثر في محركات البحث.<br>\n';
            } else {
                disadvantages.performance += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>سرعة الموقع على الديسكتوب تحتاج تحسين:</strong> سرعة التحميل البطيئة قد تؤثر سلبًا على تجربة المستخدم، مما يؤدي إلى نفور الزوار.<br>\n';
            }

            if (mobileSpeed < 3) {
                advantages.performance += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>سرعة الموقع على الموبايل جيدة:</strong> هذا الأمر يسهل على المستخدمين الوصول إلى المحتوى على الهواتف الذكية، مما يزيد من التفاعل مع الموقع.<br>\n';
            } else {
                disadvantages.performance += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>سرعة الموقع على الموبايل تحتاج تحسين:</strong> المواقع البطيئة على الهواتف تؤثر سلبًا على تجربة المستخدم، حيث يفضل معظم المستخدمين السرعة.<br>\n';
            }

            if (parseInt(mozDomainAuthority) > 30) {
                advantages.authority += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>ترتيب Moz Domain Authority جيد:</strong> هذا يشير إلى أن الموقع يمتلك سمعة قوية على الويب، مما يساعد في تحسين فرص ظهوره في نتائج البحث.<br>\n';
            } else {
                disadvantages.authority += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>ترتيب Moz Domain Authority منخفض جدا يحتاج إلى تحسين:</strong> يحتاج الموقع إلى تحسين ترتيب Moz Domain Authority إلى 30+، سوف يساعد ذلك الموقع على المنافسة في النتائج الأولى في محرك البحث.<br>\n';
            }

            if (parseInt(ahrefsDomainRank) > 30) {
                advantages.authority += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>ترتيب Ahrefs Domain Rank جيد:</strong> يشير إلى قوة الموقع في محركات البحث، مما يزيد من فرص جذب الزوار.<br>\n';
            } else {
                disadvantages.authority += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>ترتيب Ahrefs Domain Rank منخفض جدا يحتاج إلى تحسين:</strong> يحتاج الموقع إلى تحسين ترتيب Ahrefs Domain Rank إلى 30+، سوف يساعد ذلك الموقع على المنافسة في النتائج الأولى في محرك البحث.<br>\n';
            }

            if (parseInt(semrushAS) > 30) {
                advantages.authority += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>ترتيب Semrush AS جيد:</strong> يدل على أن الموقع يستفيد من تحسين محركات البحث بشكل فعال، مما يساعد في جذب المزيد من الزوار.<br>\n';
            } else {
                disadvantages.authority += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>ترتيب Semrush AS يحتاج تحسين:</strong> يشير إلى الحاجة إلى بناء روابط عالية الجودة وتحسين استراتيجيات SEO.<br>\n';
            }

            if (parseInt(majesticTrustFlow) > 20) {
                advantages.authority += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>ترتيب Majestic Trust Flow جيد:</strong> يشير إلى جودة الروابط الخلفية وثقة الموقع، مما يعزز ترتيبه في محركات البحث.<br>\n';
            } else {
                disadvantages.authority += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>ترتيب Majestic Trust Flow منخفض:</strong> يحتاج الموقع إلى تحسين جودة الروابط الخلفية لزيادة الثقة.<br>\n';
            }

            if (socialSignals === 'yes') {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>الموقع يحصل على Social signals مناسبة:</strong> يساعد ذلك في تعزيز وجوده الاجتماعي وجذب الزوار المحتملين.<br>\n';
            } else {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم الحصول على Social signals مناسبة:</strong> يجب العمل على تعزيز وجود الموقع على الشبكات الاجتماعية وزيادة السوشيال سيجنالز لتحسين التفاعل والظهور.<br>\n';
            }

            if (backlinkQuality === 'yes') {
                advantages.authority += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عدد الباك لينك جيد وجودته عالية:</strong> هذا رائع جدا حيث يعزز SEO ويساعد على زيادة سمعة الموقع في محركات البحث وزيادة الظهور في نتائج متقدمة في محركات البحث والقدرة على المنافسة.<br>\n';
            } else {
                disadvantages.authority += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>قلة أو ضعف جودة الباك لينك:</strong> يجب تحسين روابط الباك لينك للحصول على روابط خلفية أكثر وبجودة أعلى، كي يتمكن الموقع من الحصول على سمعة أعلى لدى محركات البحث والقدرة على المنافسة والظهور في نتائج متقدمة في محركات البحث.<br>\n';
            }

            if (hasH1Tag === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وسم H1 موجود في الرئيسية:</strong> هذا رائع جدا حيث يعتبر هذا العنصر مهمًا لتحسين محركات البحث، حيث يساعد محركات البحث في فهم محتوى الصفحة.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود وسم H1 في الرئيسية:</strong> قد يؤدي ذلك إلى صعوبة محركات البحث في فهم محتوى الصفحة، من الضروري جدا تحسين الصفحة الرئيسية وإضافة تعريف لها بالوسم H1.<br>\n';
            }

            if (repeatedH1 === 'yes') {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>تكرار الوسم H1 في الصفحة الرئيسية:</strong> هذا قد يؤدي إلى تعقيد فهم محركات البحث للصفحة. من المستحسن تحسين الوسوم لتحقيق أفضل أداء.<br>\n';
            } else {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عدم تكرار الوسم H1 في الصفحة الرئيسية:</strong> هذا جيد، حيث يُسهِّل على محركات البحث فهم محتوى الصفحة بدون مشاكل.<br>\n';
            }

            if (keywordInDomain === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود الكلمة المفتاحية في اسم الدومين:</strong> هذا يعزز SEO ويزيد من ثقة الزوار بالموقع، كما يساهم بشكل كبير في تصدر نتائج البحث في هذه الكلمة المفتاحية الرئيسية المستهدفة.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود الكلمة المفتاحية في اسم الدومين:</strong> قد يؤثر ذلك على سهولة التعرف على الموقع واستهدافه للكلمات المفتاحية الرئيسية.<br>\n';
            }

            if (siteTitle === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عنوان الموقع مناسب ويحتوي على الكلمة المفتاحية:</strong> هذا يعزز SEO ويزيد من قابلية العثور على الموقع في الكلمات المفتاحية المستهدفة.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عنوان الموقع غير مناسب أو لا يحتوي على الكلمة المفتاحية:</strong> يجب تحسين العنوان لزيادة فرص الظهور في نتائج البحث.<br>\n';
            }

            if (titleLength === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>طول العنوان أقل من 60 حرف:</strong> هذا جيد جدا حيث يساعد في تحسين محركات البحث ويظهر بشكل جيد في نتائج البحث.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>طول العنوان أكثر من 60 حرف:</strong> قد يؤدي ذلك إلى تقطيعه في نتائج البحث ويؤثر على نسبة النقر إلى الظهور لذلك فهو بحاجة إلى تحسين.<br>\n';
            }

            if (descriptionLength === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>طول الوصف أقل من 160 حرف:</strong> هذا يجعل الوصف مناسبًا للعرض في نتائج البحث، مما يحفز النقرات.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>طول الوصف أكثر من 160 حرف:</strong> يؤدي ذلك إلى تقطيعه في نتائج البحث، مما يؤثر سلبًا على الظهور.<br>\n';
            }

            if (hasAltTags === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>الـ alt tag مضاف بشكل جيد للصور:</strong> رائع جدا حيث يساعد هذا محركات البحث على فهم محتوى الصور ويعزز الظهور أكثر في محركات البحث للصور في الكلمات المفتاحية المستهدفة.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم إضافة alt tag لبعض الصور الهامة:</strong> قد يؤثر ذلك سلباً على تحسين محركات البحث وتجربة المستخدم، وقد يؤدي لفقدان الكثير من الفرص للظهور في الكلمات المفتاحية المستهدفة في محرك بحث الصور ونتائج البحث بشكل عام.<br>\n';
            }

            if (sslIssues === 'no') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>لا يوجد مشكلات تتعلق بـ SSL :</strong> يدل ذلك على أن الموقع آمن، مما يعزز الثقة ايضا في الموقع لدى محركات البحث.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>وجود مشكلات تتعلق بـ SSL:</strong> قد يؤثر ذلك سلباً على أمان الموقع وتجربة المستخدم وظهوره في نتائج البحث، يحتاج هذا الأمر إلى معالجة.<br>\n';
            }

            if (noindexBrokenlinks === 'no') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عدم وجود مشاكل Noindex أو روابط مكسورة:</strong> يشير إلى جودة الموقع وسهولة الزحف.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>وجود مشاكل Noindex أو روابط مكسورة:</strong> من الضروري إصلاح هذه المشاكل لتحسين تجربة المستخدم وSEO.<br>\n';
            }

            if (goodSectionDescription === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود وصف جيد للأقسام الرئيسية:</strong> هذا رائع جدا حيث يسهل على الزوار فهم المحتوى ويوفر تجربة مستخدم أفضل، كما يساعد محركات البحث على فهم الأقسام والصفحات الرئيسية للموقع وتصنيفها بشكل جيد.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود وصف جيد للأقسام الرئيسية:</strong> تحتاج الأقسام في الموقع والصفحات الرئيسية إلى وصف جيد لكل قسم، يعزز من قدرة الموقع على تصدر نتائج البحث في الكلمات المفتاحية المستهدفة كما يصف الموقع بشكل جيد لمحركات البحث ويسهل من عملية تصنيفها.<br>\n';
            }

            if (goodProductDescriptions === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود وصف جيد للمنتجات:</strong> يساعد في جذب الزوار وزيادة فرص المبيعات، وتحسين الظهور في محركات البحث أكثر.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود وصف جيد للمنتجات:</strong> تحتاج المنتجات في الموقع إلى إضافة أوصاف مناسبة حيث سوف يعزز ذلك أكثر من تصنيف المنتجات وظهورها في ترتيب جيد في محركات البحث.<br>\n';
            }

            if (contentMarketing === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود مدونة للتسويق بالمحتوى:</strong> هذا رائع جدا حيث يتيح استهداف كلمات مفتاحية إضافية والتسويق بالمحتوى وتعزيز SEO.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود مدونة للموقع:</strong> قد يؤثر ذلك على فرص تعزيز المحتوى وجذب الزوار، من المهم جدا وجود مدونة للموقع لإضافة المزيد من المحتوى المتعلق بالموقع واستهداف كلمات مفتاحية أكثر.<br>\n';
            }

            if (articlesSEO === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تهيئة المقالات بشكل جيد لـ SEO:</strong> المقالات مهيئة بشكل جيد للـ SEO وهذا رائع حيث يساعد في تحسين الظهور في محركات البحث وزيادة الزوار.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم تهيئة المقالات بشكل جيد لـ SEO:</strong> المقالات غير مهيئة جيدا للـ SEO مما يجعلنا بحاجة إلى إضافة بعض التحسينات لزيادة معدل الظهور في نتائج البحث والزيارات لهذه المقالات.<br>\n';
            }

            if (pagesSEO === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تهيئة الصفحات بشكل جيد لـ SEO:</strong> هذا رائع جدا حيث يسهل على محركات البحث فهرسة المحتوى لتلك الصفحات وظهورها بشكل أفضل في نتائج محركات البحث.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم تهيئة الصفحات بشكل جيد لـ SEO:</strong> تحتاج صفحات الموقع إلى بعض تحسينات الـ SEO لتحسين ظهورها وترتيبها في نتائج محركات البحث.<br>\n';
            }

            if (contentQuantity === 'yes') {
                advantages.content += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود عدد جيد من المقالات:</strong> عدد المقالات في الموقع جيد وهذا يعزز فرص الظهور في عدد كلمات مفتاحية مناسب في محركات البحث والمزيد من الزوار.<br>\n';
            } else {
                disadvantages.content += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>وجود عدد قليل من المقالات:</strong> المحتوى قليل في الموقع، يحتاج الموقع إلى خطة محتوى جيدة لتعزيز ظهوره أكثر في محركات البحث، واستهداف عدد أكبر من الكلمات المفتاحية المناسبة.<br>\n';
            }

            if (linkStructureIssues === 'yes') {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>وجود مشكلة في بنية الروابط:</strong> قد يجعل من الصعب على محركات البحث فهرسة المحتوى، يحتاج الموقع إلى تعديل في بنية الروابط من أجل تحسين الـ SEO والظهور أكثر في محركات البحث في مراتب متقدمة.<br>\n';
            } else {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عدم وجود مشكلة في بنية الروابط:</strong> بنية الروابط في الموقع جيدة مما يسهل على محركات البحث فهرسة الموقع بشكل صحيح بدون مشاكل إن شاء الله تعالى.<br>\n';
            }

            if (navTags === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تم استخدام الوسم Nav في القوائم:</strong> هذا الوسم جيد في تعريف محركات البحث بالقوائم ويساعد على تحسين الظهور في محرك البحث واستهداف الكلمات المفتاحية بشكل أفضل.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم استخدام الوسم Nav في القوائم:</strong> هذا الوسم مهم في تعريف محركات البحث بالقوائم ويساعد على تحسين الظهور في محرك البحث واستهداف الكلمات المفتاحية بشكل أفضل لذلك من المهم استخدامه لتعريف قوائم الموقع المختلفة.<br>\n';
            }

            if (essentialPages === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود الصفحات الأساسية مثل "الخصوصية" و "شروط الاستخدام":</strong>وجود تلك الصفحات الرئيسية في الموقع جيد جدا حيث يعزز الثقة ويظهر احترافية الموقع ومصداقيته لدى محركات البحث.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>الصفحات الأساسية غير موجودة في الموقع:</strong> عدم وجود الصفحات الأساسية مثل "الخصوصية" أو "شروط الاستخدام" قد يؤثر على الثقة بالموقع لدى محركات البحث وبالتالي نتائج متأخرة في محرك البحث.<br>\n';
            }

            if (sitemap === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>خريطة الموقع موجودة وتعمل بشكل جيد:</strong> وجود خريطة الموقع وتقديمها لمحركات البحث يسهل على محركات البحث فهرسة المحتوى بشكل فعال.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>لا يوجد خريطة للموقع Sitemap:</strong> قد يؤثر ذلك بشكل كبير على فهرسة المحتوى، لذلك يجب إنشاء الخريطة وإعدادها بشكل جيد وتقديمها لمحركات البحث لتحسين جودة وسرعة الأرشفة للموقع.<br>\n';
            }

            if (googleConsole === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>الموقع مؤرشف ومضاف جيدا إلى Google Console:</strong> رائع جدا حيث يسمح ذلك بمراقبة الأداء وحل المشكلات المتعلقة بالـ SEO وتحسين الأرشفة والظهور في محركات البحث.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>الموقع غير مؤرشف وغير مضاف جيدا إلى Google Console:</strong> من المهم جدا إضافة الموقع بشكل جيد إلى Google Search Console لمراقبة أداء الموقع على محرك البحث وتحسينه وتحسين سرعة الأرشفة.<br>\n';
            }

            if (googleAnalytics === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تم ربط احصائيات جوجل أناليتكس بالموقع:</strong> هذا رائع جدا حيث يساعد في فهم سلوك الزوار وتحليل الأداء.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم ربط احصائيات جوجل أناليتكس بالموقع:</strong> قد يمنعك من اتخاذ قرارات مستندة إلى البيانات وتحليل أداء الموقع والزيارات، لذلك من معالجة ذلك.<br>\n';
            }

            if (openGraph === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>كود الـ OpenGraph مضاف بشكل جيد:</strong> هذا رائع جدا ويساعد في تحسين مشاركة المحتوى على وسائل التواصل الاجتماعي وإظهار الصفحات بشكل جذاب.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>كود الـ OpenGraph غير مضاف للموقع:</strong> قد يؤثر سلبًا على كيفية ظهور الصفحات عند المشاركة.<br>\n';
            }

            if (favicon === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود Favicon:</strong> يساعد في تعزيز الهوية البصرية للموقع ويسهل التعرف عليه من قبل الزوار.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود Favicon:</strong> نحتاج إلى إضافة Favicon للموقع حيث يعزز ذلك من الهوية البصرية له وتجربة المستخدم.<br>\n';
            }

            if (keywordResearch === 'yes') {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>الحاجة إلى بحث كلمات مفتاحية:</strong> يحتاج الموقع إلى "بحث كلمات مفتاحية متقدم" للوصول إلى عدد أكبر من الكلمات المفتاحية المناسبة للمجال واستهدافها بشكل صحيح، للحصول على عدد زيارات أكبر من محركات البحث.<br>\n';
            } else {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>عدم الحاجة إلى بحث كلمات مفتاحية:</strong> الموقع يستهدف الكلمات المفتاحية بشكل جيد في المحتوى، ولديه استراتيجية كلمات مفتاحية محددة يعمل عليها فعليا.<br>\n';
            }

            if (targetedKeywords === 'yes') {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>يستهدف الموقع شبكة كلمات مفتاحية ذات صلة بالكلمة الرئيسية:</strong>هذا رائع جدا حيث يساعد في تحسين محركات البحث وزيادة الفرص في الظهور أكثر في الكلمات المفتاحية الأساسية المستهدفة.<br>\n';
            } else {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم استهداف شبكة كلمات مفتاحية ذات صلة بالكلمة الرئيسية:</strong>يؤدي ذلك إلى فقدان الفرص في الظهور بشكل أفضل في محرك البحث للكلمات المفتاحية الرئيسية المستهدفة.<br>\n';
            }

            if (domainKeyword === 'yes') {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>احتواء الدومين على الكلمة المفتاحية الرئيسية:</strong> هذا رائع جدا ويساعد في تحسين محركات البحث وزيادة ظهور الموقع في الكلمة الرئيسية المستهدفة.<br>\n';
            } else {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم احتواء الدومين على الكلمة المفتاحية الرئيسية:</strong> يقلل ذلك من فرص تصدر نتائج البحث في الكلمة المفتاحية المستهدفة.<br>\n';
            }

            if (coreWebVitals === 'yes') {
                advantages.performance += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تحقيق معايير Core Web Vitals:</strong> هذا يعزز تجربة المستخدم ويحسن ترتيب الموقع في محركات البحث.<br>\n';
            } else {
                disadvantages.performance += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم تحقيق معايير Core Web Vitals:</strong> يحتاج الموقع إلى تحسين الأداء لتلبية معايير جوجل لتجربة الصفحة.<br>\n';
            }

            if (robotsTxt === 'yes') {
                advantages.technical += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>وجود ملف robots.txt صحيح:</strong> يساعد في التحكم بزحف محركات البحث وتحسين الفهرسة.<br>\n';
            } else {
                disadvantages.technical += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم وجود ملف robots.txt صحيح:</strong> قد يؤدي إلى فهرسة غير مرغوبة أو مشاكل في الزحف.<br>\n';
            }

            if (contentFreshness === 'yes') {
                advantages.marketing += '<i class="fas fa-check-circle icon" style="color: #27ae60;"></i> <strong>تحديث المحتوى بشكل دوري:</strong> يعزز من جاذبية الموقع لمحركات البحث والزوار.<br>\n';
            } else {
                disadvantages.marketing += '<i class="fas fa-times-circle icon" style="color: #e74c3c;"></i> <strong>عدم تحديث المحتوى بشكل دوري:</strong> قد يؤثر على ترتيب الموقع لأن محركات البحث تفضل المحتوى الجديد.<br>\n';
            }

            // حساب نسبة التقييم
            const totalCriteria = 34; // عدد المعايير الكلي
            let positiveCount = 0;
            for (let key in advantages) {
                positiveCount += (advantages[key].split('<br>').length - 1);
            }
            const score = Math.round((positiveCount / totalCriteria) * 100);

            // بناء التقرير
            const advantagesList = {
                basic: advantages.basic.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>',
                performance: advantages.performance.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>',
                authority: advantages.authority.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>',
                content: advantages.content.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>',
                technical: advantages.technical.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>',
                marketing: advantages.marketing.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد مزايا.</li>'
            };
            const disadvantagesList = {
                basic: disadvantages.basic.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>',
                performance: disadvantages.performance.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>',
                authority: disadvantages.authority.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>',
                content: disadvantages.content.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>',
                technical: disadvantages.technical.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>',
                marketing: disadvantages.marketing.split('\n').filter(item => item).map(item => `<li>${item}</li>`).join('') || '<li>لا توجد عيوب.</li>'
            };

            const report = `
                <h1><i class="fas fa-file-alt" style="color: #3498db; margin-left: 10px;"></i> تقرير تحليل SEO للموقع: ${domain}</h1>
                <p><strong>السلام عليكم</strong></p>
                <p>نتمنى أن تكون بصحة جيدة أخي الكريم وبأفضل حال يا رب</p>
                <p>بالنسبة للموقع الخاص بكم: <a href="${domain}" style="color: #3498db;">${domain}</a></p>
                <p><strong>فقد لاحظنا ما يلي:</strong></p>
                <hr style="border: 2px solid #3498db;" />

                <div class="card">
                    <h4><i class="fas fa-info-circle" style="color: #3498db; margin-left: 10px;"></i> تفاصيل الموقع</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-list" style="margin-left: 10px;"></i> تفاصيل الموقع</th>
                            <th><i class="fas fa-check-square" style="margin-left: 10px;"></i> القيمة</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-calendar-alt icon"></i> عمر الدومين</td>
                            <td>${siteAge} سنة تقريبا</td>
                        </tr>
                        <tr>
                            <td><i class="fab fa-wordpress icon"></i> هل يستخدم ووردبريس؟</td>
                            <td>${isWordPress === 'yes' ? 'نعم' : 'لا'}</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-desktop icon"></i> سرعة الموقع على الديسكتوب</td>
                            <td>${desktopSpeed} ثواني</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-mobile-alt icon"></i> سرعة الموقع على الموبايل</td>
                            <td>${mobileSpeed} ثواني</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-chart-line icon"></i> ترتيب Moz Domain Authority</td>
                            <td>${mozDomainAuthority}</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-link icon"></i> ترتيب Ahrefs Domain Rank</td>
                            <td>${ahrefsDomainRank}</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-signal icon"></i> ترتيب Semrush AS</td>
                            <td>${semrushAS}</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shield-alt icon"></i> ترتيب Majestic Trust Flow</td>
                            <td>${majesticTrustFlow}</td>
                        </tr>
                    </table>
                </div>

                <div class="card">
                    <h3><i class="fas fa-thumbs-up" style="color: #27ae60; margin-left: 10px;"></i> <span style="color: #27ae60;">أولاً: مميزات الموقع</span></h3>
                    <h4>المعلومات الأساسية</h4>
                    <ul>${advantagesList.basic}</ul>
                    <h4>الأداء والسرعة</h4>
                    <ul>${advantagesList.performance}</ul>
                    <h4>السلطة والترتيب</h4>
                    <ul>${advantagesList.authority}</ul>
                    <h4>المحتوى والهيكلة</h4>
                    <ul>${advantagesList.content}</ul>
                    <h4>التكامل التقني</h4>
                    <ul>${advantagesList.technical}</ul>
                    <h4>التسويق والكلمات المفتاحية</h4>
                    <ul>${advantagesList.marketing}</ul>
                </div>

                <div class="card">
                    <h3><i class="fas fa-exclamation-triangle" style="color: #e74c3c; margin-left: 10px;"></i> <span style="color: #e74c3c;">ثانياً: العيوب المأخوذة على الموقع</span></h3>
                    <h4>المعلومات الأساسية</h4>
                    <ul>${disadvantagesList.basic}</ul>
                    <h4>الأداء والسرعة</h4>
                    <ul>${disadvantagesList.performance}</ul>
                    <h4>السلطة والترتيب</h4>
                    <ul>${disadvantagesList.authority}</ul>
                    <h4>المحتوى والهيكلة</h4>
                    <ul>${disadvantagesList.content}</ul>
                    <h4>التكامل التقني</h4>
                    <ul>${disadvantagesList.technical}</ul>
                    <h4>التسويق والكلمات المفتاحية</h4>
                    <ul>${disadvantagesList.marketing}</ul>
                </div>

                <div class="card">
                    <h3 style="color: #27ae60;">
                        <i class="fas fa-tools" style="color: #27ae60; margin-left: 10px;"></i> ما هي التحسينات التي يمكننا عملها بالنسبة للسيو في الموقع؟
                    </h3>
                    <p style="font-style: italic;">إن شاء الله تعالى</p>
                    <ol>
                        <li><i class="fas fa-wrench icon"></i> إصلاح مشاكل السيو الداخلية للموقع الخاصة بـ title و description و وصف الصفحات والمقالات والمنتجات، وتوزيع الكلمات المفتاحية بشكل صحيح على المحتوى والعناوين.</li>
                        <li><i class="fas fa-sitemap icon"></i> إصلاح مشاكل الهيكلة الداخلية والوسوم H1 و H2 و Alt tag وبنية الروابط.</li>
                        <li><i class="fas fa-file icon"></i> إنشاء الصفحات الرئيسية المفقودة.</li>
                        <li><i class="fas fa-search-plus icon"></i> العمل على تحسين الأرشفة ومعالجة مشكلاتها لزيادة عدد نتائج الموقع في محرك البحث.</li>
                        <li><i class="fas fa-arrow-up icon"></i> العمل على رفع الدومين رانك في ahrefs إلى 30+، سيزيد ذلك كثيرا من قوة الدومين إن شاء الله في محركات البحث، ويتبعه بذلك زيادة وارتفاع في الترتيب للموقع في نتائج البحث إن شاء الله تعالى.</li>
                        <li><i class="fas fa-arrow-up icon"></i> زيادة الدومين أوثوريتي للموقع 30+ في Moz لتقوية الدومين وزيادة قدرته على المنافسة في محركات البحث.</li>
                        <li><i class="fas fa-link icon"></i> ندلكم على دومينات ذات أوثوريتي وعدد باك لينك مرتفع، وتحويلها للموقع، تسمى باستراتيجية التحويل الذهبية، وهي من اسرار السيو، ويمكن أن تزيد من ثقة محرك البحث جوجل في الموقع بشكل عام إن شاء الله تعالى.</li>
                        <li><i class="fas fa-search icon"></i> عمل بحث وتحليل الكلمات المفتاحية في هذا التخصص للوصول لأفضل كلمات مفتاحية لاستهدافها.</li>
                        <li><i class="fas fa-file-alt icon"></i> إنشاء 12 مقال مميزة جديدة <strong>باللغة العربية</strong> كل مقال أكثر من 1500 كلمة، متوافقين مع معايير الـ SEO، باستهداف 12 كلمة مفتاحية جديدة بعد دراستهم وعمل تحليل وتقرير كامل حول الكلمات المفتاحية.</li>
                        <li><i class="fas fa-backward icon"></i> انشاء عدد باك لينك <strong>اجنبي</strong> جيد إضافي للموقع، يستهدف وجود رابط الموقع في عدة مواقع اجنبية وبناء <strong>لينك بروفايل</strong> جيد للموقع في عدة مواقع مهمة لزيادة ثقة محركات البحث في الموقع، على أن تكون الباك لينك من نوع DoFollow و أوثوريتي مرتفع لتكون مفيدة لموقعك ولتحسين ترتيبه.</li>
                        <li><i class="fas fa-file-export icon"></i> تقديم ملف توصيات لأفضل مقدمي خدمات الجيست بوست، وكيفية شراؤه للموقع والنصائح الخاصة به، <strong>كما يمكننا إدارة هذه العملية في الشهور التالية</strong> إن شاء الله، وذلك لتحسين ظهور الموقع أكثر في محركات البحث إن شاء الله تعالى.</li>
                    </ol>
                    <p style="font-weight: 700;">إن أصبنا وأحسنا فمن الله تعالى، وإن أخطأنا أو قصرنا فمن أنفسنا ومن الشيطان، ونسال الله العظيم لنا ولكم التوفيق والسداد لكل خير ..</p>
                </div>

                <div class="card">
                    <h3 style="color: #3498db;">
                        <i class="fas fa-chart-bar" style="color: #3498db; margin-left: 10px;"></i> تقييم الأداء العام
                    </h3>
                    <p>نسبة الأداء الكلية للموقع: <strong>${score}%</strong></p>
                    <div class="progress-bar">
                        <div class="progress" style="width: ${score}%;"></div>
                    </div>
                </div>
            `;

            // عرض التقرير
            const reportDiv = document.getElementById('seo-report');
            reportDiv.innerHTML = report;
            reportDiv.classList.remove('d-none');

            // نسخ التقرير كـ HTML
            document.getElementById('copy-html').addEventListener('click', function() {
                const reportHTML = reportDiv.innerHTML;
                navigator.clipboard.writeText(reportHTML).then(() => {
                    alert('تم نسخ التقرير ككود HTML!');
                }).catch(err => {
                    console.error('فشل النسخ: ', err);
                    alert('فشل نسخ التقرير، حاول مرة أخرى.');
                });
            });

            // نسخ التقرير بشكل عام (نص عادي)
            document.getElementById('copy-general').addEventListener('click', function() {
                const reportText = reportDiv.innerText;
                navigator.clipboard.writeText(reportText).then(() => {
                    alert('تم نسخ التقرير بشكل عام!');
                }).catch(err => {
                    console.error('فشل النسخ: ', err);
                    alert('فشل نسخ التقرير، حاول مرة أخرى.');
                });
            });

            // تصدير كملف Word
            document.getElementById('export-word').addEventListener('click', function() {
                const reportHTML = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>تقرير SEO لـ ${domain}</title>
                        <style>
                            body { font-family: Arial, sans-serif; color: #2c3e50; line-height: 1.8; padding: 20px; }
                            h1 { font-size: 36pt; color: #2c3e50; text-align: center; margin-bottom: 20pt; }
                            h3 { font-size: 24pt; color: #3498db; margin-top: 15pt; }
                            h4 { font-size: 18pt; color: #2980b9; border-bottom: 2px solid #3498db; padding-bottom: 5pt; margin-top: 10pt; }
                            p, li { font-size: 14pt; color: #34495e; margin-bottom: 10pt; }
                            table { width: 100%; border-collapse: collapse; margin: 20pt 0; }
                            th, td { border: 1px solid #e0e6ed; padding: 10pt; text-align: right; }
                            th { background: #3498db; color: white; }
                            hr { border: 2px solid #3498db; margin: 20pt 0; }
                            .icon { margin-left: 10pt; font-size: 14pt; vertical-align: middle; }
                            .progress-bar { width: 100%; background: #e0e6ed; height: 20pt; border-radius: 10pt; overflow: hidden; margin: 20pt 0; }
                            .progress { height: 100%; background: #27ae60; width: ${score}%; }
                        </style>
                    </head>
                    <body>
                        ${report}
                    </body>
                    </html>
                `;
                const blob = new Blob(['\ufeff', reportHTML], { type: 'application/vnd.ms-word' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `SEO_Report_${domain}.doc`;
                link.click();
                URL.revokeObjectURL(link.href);
                alert('تم تصدير التقرير كملف Word!');
            });

            // تصدير إلى Google Docs
            document.getElementById('export-gdocs').addEventListener('click', function() {
                const reportHTML = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>تقرير SEO لـ ${domain}</title>
                        <style>
                            body { font-family: Arial, sans-serif; color: #2c3e50; line-height: 1.8; padding: 20px; }
                            h1 { font-size: 36pt; color: #2c3e50; text-align: center; margin-bottom: 20pt; }
                            h3 { font-size: 24pt; color: #3498db; margin-top: 15pt; }
                            h4 { font-size: 18pt; color: #2980b9; border-bottom: 2px solid #3498db; padding-bottom: 5pt; margin-top: 10pt; }
                            p, li { font-size: 14pt; color: #34495e; margin-bottom: 10pt; }
                            table { width: 100%; border-collapse: collapse; margin: 20pt 0; }
                            th, td { border: 1px solid #e0e6ed; padding: 10pt; text-align: right; }
                            th { background: #3498db; color: white; }
                            hr { border: 2px solid #3498db; margin: 20pt 0; }
                            .icon { margin-left: 10pt; font-size: 14pt; vertical-align: middle; }
                            .progress-bar { width: 100%; background: #e0e6ed; height: 20pt; border-radius: 10pt; overflow: hidden; margin: 20pt 0; }
                            .progress { height: 100%; background: #27ae60; width: ${score}%; }
                        </style>
                    </head>
                    <body>
                        ${report}
                    </body>
                    </html>
                `;
                const encodedReport = encodeURIComponent(reportHTML);
                const url = `https://docs.google.com/document/create?usp=sharing&html=${encodedReport}`;
                window.open(url, '_blank');
                alert('تم تصدير التقرير إلى Google Docs!');
            });
        });
    </script>
</body>
</html>