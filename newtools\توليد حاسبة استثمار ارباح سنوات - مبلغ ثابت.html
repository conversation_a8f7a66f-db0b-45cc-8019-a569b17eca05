<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة الأرباح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-card {
            margin-top: 20px;
        }
    </style>
</head>

<body>
    <div class="container py-5">
        <h1 class="text-center mb-4">حاسبة الأرباح</h1>
        <div class="card shadow-sm p-4">
            <form id="profitForm">
                <div class="mb-3">
                    <label for="initialInvestment" class="form-label">المبلغ الأساسي للاستثمار (جنيه)</label>
                    <input type="number" class="form-control" id="initialInvestment" placeholder="أدخل المبلغ الأساسي" required>
                </div>
                <div class="mb-3">
                    <label for="annualReturn" class="form-label">نسبة العائد السنوي (%)</label>
                    <input type="number" class="form-control" id="annualReturn" placeholder="أدخل نسبة العائد السنوي" required>
                </div>
                <div class="mb-3">
                    <label for="investmentYears" class="form-label">عدد سنوات الاستثمار</label>
                    <input type="number" class="form-control" id="investmentYears" placeholder="أدخل عدد السنوات" required>
                </div>
                <button type="button" class="btn btn-primary w-100" onclick="calculateProfit()">احسب</button>
            </form>
        </div>

        <div class="card shadow-sm p-4 result-card" id="resultCard" style="display: none;">
            <h4 class="text-center mb-3">نتائج الاستثمار</h4>
            <ul class="list-group mb-3">
                <li class="list-group-item">إجمالي الاستثمار: <span id="totalInvestment"></span> جنيه</li>
                <li class="list-group-item">إجمالي الأرباح: <span id="totalProfit"></span> جنيه</li>
                <li class="list-group-item">إجمالي المبلغ بعد الاستثمار: <span id="finalAmount"></span> جنيه</li>
            </ul>

            <h5 class="text-center mb-3">تفاصيل الأرباح السنوية</h5>
            <div class="table-responsive mb-4">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>السنة</th>
                            <th>المبلغ الإجمالي بنهاية السنة (جنيه)</th>
                            <th>الأرباح السنوية (جنيه)</th>
                            <th>متوسط الربح السنوي (جنيه)</th>
                            <th>متوسط نسبة الربح التراكمية (%)</th>
                        </tr>
                    </thead>
                    <tbody id="yearlyDetails"></tbody>
                </table>
            </div>

            <h5 class="text-center mb-3">الربح الشهري المتوقع</h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>السنة</th>
                            <th>المبلغ الإجمالي بنهاية السنة (جنيه)</th>
                            <th>الأرباح الشهرية (جنيه)</th>
                        </tr>
                    </thead>
                    <tbody id="monthlyDetails"></tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function calculateProfit() {
            const initialInvestment = parseFloat(document.getElementById('initialInvestment').value);
            const annualReturn = parseFloat(document.getElementById('annualReturn').value) / 100;
            const investmentYears = parseInt(document.getElementById('investmentYears').value);

            if (isNaN(initialInvestment) || isNaN(annualReturn) || isNaN(investmentYears) || initialInvestment <= 0 || annualReturn <= 0 || investmentYears <= 0) {
                alert("يرجى إدخال قيم صحيحة");
                return;
            }

            let currentBalance = initialInvestment;
            let yearlyDetails = "";
            let monthlyDetails = "";
            let totalProfit = 0;

            for (let year = 1; year <= investmentYears; year++) {
                const previousBalance = currentBalance;
                currentBalance += currentBalance * annualReturn;
                const annualProfit = currentBalance - previousBalance;
                const monthlyProfit = annualProfit / 12;

                totalProfit += annualProfit;

                // جدول الأرباح السنوية
                yearlyDetails += `
                    <tr>
                        <td>${year}</td>
                        <td>${currentBalance.toFixed(2)}</td>
                        <td>${annualProfit.toFixed(2)}</td>
                        <td>${(totalProfit / year).toFixed(2)}</td>
                        <td>${((totalProfit / (initialInvestment * year)) * 100).toFixed(2)}%</td>
                    </tr>
                `;

                // جدول الأرباح الشهرية
                monthlyDetails += `
                    <tr>
                        <td>${year}</td>
                        <td>${currentBalance.toFixed(2)}</td>
                        <td>${monthlyProfit.toFixed(2)}</td>
                    </tr>
                `;
            }

            document.getElementById('totalInvestment').innerText = initialInvestment.toFixed(2);
            document.getElementById('totalProfit').innerText = totalProfit.toFixed(2);
            document.getElementById('finalAmount').innerText = currentBalance.toFixed(2);

            document.getElementById('yearlyDetails').innerHTML = yearlyDetails;
            document.getElementById('monthlyDetails').innerHTML = monthlyDetails;

            document.getElementById('resultCard').style.display = 'block';
        }
    </script>
</body>

</html>
