<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مؤقت عكسي - Countdown Timer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .timer-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .timer-display {
            background-color: var(--card-bg);
            padding: 40px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
        }
        .time {
            font-size: 4rem;
            font-family: monospace;
            color: var(--primary-color);
            margin: 20px 0;
            letter-spacing: 2px;
        }
        .timer-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .input-group {
            text-align: center;
        }
        .input-group input {
            width: 80px;
            padding: 10px;
            text-align: center;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 1.2rem;
        }
        .input-label {
            display: block;
            margin-top: 5px;
            color: var(--text-color);
        }
        .timer-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        .timer-btn {
            padding: 10px 20px;
            font-size: 1.2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.2s;
            min-width: 120px;
        }
        .start-btn {
            background-color: #28a745;
            color: white;
        }
        .pause-btn {
            background-color: var(--primary-color);
            color: white;
        }
        .reset-btn {
            background-color: #dc3545;
            color: white;
        }
        .presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .preset-btn {
            padding: 10px;
            background-color: var(--bg-color);
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .preset-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }
        .progress-bar {
            height: 10px;
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
            margin-top: 20px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: var(--primary-color);
            width: 100%;
            transition: width 1s linear;
        }
    </style>
</head>
<body>
    <header>
        <h1>مؤقت عكسي <span>Countdown Timer</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="timer-container">
            <div class="timer-display">
                <div class="time" id="timerDisplay">00:00:00</div>
                <div class="progress-bar">
                    <div class="progress" id="progressBar"></div>
                </div>
            </div>

            <div class="timer-inputs">
                <div class="input-group">
                    <input type="number" id="hoursInput" min="0" max="99" value="0">
                    <label class="input-label ar">ساعات</label>
                    <label class="input-label en">Hours</label>
                </div>
                <div class="input-group">
                    <input type="number" id="minutesInput" min="0" max="59" value="0">
                    <label class="input-label ar">دقائق</label>
                    <label class="input-label en">Minutes</label>
                </div>
                <div class="input-group">
                    <input type="number" id="secondsInput" min="0" max="59" value="0">
                    <label class="input-label ar">ثواني</label>
                    <label class="input-label en">Seconds</label>
                </div>
            </div>

            <div class="presets">
                <button class="preset-btn" data-time="300">5 دقائق / min</button>
                <button class="preset-btn" data-time="600">10 دقائق / min</button>
                <button class="preset-btn" data-time="900">15 دقائق / min</button>
                <button class="preset-btn" data-time="1800">30 دقائق / min</button>
                <button class="preset-btn" data-time="3600">60 دقائق / min</button>
            </div>

            <div class="timer-controls">
                <button id="startBtn" class="timer-btn start-btn">
                    <span class="ar">بدء</span>
                    <span class="en">Start</span>
                </button>
                <button id="pauseBtn" class="timer-btn pause-btn" disabled>
                    <span class="ar">إيقاف مؤقت</span>
                    <span class="en">Pause</span>
                </button>
                <button id="resetBtn" class="timer-btn reset-btn" disabled>
                    <span class="ar">إعادة تعيين</span>
                    <span class="en">Reset</span>
                </button>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const timerDisplay = document.getElementById('timerDisplay');
            const progressBar = document.getElementById('progressBar');
            const hoursInput = document.getElementById('hoursInput');
            const minutesInput = document.getElementById('minutesInput');
            const secondsInput = document.getElementById('secondsInput');
            const startBtn = document.getElementById('startBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resetBtn = document.getElementById('resetBtn');
            const presetBtns = document.querySelectorAll('.preset-btn');

            let timeLeft = 0;
            let totalTime = 0;
            let timerId = null;
            let isPaused = false;

            // Create audio context for alert sound
            let audioContext = null;
            let oscillator = null;

            function playAlertSound() {
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.type = 'sine';
                oscillator.frequency.value = 440;
                gainNode.gain.value = 0.5;

                oscillator.start();
                setTimeout(() => {
                    oscillator.stop();
                    oscillator = null;
                }, 1000);
            }

            function formatTime(seconds) {
                const h = Math.floor(seconds / 3600);
                const m = Math.floor((seconds % 3600) / 60);
                const s = seconds % 60;
                return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
            }

            function updateTimer() {
                if (timeLeft > 0) {
                    timeLeft--;
                    timerDisplay.textContent = formatTime(timeLeft);
                    progressBar.style.width = `${(timeLeft / totalTime) * 100}%`;
                } else {
                    clearInterval(timerId);
                    timerId = null;
                    playAlertSound();
                    resetTimer();
                }
            }

            function startTimer() {
                if (!timerId && !isPaused) {
                    const hours = parseInt(hoursInput.value) || 0;
                    const minutes = parseInt(minutesInput.value) || 0;
                    const seconds = parseInt(secondsInput.value) || 0;
                    timeLeft = hours * 3600 + minutes * 60 + seconds;
                    totalTime = timeLeft;
                }

                if (timeLeft > 0) {
                    timerId = setInterval(updateTimer, 1000);
                    startBtn.disabled = true;
                    pauseBtn.disabled = false;
                    resetBtn.disabled = false;
                    isPaused = false;
                }
            }

            function pauseTimer() {
                clearInterval(timerId);
                timerId = null;
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                isPaused = true;
            }

            function resetTimer() {
                clearInterval(timerId);
                timerId = null;
                timeLeft = 0;
                totalTime = 0;
                timerDisplay.textContent = '00:00:00';
                progressBar.style.width = '100%';
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                resetBtn.disabled = true;
                isPaused = false;
            }

            // Event listeners
            startBtn.addEventListener('click', startTimer);
            pauseBtn.addEventListener('click', pauseTimer);
            resetBtn.addEventListener('click', resetTimer);

            // Preset buttons
            presetBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const seconds = parseInt(btn.dataset.time);
                    hoursInput.value = Math.floor(seconds / 3600);
                    minutesInput.value = Math.floor((seconds % 3600) / 60);
                    secondsInput.value = seconds % 60;
                });
            });

            // Load saved values
            hoursInput.value = localStorage.getItem('timer_hours') || 0;
            minutesInput.value = localStorage.getItem('timer_minutes') || 0;
            secondsInput.value = localStorage.getItem('timer_seconds') || 0;

            // Save values on change
            [hoursInput, minutesInput, secondsInput].forEach(input => {
                input.addEventListener('change', () => {
                    localStorage.setItem('timer_hours', hoursInput.value);
                    localStorage.setItem('timer_minutes', minutesInput.value);
                    localStorage.setItem('timer_seconds', secondsInput.value);
                });
            });
        });
    </script>
</body>
</html>