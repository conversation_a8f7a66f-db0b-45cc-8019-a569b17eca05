:root {
    --primary-color: #4a6baf;
    --primary-color-rgb: 74, 107, 175;
    --secondary-color: #6c757d;
    --text-color: #333;
    --bg-color: #f8f9fa;
    --card-bg: #fff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --header-height: 80px;
    --footer-bg: #2f3640;
    --footer-text: #f5f6fa;
    --tab-height: 60px;
    --tab-transition: all 0.3s ease;
}

[dir="rtl"] {
    --text-direction: right;
}

[dir="ltr"] {
    --text-direction: left;
}

/* Dark Mode Variables */
.dark-mode {
    --primary-color: #5d8aff;
    --secondary-color: #adb5bd;
    --text-color: #f8f9fa;
    --bg-color: #212529;
    --card-bg: #2b3035;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    transition: background-color 0.3s, color 0.3s;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    text-align: var(--text-direction);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: calc(var(--header-height) + 20px);
}

/* Enhanced Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.dark-mode .main-header {
    background-color: rgba(33, 37, 41, 0.95);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}

.header-logo i {
    font-size: 2.2rem;
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header-logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), #7a9ee0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, #7a9ee0 100%);
    color: white;
    padding: 6rem 2rem 4rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="20" height="20" fill="none"/><circle cx="3" cy="3" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.search-box:hover,
.search-box:focus-within {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.search-box i {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
}

.search-box input {
    flex: 1;
    background: none;
    border: none;
    color: white;
    font-size: 1.1rem;
    padding: 0.5rem;
    width: 100%;
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-box input:focus {
    outline: none;
}

.search-results {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-height: 400px;
    overflow-y: auto;
    display: none;
    position: fixed;
    width: 100%;
    max-width: 600px;
    left: 50%;
    top: calc(var(--header-height) + 180px);
    transform: translateX(-50%);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    z-index: 2000;
}

.search-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: 1999;
}

.search-backdrop.active {
    display: block;
}

.dark-mode .search-results {
    background: var(--card-bg);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

.search-result {
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
@media (max-width: 768px) {
    .search-results {
        width: 90%;
        top: calc(var(--header-height) + 220px);
    }
}

    background: var(--card-bg);
}

.search-result:hover {
    background: var(--bg-color);
}

.dark-mode .search-result {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-results.active {
    display: block;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

h1 {
    color: var(--primary-color);
    font-size: 2rem;
}

h1 span {
    display: block;
    font-size: 1.2rem;
    color: var(--secondary-color);
}

/* Tools Navigation */
.tools-tabs {
    position: sticky;
    top: var(--header-height);
    background: var(--card-bg);
    box-shadow: var(--shadow);
    z-index: 90;
    margin: 0 -20px 2rem;
    padding: 0.5rem 1rem;
}

.tabs-container {
    display: flex;
    max-width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    gap: 0.5rem;
    position: relative;
    margin: 0 2rem;
    padding: 0.5rem 0;
}

.tabs-container::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-color);
    font-weight: 500;
    white-space: nowrap;
    cursor: pointer;
    transition: var(--tab-transition);
    border-radius: var(--border-radius);
    position: relative;
}

.tab-btn i {
    font-size: 1.2rem;
    transition: var(--tab-transition);
}

.tab-btn:hover {
    background: rgba(var(--primary-color-rgb), 0.1);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.tab-btn.active i {
    transform: scale(1.1);
}

.tabs-arrows {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.5rem;
}

.tab-arrow {
    pointer-events: auto;
    border: none;
    background: var(--card-bg);
    color: var(--text-color);
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0.8;
    transition: var(--tab-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
}

.tab-arrow:hover {
    opacity: 1;
    transform: scale(1.1);
}

[dir="rtl"] .tab-arrow i.fa-chevron-left {
    transform: rotate(180deg);
}

[dir="rtl"] .tab-arrow i.fa-chevron-right {
    transform: rotate(180deg);
}

.tools-content {
    position: relative;
}

.tool-category {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tool-category.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tool-card {
    background-color: var(--card-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: transform 0.2s;
}

.tool-card:hover {
    transform: translateY(-5px);
}

.tool-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.tool-card h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.tool-card p {
    margin-bottom: 5px;
    color: var(--text-color);
}

.tool-card .en {
    display: none;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

[dir="ltr"] .tool-card .ar,
[dir="rtl"] .tool-card .en {
    display: none;
}

[dir="ltr"] .tool-card .en,
[dir="rtl"] .tool-card .ar {
    display: block;
}

/* Theme Toggle Styles */
.theme-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Language Toggle Button */
.lang-toggle button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
}

/* Enhanced Footer Styles */
.site-footer {
    background: linear-gradient(135deg, var(--footer-bg) 0%, #1a1f24 100%);
    color: var(--footer-text);
    padding: 5rem 2rem 2rem;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.footer-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><rect width="60" height="60" fill="none"/><path d="M0 0h60v60H0z" fill="none"/><circle cx="2" cy="2" r="2" fill="rgba(255,255,255,0.025)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr repeat(3, 1fr);
    gap: 4rem;
    position: relative;
}

.footer-section {
    position: relative;
}

.footer-section.primary {
    grid-column: 1;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo i {
    font-size: 2rem;
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.footer-logo h3 {
    font-size: 1.8rem;
    margin: 0;
}

.footer-logo h3::after {
    display: none;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    padding-left: 1.2rem;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.footer-links a:hover::before {
    opacity: 1;
}

[dir="rtl"] .footer-links a {
    padding-left: 0;
    padding-right: 1.2rem;
}

[dir="rtl"] .footer-links a::before {
    left: auto;
    right: 0;
}

[dir="rtl"] .footer-links a:hover {
    transform: translateX(-5px);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-hover {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-hover::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 0;
}

.social-hover:hover {
    transform: translateY(-5px);
    color: white;
}

.social-hover:hover::before {
    top: 0;
}

.social-hover i {
    position: relative;
    z-index: 1;
}

.newsletter .subscribe-form {
    display: flex;
    gap: 0.5rem;
}

.subscribe-form input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: white;
    transition: all 0.3s ease;
}

.subscribe-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.1);
}

.subscribe-form button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.subscribe-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.footer-bottom {
    text-align: center;
    padding-top: 3rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.footer-meta {
    display: flex;
    gap: 1.5rem;
}

.footer-meta a {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-meta a:hover {
    color: var(--primary-color);
}

.footer-meta .separator {
    color: rgba(255, 255, 255, 0.3);
}

/* Responsive Footer */
@media (max-width: 1200px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 3rem;
    }

    .footer-section.primary {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .site-footer {
        padding: 3rem 1.5rem 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }

    .footer-info {
        flex-direction: column;
        text-align: center;
    }

    .footer-meta {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .footer-section h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links a {
        padding-left: 0;
    }

    .footer-links a::before {
        display: none;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
        padding-top: calc(var(--header-height) + 10px);
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }

    .header-logo span {
        display: none;
    }

    .theme-toggle i {
        display: none;
    }
}

/* Tool Page Specific Styles */
.tool-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.input-group, .output-group {
    margin-bottom: 20px;
}

.input-group label, .output-group label {
    display: block;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: bold;
}

.input-group textarea, .output-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--secondary-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-size: 1rem;
    resize: vertical;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.button-group button, .output-group button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.button-group button:hover, .output-group button:hover {
    background-color: var(--secondary-color);
}

/* Image Tool Styles */
.image-preview {
    max-width: 100%;
    margin: 20px 0;
    border-radius: var(--border-radius);
}

.image-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.image-controls input[type="number"],
.image-controls input[type="range"] {
    width: 100%;
    padding: 5px;
    border: 1px solid var(--secondary-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Calculator Styles */
.calculator-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 400px;
    margin: 20px auto;
}

.calculator-display {
    grid-column: 1 / -1;
    background-color: var(--card-bg);
    padding: 20px;
    text-align: right;
    font-size: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.calculator-grid button {
    padding: 15px;
    font-size: 1.2rem;
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.calculator-grid button:hover {
    background-color: var(--secondary-color);
}

/* Timer Styles */
.timer-display {
    font-size: 3rem;
    text-align: center;
    margin: 20px 0;
    font-family: monospace;
    color: var(--primary-color);
}

.timer-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

/* Currency Converter Styles */
.currency-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.currency-grid select {
    padding: 10px;
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--secondary-color);
}

/* Language-specific Display */
[dir="ltr"] .ar,
[dir="rtl"] .en {
    display: none;
}

[dir="ltr"] .en,
[dir="rtl"] .ar {
    display: block;
}

/* Back Button */
footer a {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-block;
    margin-top: 20px;
    font-weight: bold;
}

footer a:hover {
    color: var(--secondary-color);
}