:root {
    /* Modern & Optimistic Color Palette */
    --primary-color: #2563eb;
    --primary-color-rgb: 37, 99, 235;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

    --secondary-color: #059669;
    --secondary-light: #10b981;
    --secondary-dark: #047857;

    --accent-color: #dc2626;
    --accent-light: #ef4444;
    --accent-dark: #b91c1c;

    --success-color: #16a34a;
    --warning-color: #ca8a04;
    --danger-color: #dc2626;
    --info-color: #0891b2;

    /* Bright & Optimistic Colors */
    --orange-bright: #ea580c;
    --pink-bright: #ec4899;
    --purple-bright: #9333ea;
    --blue-bright: #0284c7;
    --green-bright: #059669;
    --yellow-bright: #eab308;

    /* Text Colors */
    --text-color: #2c3e50;
    --text-light: #6c757d;
    --text-muted: #95a5a6;

    /* Background Colors */
    --bg-color: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0fdf4 100%);
    --hero-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 25%, #059669 75%, #16a34a 100%);
    --card-bg: #ffffff;
    --card-hover-bg: #f1f5f9;
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);

    /* Shadows & Effects */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Glows */
    --glow-primary: 0 0 20px rgba(99, 102, 241, 0.4);
    --glow-secondary: 0 0 20px rgba(245, 158, 11, 0.4);
    --glow-accent: 0 0 20px rgba(16, 185, 129, 0.4);
    --glow-success: 0 0 20px rgba(34, 197, 94, 0.4);

    /* Border & Radius */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-color: #e9ecef;

    /* Layout */
    --header-height: 80px;
    --footer-bg: #2c3e50;
    --footer-text: #ecf0f1;
    --tab-height: 60px;

    /* Transitions & Animations */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography */
    --font-family-primary: 'Tajawal', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-arabic: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
}

[dir="rtl"] {
    --text-direction: right;
}

[dir="ltr"] {
    --text-direction: left;
}

/* Enhanced Dark Mode Variables */
.dark-mode {
    /* Enhanced Dark Color Palette */
    --primary-color: #5d8aff;
    --primary-light: #7da3ff;
    --primary-dark: #4a6baf;
    --secondary-color: #adb5bd;
    --accent-color: #20c997;
    --warning-color: #ffc107;
    --danger-color: #e74c3c;
    --info-color: #3498db;

    /* Dark Text Colors */
    --text-color: #ecf0f1;
    --text-light: #bdc3c7;
    --text-muted: #7f8c8d;

    /* Dark Background Colors */
    --bg-color: #1a1a1a;
    --bg-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --card-bg: #2c3e50;
    --card-hover-bg: #34495e;

    /* Dark Shadows & Effects */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.3);
    --glow: 0 0 20px rgba(93, 138, 255, 0.4);

    /* Dark Border */
    --border-color: #495057;

    /* Dark Footer */
    --footer-bg: #1a1a1a;
    --footer-text: #ecf0f1;
}

/* Enhanced Global Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*,
*::before,
*::after {
    transition: var(--transition);
}

/* Enhanced Body Styles */
body {
    font-family: var(--font-family-arabic);
    background: var(--bg-gradient);
    color: var(--text-color);
    text-align: var(--text-direction);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-lg);
    padding-top: calc(var(--header-height) + var(--spacing-lg));
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
}

[dir="ltr"] body {
    font-family: var(--font-family-primary);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-relaxed);
    color: var(--text-light);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Enhanced Focus Styles */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Enhanced Selection Styles */
::selection {
    background-color: var(--primary-color);
    color: white;
}

::-moz-selection {
    background-color: var(--primary-color);
    color: white;
}

/* Enhanced Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: var(--transition);
}

.main-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-xl);
    transform: translateY(-1px);
}

.dark-mode .main-header {
    background: rgba(44, 62, 80, 0.95);
    border-bottom-color: var(--border-color);
}

.dark-mode .main-header.scrolled {
    background: rgba(44, 62, 80, 0.98);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-xl);
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-xl);
}

.header-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition);
}

.header-logo:hover {
    transform: translateY(-2px);
}

.header-logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
    filter: drop-shadow(var(--shadow-sm));
    transition: var(--transition);
}

.header-logo:hover i {
    transform: rotate(5deg) scale(1.1);
    filter: drop-shadow(var(--glow));
}

.header-logo h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.header-logo h1 span {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    margin-top: var(--spacing-xs);
    background: none;
    -webkit-text-fill-color: var(--text-muted);
}

/* Header Search */
.header-search {
    flex: 1;
    max-width: 500px;
    position: relative;
}

.header-search .search-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.header-search .search-box:hover,
.header-search .search-box:focus-within {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.header-search .search-box i {
    color: var(--text-muted);
    font-size: 1rem;
}

.header-search .search-box input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: var(--font-size-base);
    padding: var(--spacing-sm);
    width: 100%;
    font-family: inherit;
}

.header-search .search-box input::placeholder {
    color: var(--text-muted);
    font-weight: 300;
}

.header-search .search-box input:focus {
    outline: none;
    color: var(--text-color);
}

.header-search .search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.header-search .search-results.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-shrink: 0;
}

/* Enhanced Hero Section */
.hero {
    background: var(--hero-gradient);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    color: white;
    padding: var(--spacing-3xl) var(--spacing-xl) var(--spacing-3xl);
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 75vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>') repeat;
    opacity: 0.4;
    animation: float 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: rotate 60s linear infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    font-weight: 800;
    line-height: var(--line-height-tight);
    animation: slideInLeft 1s ease-out 0.2s both;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-subtitle {
    font-size: clamp(1rem, 3vw, 1.4rem);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.95;
    font-weight: 300;
    line-height: var(--line-height-relaxed);
    animation: slideInRight 1s ease-out 0.4s both;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.search-container {
    max-width: 700px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.search-box {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.search-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-slow);
}

.search-box:hover::before {
    left: 100%;
}

.search-box:hover,
.search-box:focus-within {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.search-box i {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
    position: relative;
    z-index: 1;
}

.search-box:focus-within i {
    color: white;
    transform: scale(1.1);
}

.search-box input {
    flex: 1;
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-lg);
    padding: var(--spacing-sm);
    width: 100%;
    font-weight: 400;
    position: relative;
    z-index: 1;
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}

.search-box input:focus {
    outline: none;
    color: white;
}

.search-results {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-height: 400px;
    overflow-y: auto;
    display: none;
    position: fixed;
    width: 100%;
    max-width: 600px;
    left: 50%;
    top: calc(var(--header-height) + 180px);
    transform: translateX(-50%);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    z-index: 2000;
}

.search-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: 1999;
}

.search-backdrop.active {
    display: block;
}

.dark-mode .search-results {
    background: var(--card-bg);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

.search-result {
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
@media (max-width: 768px) {
    .search-results {
        width: 90%;
        top: calc(var(--header-height) + 220px);
    }
}

    background: var(--card-bg);
}

.search-result:hover {
    background: var(--bg-color);
}

.dark-mode .search-result {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-results.active {
    display: block;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-3xl);
    animation: fadeInUp 1s ease-out 0.8s both;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-slow);
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.05);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.stat-number {
    display: block;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.stat-label {
    font-size: var(--font-size-base);
    opacity: 0.95;
    font-weight: 300;
    position: relative;
    z-index: 1;
}

h1 {
    color: var(--primary-color);
    font-size: 2rem;
}

h1 span {
    display: block;
    font-size: 1.2rem;
    color: var(--secondary-color);
}

/* Tools Navigation */
.tools-tabs {
    position: sticky;
    top: var(--header-height);
    background: var(--card-bg);
    box-shadow: var(--shadow);
    z-index: 90;
    margin: 0 -20px 2rem;
    padding: 0.5rem 1rem;
}

.tabs-container {
    display: flex;
    max-width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    gap: 0.5rem;
    position: relative;
    margin: 0 2rem;
    padding: 0.5rem 0;
}

.tabs-container::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-color);
    font-weight: 500;
    white-space: nowrap;
    cursor: pointer;
    transition: var(--tab-transition);
    border-radius: var(--border-radius);
    position: relative;
}

.tab-btn i {
    font-size: 1.2rem;
    transition: var(--tab-transition);
}

.tab-btn:hover {
    background: rgba(var(--primary-color-rgb), 0.1);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.tab-btn.active i {
    transform: scale(1.1);
}

.tabs-arrows {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.5rem;
}

.tab-arrow {
    pointer-events: auto;
    border: none;
    background: var(--primary-color);
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0.9;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    z-index: 10;
    position: relative;
}

.tab-arrow:hover {
    opacity: 1;
    transform: scale(1.1);
}

[dir="rtl"] .tab-arrow i.fa-chevron-left {
    transform: rotate(180deg);
}

[dir="rtl"] .tab-arrow i.fa-chevron-right {
    transform: rotate(180deg);
}

.tools-content {
    position: relative;
}

.tool-category {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tool-category.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg) 0;
}

/* Enhanced Tool Cards */
.tool-card {
    background: var(--card-gradient);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 220px;
    justify-content: space-between;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.tool-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition);
    transform-origin: left;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 107, 175, 0.05), transparent);
    transition: var(--transition-slow);
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(255, 255, 255, 0.4);
    background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
}

.tool-card:hover::after {
    transform: scaleX(1);
}

.tool-card:active {
    transform: translateY(-8px) scale(1.01);
}

.tool-card i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    transition: var(--transition);
    filter: drop-shadow(var(--shadow-sm));
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tool-card:hover i {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(var(--glow-primary));
}

/* Category-specific icon colors */
.tool-card[data-category="text"] i {
    background: linear-gradient(135deg, var(--blue-bright), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="email"] i {
    background: linear-gradient(135deg, var(--green-bright), var(--accent-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="image"] i {
    background: linear-gradient(135deg, var(--purple-bright), var(--pink-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="number"] i {
    background: linear-gradient(135deg, var(--orange-bright), var(--yellow-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="time"] i {
    background: linear-gradient(135deg, var(--info-color), var(--blue-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="url"] i {
    background: linear-gradient(135deg, var(--secondary-color), var(--warning-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="daily"] i {
    background: linear-gradient(135deg, var(--success-color), var(--green-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="developer"] i {
    background: linear-gradient(135deg, var(--danger-color), var(--orange-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card[data-category="misc"] i {
    background: linear-gradient(135deg, var(--primary-color), var(--purple-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-card h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
    font-size: var(--font-size-xl);
    font-weight: 600;
    line-height: var(--line-height-tight);
    transition: var(--transition);
}

.tool-card:hover h3 {
    color: var(--primary-color);
}

.tool-card p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.tool-card .en {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: auto;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    width: 100%;
    transition: var(--transition);
}

.tool-card:hover .en {
    color: var(--primary-color);
    border-top-color: var(--primary-color);
}

[dir="ltr"] .tool-card .ar,
[dir="rtl"] .tool-card .en {
    display: none;
}

[dir="ltr"] .tool-card .en,
[dir="rtl"] .tool-card .ar {
    display: block;
}

/* Theme Toggle Styles */
.theme-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Language Toggle Button */
.lang-toggle button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
}

/* Enhanced Footer Styles */
.site-footer {
    background: linear-gradient(135deg, var(--footer-bg) 0%, #1a1f24 100%);
    color: var(--footer-text);
    padding: 5rem 2rem 2rem;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.footer-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><rect width="60" height="60" fill="none"/><path d="M0 0h60v60H0z" fill="none"/><circle cx="2" cy="2" r="2" fill="rgba(255,255,255,0.025)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr repeat(3, 1fr);
    gap: 4rem;
    position: relative;
}

.footer-section {
    position: relative;
}

.footer-section.primary {
    grid-column: 1;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo i {
    font-size: 2rem;
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.footer-logo h3 {
    font-size: 1.8rem;
    margin: 0;
}

.footer-logo h3::after {
    display: none;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    padding-left: 1.2rem;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.footer-links a:hover::before {
    opacity: 1;
}

[dir="rtl"] .footer-links a {
    padding-left: 0;
    padding-right: 1.2rem;
}

[dir="rtl"] .footer-links a::before {
    left: auto;
    right: 0;
}

[dir="rtl"] .footer-links a:hover {
    transform: translateX(-5px);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-hover {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-hover::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 0;
}

.social-hover:hover {
    transform: translateY(-5px);
    color: white;
}

.social-hover:hover::before {
    top: 0;
}

.social-hover i {
    position: relative;
    z-index: 1;
}

.newsletter .subscribe-form {
    display: flex;
    gap: 0.5rem;
}

.subscribe-form input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: white;
    transition: all 0.3s ease;
}

.subscribe-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.1);
}

.subscribe-form button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.subscribe-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.footer-bottom {
    text-align: center;
    padding-top: 3rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.footer-meta {
    display: flex;
    gap: 1.5rem;
}

.footer-meta a {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-meta a:hover {
    color: var(--primary-color);
}

.footer-meta .separator {
    color: rgba(255, 255, 255, 0.3);
}

/* Responsive Footer */
@media (max-width: 1200px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 3rem;
    }

    .footer-section.primary {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .site-footer {
        padding: 3rem 1.5rem 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }

    .footer-info {
        flex-direction: column;
        text-align: center;
    }

    .footer-meta {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .footer-section h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links a {
        padding-left: 0;
    }

    .footer-links a::before {
        display: none;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
        padding-top: calc(var(--header-height) + 10px);
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }

    .header-logo span {
        display: none;
    }

    .theme-toggle i {
        display: none;
    }
}

/* Tool Page Specific Styles */
.tool-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.input-group, .output-group {
    margin-bottom: 20px;
}

.input-group label, .output-group label {
    display: block;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: bold;
}

.input-group textarea, .output-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--secondary-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-size: 1rem;
    resize: vertical;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.button-group button, .output-group button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.button-group button:hover, .output-group button:hover {
    background-color: var(--secondary-color);
}

/* Image Tool Styles */
.image-preview {
    max-width: 100%;
    margin: 20px 0;
    border-radius: var(--border-radius);
}

.image-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.image-controls input[type="number"],
.image-controls input[type="range"] {
    width: 100%;
    padding: 5px;
    border: 1px solid var(--secondary-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Calculator Styles */
.calculator-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 400px;
    margin: 20px auto;
}

.calculator-display {
    grid-column: 1 / -1;
    background-color: var(--card-bg);
    padding: 20px;
    text-align: right;
    font-size: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.calculator-grid button {
    padding: 15px;
    font-size: 1.2rem;
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.calculator-grid button:hover {
    background-color: var(--secondary-color);
}

/* Timer Styles */
.timer-display {
    font-size: 3rem;
    text-align: center;
    margin: 20px 0;
    font-family: monospace;
    color: var(--primary-color);
}

.timer-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

/* Currency Converter Styles */
.currency-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.currency-grid select {
    padding: 10px;
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--secondary-color);
}

/* Language-specific Display */
[dir="ltr"] .ar,
[dir="rtl"] .en {
    display: none;
}

[dir="ltr"] .en,
[dir="rtl"] .ar {
    display: block;
}

/* Back Button */
footer a {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-block;
    margin-top: 20px;
    font-weight: bold;
}

footer a:hover {
    color: var(--secondary-color);
}

/* Header Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }

    .header-search {
        order: 2;
        max-width: 100%;
    }

    .header-controls {
        order: 3;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 var(--spacing-lg);
        gap: var(--spacing-md);
    }

    .header-logo h1 {
        font-size: var(--font-size-lg);
    }

    .header-logo span {
        display: none;
    }

    .header-controls {
        gap: var(--spacing-md);
    }

    .header-search .search-box {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-search .search-box input {
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .header-logo {
        justify-content: center;
    }

    .header-search {
        order: 1;
        width: 100%;
    }

    .header-controls {
        order: 2;
        gap: var(--spacing-sm);
    }
}

/* Dark Mode Header Search */
.dark-mode .header-search .search-box {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .header-search .search-box:hover,
.dark-mode .header-search .search-box:focus-within {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .header-search .search-box input {
    color: white;
}

.dark-mode .header-search .search-box input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.dark-mode .header-search .search-results {
    background: var(--bg-color);
    border-color: var(--border-color);
}