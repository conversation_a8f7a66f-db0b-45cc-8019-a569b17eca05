<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول العملات - Currency Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .converter-container {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }
        .currency-box {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .currency-box select {
            width: 100%;
            margin: 10px 0;
            padding: 8px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .currency-box input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 1.2rem;
        }
        .swap-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            cursor: pointer;
            transition: transform 0.3s;
        }
        .swap-icon:hover {
            transform: rotate(180deg);
        }
        .rate-info {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            color: var(--text-color);
        }
        .common-conversions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .common-conversion {
            background-color: var(--card-bg);
            padding: 15px;
            border-radius: var(--border-radius);
            text-align: center;
        }
    </style>
</head>
<body>
    <header>
        <h1>محول العملات <span>Currency Converter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="converter-container">
            <div class="currency-box">
                <label for="fromAmount" class="ar">المبلغ:</label>
                <label for="fromAmount" class="en">Amount:</label>
                <input type="number" id="fromAmount" min="0" step="0.01" value="1">
                
                <label for="fromCurrency" class="ar">من:</label>
                <label for="fromCurrency" class="en">From:</label>
                <select id="fromCurrency">
                    <option value="USD">USD - دولار أمريكي</option>
                    <option value="EUR">EUR - يورو</option>
                    <option value="GBP">GBP - جنيه إسترليني</option>
                    <option value="SAR">SAR - ريال سعودي</option>
                    <option value="AED">AED - درهم إماراتي</option>
                    <option value="EGP">EGP - جنيه مصري</option>
                </select>
            </div>

            <i class="fas fa-exchange-alt swap-icon" id="swapBtn"></i>

            <div class="currency-box">
                <label for="toAmount" class="ar">المبلغ المحول:</label>
                <label for="toAmount" class="en">Converted Amount:</label>
                <input type="number" id="toAmount" readonly>
                
                <label for="toCurrency" class="ar">إلى:</label>
                <label for="toCurrency" class="en">To:</label>
                <select id="toCurrency">
                    <option value="EGP">EGP - جنيه مصري</option>
                    <option value="USD">USD - دولار أمريكي</option>
                    <option value="EUR">EUR - يورو</option>
                    <option value="GBP">GBP - جنيه إسترليني</option>
                    <option value="SAR">SAR - ريال سعودي</option>
                    <option value="AED">AED - درهم إماراتي</option>
                </select>
            </div>
        </div>

        <div class="rate-info" id="rateInfo"></div>

        <div class="common-conversions" id="commonConversions">
            <!-- Common conversions will be added here dynamically -->
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const fromAmount = document.getElementById('fromAmount');
            const toAmount = document.getElementById('toAmount');
            const fromCurrency = document.getElementById('fromCurrency');
            const toCurrency = document.getElementById('toCurrency');
            const swapBtn = document.getElementById('swapBtn');
            const rateInfo = document.getElementById('rateInfo');
            const commonConversions = document.getElementById('commonConversions');

            // Exchange rates (as of a specific date)
            const rates = {
                USD: { EUR: 0.92, GBP: 0.79, SAR: 3.75, AED: 3.67, EGP: 31.00 },
                EUR: { USD: 1.09, GBP: 0.86, SAR: 4.08, AED: 4.00, EGP: 33.77 },
                GBP: { USD: 1.27, EUR: 1.16, SAR: 4.75, AED: 4.65, EGP: 39.27 },
                SAR: { USD: 0.27, EUR: 0.25, GBP: 0.21, AED: 0.98, EGP: 8.27 },
                AED: { USD: 0.27, EUR: 0.25, GBP: 0.22, SAR: 1.02, EGP: 8.45 },
                EGP: { USD: 0.032, EUR: 0.030, GBP: 0.025, SAR: 0.121, AED: 0.118 }
            };

            // Initialize rates for each currency to itself
            Object.keys(rates).forEach(currency => {
                rates[currency][currency] = 1;
            });

            // Convert amount
            function convert() {
                const from = fromCurrency.value;
                const to = toCurrency.value;
                const amount = parseFloat(fromAmount.value);

                if (isNaN(amount)) {
                    toAmount.value = '';
                    rateInfo.textContent = '';
                    return;
                }

                const rate = rates[from][to];
                const result = amount * rate;
                toAmount.value = result.toFixed(2);

                const lang = document.documentElement.lang;
                rateInfo.textContent = lang === 'ar' ?
                    `1 ${from} = ${rate.toFixed(4)} ${to}` :
                    `1 ${from} = ${rate.toFixed(4)} ${to}`;

                updateCommonConversions(from);
            }

            // Update common conversions
            function updateCommonConversions(from) {
                const commonAmounts = [1, 5, 10, 50, 100];
                let html = '';

                commonAmounts.forEach(amount => {
                    const conversions = Object.entries(rates[from])
                        .filter(([currency]) => currency !== from)
                        .map(([currency, rate]) => {
                            const converted = (amount * rate).toFixed(2);
                            return `${converted} ${currency}`;
                        })
                        .join(' | ');

                    html += `
                        <div class="common-conversion">
                            <strong>${amount} ${from}</strong>
                            <div>${conversions}</div>
                        </div>
                    `;
                });

                commonConversions.innerHTML = html;
            }

            // Swap currencies
            swapBtn.addEventListener('click', () => {
                const tempCurrency = fromCurrency.value;
                fromCurrency.value = toCurrency.value;
                toCurrency.value = tempCurrency;
                convert();
            });

            // Event listeners
            fromAmount.addEventListener('input', convert);
            fromCurrency.addEventListener('change', convert);
            toCurrency.addEventListener('change', convert);

            // Load saved values
            fromAmount.value = localStorage.getItem('converter_amount') || 1;
            fromCurrency.value = localStorage.getItem('converter_from') || 'USD';
            toCurrency.value = localStorage.getItem('converter_to') || 'EGP';

            // Save values on change
            fromAmount.addEventListener('change', () => {
                localStorage.setItem('converter_amount', fromAmount.value);
            });
            fromCurrency.addEventListener('change', () => {
                localStorage.setItem('converter_from', fromCurrency.value);
            });
            toCurrency.addEventListener('change', () => {
                localStorage.setItem('converter_to', toCurrency.value);
            });

            // Initial conversion
            convert();
        });
    </script>
</body>
</html>