<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول العملات - Currency Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .converter-form {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-color);
        }
        .currency-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .currency-input select,
        .currency-input input {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .currency-input select {
            width: 120px;
        }
        .currency-input input {
            flex-grow: 1;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: 4px;
            text-align: center;
            font-size: 1.2em;
        }
        .swap-btn {
            display: block;
            margin: 10px auto;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5em;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <h1>محول العملات <span>Currency Converter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main>
        <div class="converter-form">
            <div class="currency-input">
                <select id="fromCurrency">
                    <option value="USD">USD - دولار أمريكي</option>
                    <option value="EUR">EUR - يورو</option>
                    <option value="GBP">GBP - جنيه إسترليني</option>
                    <option value="JPY">JPY - ين ياباني</option>
                    <option value="EGP">EGP - جنيه مصري</option>
                    <option value="SAR">SAR - ريال سعودي</option>
                    <option value="AED">AED - درهم إماراتي</option>
                    <option value="KWD">KWD - دينار كويتي</option>
                </select>
                <input type="number" id="amount" placeholder="أدخل المبلغ (Enter amount)">
            </div>

            <button class="swap-btn" onclick="swapCurrencies()">
                <i class="fas fa-exchange-alt"></i>
            </button>

            <div class="currency-input">
                <select id="toCurrency">
                    <option value="EGP">EGP - جنيه مصري</option>
                    <option value="USD">USD - دولار أمريكي</option>
                    <option value="EUR">EUR - يورو</option>
                    <option value="GBP">GBP - جنيه إسترليني</option>
                    <option value="JPY">JPY - ين ياباني</option>
                    <option value="SAR">SAR - ريال سعودي</option>
                    <option value="AED">AED - درهم إماراتي</option>
                    <option value="KWD">KWD - دينار كويتي</option>
                </select>
                <input type="number" id="result" readonly placeholder="النتيجة (Result)">
            </div>

            <button onclick="convertCurrency()" class="primary-button">تحويل (Convert)</button>

            <div class="result" id="conversionResult">
                <!-- Conversion result will appear here -->
            </div>
        </div>
    </main>

    <script>
        const API_KEY = 'YOUR_API_KEY'; // Replace with your API key
        
        async function convertCurrency() {
            const amount = document.getElementById('amount').value;
            const fromCurrency = document.getElementById('fromCurrency').value;
            const toCurrency = document.getElementById('toCurrency').value;

            if (!amount || isNaN(amount)) {
                alert('الرجاء إدخال مبلغ صحيح (Please enter a valid amount)');
                return;
            }

            try {
                // Using ExchangeRate-API (you can replace with your preferred API)
                const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${fromCurrency}`);
                const data = await response.json();
                
                const rate = data.rates[toCurrency];
                const result = (amount * rate).toFixed(2);
                
                document.getElementById('result').value = result;
                document.getElementById('conversionResult').innerHTML = `
                    ${amount} ${fromCurrency} = ${result} ${toCurrency}
                `;
            } catch (error) {
                alert('حدث خطأ في التحويل. الرجاء المحاولة مرة أخرى (Conversion error. Please try again)');
                console.error('Conversion error:', error);
            }
        }

        function swapCurrencies() {
            const fromCurrency = document.getElementById('fromCurrency');
            const toCurrency = document.getElementById('toCurrency');
            const temp = fromCurrency.value;
            
            fromCurrency.value = toCurrency.value;
            toCurrency.value = temp;
            
            if (document.getElementById('amount').value) {
                convertCurrency();
            }
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
        });

        // Language toggle
        const toggleLang = document.getElementById('toggleLang');
        toggleLang.addEventListener('click', () => {
            document.documentElement.dir = document.documentElement.dir === 'rtl' ? 'ltr' : 'rtl';
            document.documentElement.lang = document.documentElement.lang === 'ar' ? 'en' : 'ar';
        });
    </script>
</body>
</html>