<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة الاستثمار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-card {
            margin-top: 20px;
        }
    </style>
</head>

<body>
    <div class="container py-5">
        <h1 class="text-center mb-4">حاسبة الاستثمار التراكمية</h1>
        <div class="card shadow-sm p-4">
            <form id="investmentForm">
                <div class="mb-3">
                    <label for="monthlyInvestment" class="form-label">مبلغ الاستثمار الشهري (ريال)</label>
                    <input type="number" class="form-control" id="monthlyInvestment" placeholder="أدخل مبلغ الاستثمار" required>
                </div>
                <div class="mb-3">
                    <label for="annualReturn" class="form-label">نسبة العائد السنوي المتوقعة (%)</label>
                    <input type="number" class="form-control" id="annualReturn" placeholder="أدخل نسبة العائد" required>
                </div>
                <div class="mb-3">
                    <label for="investmentYears" class="form-label">عدد سنوات الاستثمار</label>
                    <input type="number" class="form-control" id="investmentYears" placeholder="أدخل عدد السنوات" required>
                </div>
                <button type="button" class="btn btn-primary w-100" onclick="calculateInvestment()">احسب</button>
            </form>
        </div>

        <div class="card shadow-sm p-4 result-card" id="resultCard" style="display: none;">
            <h4 class="text-center mb-3">نتائج الاستثمار</h4>
            <ul class="list-group">
                <li class="list-group-item">إجمالي الاستثمار: <span id="totalInvestment"></span> ريال</li>
                <li class="list-group-item">إجمالي الأرباح: <span id="totalProfit"></span> ريال</li>
                <li class="list-group-item">أرباح أول سنة: <span id="firstYearProfit"></span> ريال</li>
                <li class="list-group-item">أرباح أول 5 سنوات: <span id="fiveYearProfit"></span> ريال</li>
                <li class="list-group-item">أرباح 10 سنوات: <span id="tenYearProfit"></span> ريال</li>
            </ul>
        </div>
    </div>

    <script>
        function calculateInvestment() {
            // الحصول على القيم من الحقول
            const monthlyInvestment = parseFloat(document.getElementById('monthlyInvestment').value);
            const annualReturn = parseFloat(document.getElementById('annualReturn').value) / 100;
            const investmentYears = parseInt(document.getElementById('investmentYears').value);

            // التحقق من صحة القيم
            if (isNaN(monthlyInvestment) || isNaN(annualReturn) || isNaN(investmentYears) || monthlyInvestment <= 0 || annualReturn <= 0 || investmentYears <= 0) {
                alert("يرجى إدخال قيم صحيحة");
                return;
            }

            let totalInvestment = 0;
            let totalProfit = 0;
            let firstYearProfit = 0;
            let fiveYearProfit = 0;
            let tenYearProfit = 0;

            // حساب الأرباح التراكمية
            let currentBalance = 0;
            for (let year = 1; year <= investmentYears; year++) {
                for (let month = 1; month <= 12; month++) {
                    totalInvestment += monthlyInvestment;
                    currentBalance += monthlyInvestment;
                    const monthlyProfit = currentBalance * (annualReturn / 12);
                    currentBalance += monthlyProfit;
                }

                if (year === 1) {
                    firstYearProfit = currentBalance - totalInvestment;
                }
                if (year === 5) {
                    fiveYearProfit = currentBalance - totalInvestment;
                }
                if (year === 10) {
                    tenYearProfit = currentBalance - totalInvestment;
                }
            }

            totalProfit = currentBalance - totalInvestment;

            // عرض النتائج
            document.getElementById('totalInvestment').innerText = totalInvestment.toFixed(2);
            document.getElementById('totalProfit').innerText = totalProfit.toFixed(2);
            document.getElementById('firstYearProfit').innerText = firstYearProfit.toFixed(2);
            document.getElementById('fiveYearProfit').innerText = fiveYearProfit.toFixed(2);
            document.getElementById('tenYearProfit').innerText = tenYearProfit.toFixed(2);

            document.getElementById('resultCard').style.display = 'block';
        }
    </script>
</body>

</html>
