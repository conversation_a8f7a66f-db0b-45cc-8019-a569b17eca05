<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل الأداء في Google Search Console</title>
</head>
<body>
    <h2>أدخل الروابط مع الكلمات المفتاحية</h2>
    <textarea id="linksInput" rows="10" cols="80" placeholder="ضع كل رابط في سطر جديد، ثم | ثم النص الذي سيتم عرضه"></textarea>
    <br>
    <button onclick="generateLinks()">استبدال وإنشاء الروابط</button>
    
    <h2>روابط تحليل الأداء</h2>
    <ul id="linksList"></ul>
    
    <button onclick="openAllLinks()">فتح جميع الروابط</button>

    <script>
        function generateLinks() {
            const baseUrl = "https://search.google.com/search-console/performance/search-analytics?resource_id=https%3A%2F%2Fcouponwafir.com%2F&hl=AR&metrics=IMPRESSIONS%2CPOSITION&breakdown=page&page=!{PAGEURL}%2F&num_of_days=28&compare_date=PREV";
            const input = document.getElementById("linksInput").value;
            const lines = input.split("\n");
            const list = document.getElementById("linksList");
            list.innerHTML = "";
            
            lines.forEach(line => {
                if (line.trim() === "") return;
                let [url, anchorText] = line.split("|");
                url = url.trim();
                anchorText = anchorText ? anchorText.trim() : url;
                let searchConsoleUrl = baseUrl.replace("{PAGEURL}", encodeURIComponent(url));
                
                let listItem = document.createElement("li");
                let link = document.createElement("a");
                link.href = searchConsoleUrl;
                link.target = "_blank";
                link.textContent = anchorText;
                
                listItem.appendChild(link);
                list.appendChild(listItem);
            });
        }

        function openAllLinks() {
            const links = document.querySelectorAll('#linksList li a');
            links.forEach(link => {
                window.open(link.href, '_blank');
            });
        }
    </script>
</body>
</html>
