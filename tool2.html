<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عدّاد الكلمات والحروف - Word & Character Counter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>عدّاد الكلمات والحروف <span>Word & Character Counter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="input-group">
            <label for="inputText" class="ar">أدخل النص هنا:</label>
            <label for="inputText" class="en">Enter your text here:</label>
            <textarea id="inputText" rows="5" placeholder="Type or paste your text..."></textarea>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3 class="ar">عدد الكلمات</h3>
                <h3 class="en">Words</h3>
                <span id="wordCount">0</span>
            </div>
            
            <div class="stat-card">
                <h3 class="ar">عدد الحروف</h3>
                <h3 class="en">Characters</h3>
                <span id="charCount">0</span>
            </div>
            
            <div class="stat-card">
                <h3 class="ar">عدد الحروف (بدون مسافات)</h3>
                <h3 class="en">Characters (no spaces)</h3>
                <span id="charNoSpacesCount">0</span>
            </div>
            
            <div class="stat-card">
                <h3 class="ar">عدد المسافات</h3>
                <h3 class="en">Spaces</h3>
                <span id="spaceCount">0</span>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            inputText.value = window.utils.getSavedInput('wordCounter');
            
            // Count elements
            function updateCounts() {
                const text = inputText.value;
                window.utils.saveInput('wordCounter', text);
                
                // Word count (split by whitespace and filter empty strings)
                const words = text.trim() ? text.trim().split(/\s+/).filter(word => word.length > 0) : [];
                document.getElementById('wordCount').textContent = words.length;
                
                // Character counts
                document.getElementById('charCount').textContent = text.length;
                document.getElementById('charNoSpacesCount').textContent = text.replace(/\s/g, '').length;
                document.getElementById('spaceCount').textContent = (text.match(/\s/g) || []).length;
            }
            
            // Update counts on input
            inputText.addEventListener('input', updateCounts);
            
            // Initial count
            updateCounts();
        });
    </script>
</body>
</html>