<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>عدّاد الكلمات والحروف - حساب عدد الكلمات والحروف والمسافات | Useful Tools</title>
    <meta name="title" content="عدّاد الكلمات والحروف - حساب عدد الكلمات والحروف والمسافات">
    <meta name="description" content="أداة مجانية لحساب عدد الكلمات والحروف والمسافات في النص. مفيدة للكتاب والطلاب والمحررين لمراقبة طول النصوص.">
    <meta name="keywords" content="عداد الكلمات, عداد الحروف, حساب الكلمات, word counter, character counter, text analysis">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Useful Tools">
    <link rel="canonical" href="https://usefultools.com/tool2.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://usefultools.com/tool2.html">
    <meta property="og:title" content="عدّاد الكلمات والحروف - حساب عدد الكلمات والحروف والمسافات">
    <meta property="og:description" content="أداة مجانية لحساب عدد الكلمات والحروف والمسافات في النص. مفيدة للكتاب والطلاب والمحررين.">
    <meta property="og:image" content="https://usefultools.com/assets/word-counter-og.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://usefultools.com/tool2.html">
    <meta property="twitter:title" content="عدّاد الكلمات والحروف - حساب عدد الكلمات والحروف والمسافات">
    <meta property="twitter:description" content="أداة مجانية لحساب عدد الكلمات والحروف والمسافات في النص.">
    <meta property="twitter:image" content="https://usefultools.com/assets/word-counter-og.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <meta name="theme-color" content="#2563eb">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="enhanced-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "عدّاد الكلمات والحروف",
        "alternateName": "Word & Character Counter",
        "url": "https://usefultools.com/tool2.html",
        "description": "أداة مجانية لحساب عدد الكلمات والحروف والمسافات في النص",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "حساب عدد الكلمات",
            "حساب عدد الحروف",
            "حساب عدد الحروف بدون مسافات",
            "حساب عدد المسافات",
            "تحليل النص المباشر"
        ]
    }
    </script>
</head>
<body>
    <header>
        <h1>عدّاد الكلمات والحروف <span>Word & Character Counter</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <!-- Breadcrumb Navigation -->
        <nav class="breadcrumb" aria-label="Breadcrumb">
            <div class="breadcrumb-item">
                <a href="index.html">
                    <i class="fas fa-home"></i>
                    <span class="ar">الرئيسية</span>
                    <span class="en">Home</span>
                </a>
            </div>
            <div class="breadcrumb-item">
                <span class="ar">أدوات النصوص</span>
                <span class="en">Text Tools</span>
            </div>
            <div class="breadcrumb-item active">
                <span class="ar">عدّاد الكلمات والحروف</span>
                <span class="en">Word & Character Counter</span>
            </div>
        </nav>

        <!-- Tool Header -->
        <div class="tool-header">
            <div class="tool-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="tool-info">
                <h1 class="ar">عدّاد الكلمات والحروف</h1>
                <h1 class="en">Word & Character Counter</h1>
                <p class="ar">حساب عدد الكلمات والحروف والمسافات في النص بشكل مباشر</p>
                <p class="en">Count words, characters, and spaces in text in real-time</p>
            </div>
        </div>

        <div class="tool-workspace">
            <div class="input-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-edit"></i>
                        <span class="ar">إدخال النص</span>
                        <span class="en">Text Input</span>
                    </h3>
                    <div class="section-actions">
                        <button class="btn-icon" onclick="pasteFromClipboard()" title="لصق من الحافظة">
                            <i class="fas fa-paste"></i>
                        </button>
                        <button class="btn-icon" onclick="clearInput()" title="مسح النص">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="input-group">
                    <textarea id="inputText" rows="10" placeholder="اكتب أو الصق النص هنا للحصول على إحصائيات مباشرة... / Type or paste text here for real-time statistics..."></textarea>
                </div>
            </div>

            <div class="stats-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-chart-bar"></i>
                        <span class="ar">إحصائيات النص</span>
                        <span class="en">Text Statistics</span>
                    </h3>
                    <div class="section-actions">
                        <button class="btn-icon" onclick="exportStats()" title="تصدير الإحصائيات">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-file-word"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">عدد الكلمات</h3>
                            <h3 class="en">Words</h3>
                            <span id="wordCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card secondary">
                        <div class="stat-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">عدد الحروف</h3>
                            <h3 class="en">Characters</h3>
                            <span id="charCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card accent">
                        <div class="stat-icon">
                            <i class="fas fa-text-width"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">بدون مسافات</h3>
                            <h3 class="en">No Spaces</h3>
                            <span id="charNoSpacesCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-space-shuttle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">عدد المسافات</h3>
                            <h3 class="en">Spaces</h3>
                            <span id="spaceCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-paragraph"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">عدد الفقرات</h3>
                            <h3 class="en">Paragraphs</h3>
                            <span id="paragraphCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="ar">وقت القراءة</h3>
                            <h3 class="en">Reading Time</h3>
                            <span id="readingTime">0 دقيقة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const inputText = document.getElementById('inputText');
            inputText.value = window.utils.getSavedInput('wordCounter');

            // Count elements
            function updateCounts() {
                const text = inputText.value;
                window.utils.saveInput('wordCounter', text);

                // Word count (split by whitespace and filter empty strings)
                const words = text.trim() ? text.trim().split(/\s+/).filter(word => word.length > 0) : [];
                document.getElementById('wordCount').textContent = words.length;

                // Character counts
                document.getElementById('charCount').textContent = text.length;
                document.getElementById('charNoSpacesCount').textContent = text.replace(/\s/g, '').length;
                document.getElementById('spaceCount').textContent = (text.match(/\s/g) || []).length;

                // Paragraph count
                const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim().length > 0) : [];
                document.getElementById('paragraphCount').textContent = paragraphs.length;

                // Reading time (average 200 words per minute)
                const readingTimeMinutes = Math.ceil(words.length / 200);
                const readingTimeElement = document.getElementById('readingTime');
                const currentLang = document.documentElement.lang;

                if (currentLang === 'ar') {
                    readingTimeElement.textContent = readingTimeMinutes === 1 ? 'دقيقة واحدة' : `${readingTimeMinutes} دقيقة`;
                } else {
                    readingTimeElement.textContent = readingTimeMinutes === 1 ? '1 minute' : `${readingTimeMinutes} minutes`;
                }

                // Add animation to updated stats
                animateStatCards();
            }

            // Animate stat cards when updated
            function animateStatCards() {
                const statCards = document.querySelectorAll('.stat-card');
                statCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transform = 'scale(1.05)';
                        setTimeout(() => {
                            card.style.transform = 'scale(1)';
                        }, 150);
                    }, index * 50);
                });
            }

            // Update counts on input
            inputText.addEventListener('input', updateCounts);

            // Additional functions
            window.pasteFromClipboard = async () => {
                try {
                    const text = await navigator.clipboard.readText();
                    inputText.value = text;
                    updateCounts();
                } catch (err) {
                    console.error('Failed to read clipboard:', err);
                }
            };

            window.clearInput = () => {
                inputText.value = '';
                updateCounts();
            };

            window.exportStats = () => {
                const text = inputText.value;
                const words = text.trim() ? text.trim().split(/\s+/).filter(word => word.length > 0) : [];
                const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim().length > 0) : [];
                const readingTime = Math.ceil(words.length / 200);

                const stats = {
                    'عدد الكلمات / Words': words.length,
                    'عدد الحروف / Characters': text.length,
                    'عدد الحروف بدون مسافات / Characters (no spaces)': text.replace(/\s/g, '').length,
                    'عدد المسافات / Spaces': (text.match(/\s/g) || []).length,
                    'عدد الفقرات / Paragraphs': paragraphs.length,
                    'وقت القراءة / Reading Time': `${readingTime} دقيقة / minutes`
                };

                const statsText = Object.entries(stats)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n');

                const blob = new Blob([statsText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'text-statistics.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            };

            // Initial count
            updateCounts();
        });
    </script>
</body>
</html>