<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة النسبة المئوية - Percentage Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .calculator {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-group {
            margin-bottom: 20px;
        }
        .input-group label {
            display: block;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 5px;
            text-align: center;
            font-size: 1.2em;
        }
        .examples {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--secondary-bg);
            border-radius: var(--border-radius);
        }
        .examples h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        .example-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <header>
        <h1>حاسبة النسبة المئوية <span>Percentage Calculator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="calculator">
            <div class="input-group">
                <label for="percentage">النسبة المئوية (%) - Percentage</label>
                <input type="number" id="percentage" min="0" max="100" placeholder="مثال: 20">
            </div>
            <div class="input-group">
                <label for="number">القيمة الأساسية - Base Value</label>
                <input type="number" id="number" placeholder="مثال: 500">
            </div>
            <button onclick="calculatePercentage()" class="primary-button">
                <i class="fas fa-calculator"></i>
                <span class="ar">احسب</span>
                <span class="en">Calculate</span>
            </button>
            <div class="result" id="result">
                النتيجة ستظهر هنا - Result will appear here
            </div>

            <div class="examples">
                <h3>أمثلة شائعة - Common Examples</h3>
                <div class="example-item">15% من 200 = 30</div>
                <div class="example-item">50% من 1000 = 500</div>
                <div class="example-item">75% من 80 = 60</div>
            </div>
        </div>
    </main>

    <script>
        function calculatePercentage() {
            const percentage = parseFloat(document.getElementById('percentage').value);
            const number = parseFloat(document.getElementById('number').value);
            
            if (isNaN(percentage) || isNaN(number)) {
                document.getElementById('result').innerHTML = 'الرجاء إدخال أرقام صحيحة - Please enter valid numbers';
                return;
            }

            const result = (percentage / 100) * number;
            document.getElementById('result').innerHTML = `
                <div class="ar">${percentage}% من ${number} = ${result}</div>
                <div class="en">${percentage}% of ${number} = ${result}</div>
            `;
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>