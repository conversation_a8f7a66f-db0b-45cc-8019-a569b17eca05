<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكي أصوات الطبيعة - Nature Sounds Player</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .player {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .sounds-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .sound-card {
            background-color: var(--secondary-bg);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .sound-card:hover {
            transform: translateY(-5px);
        }
        .sound-card.playing {
            background-color: var(--primary-color);
            color: white;
        }
        .sound-card i {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .volume-control {
            margin-top: 10px;
            padding: 10px;
        }
        .volume-slider {
            width: 100%;
            -webkit-appearance: none;
            height: 5px;
            border-radius: 5px;
            background: var(--border-color);
            outline: none;
        }
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
        }
        .mixer {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--secondary-bg);
            border-radius: 10px;
        }
        .mixer h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        .active-sounds {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .active-sound {
            background-color: var(--primary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .timer-section {
            margin-top: 20px;
            text-align: center;
        }
        .timer-control {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        .timer-button {
            padding: 5px 15px;
            border: none;
            border-radius: 5px;
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
        }
        .timer-button:hover {
            background-color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>محاكي أصوات الطبيعة <span>Nature Sounds Player</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="player">
            <div class="sounds-grid">
                <div class="sound-card" onclick="toggleSound('rain')">
                    <i class="fas fa-cloud-rain"></i>
                    <h3>مطر - Rain</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('rain', this.value)">
                    </div>
                </div>

                <div class="sound-card" onclick="toggleSound('ocean')">
                    <i class="fas fa-water"></i>
                    <h3>أمواج - Ocean</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('ocean', this.value)">
                    </div>
                </div>

                <div class="sound-card" onclick="toggleSound('forest')">
                    <i class="fas fa-tree"></i>
                    <h3>غابة - Forest</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('forest', this.value)">
                    </div>
                </div>

                <div class="sound-card" onclick="toggleSound('birds')">
                    <i class="fas fa-dove"></i>
                    <h3>عصافير - Birds</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('birds', this.value)">
                    </div>
                </div>

                <div class="sound-card" onclick="toggleSound('thunder')">
                    <i class="fas fa-bolt"></i>
                    <h3>رعد - Thunder</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('thunder', this.value)">
                    </div>
                </div>

                <div class="sound-card" onclick="toggleSound('creek')">
                    <i class="fas fa-stream"></i>
                    <h3>جدول - Creek</h3>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" min="0" max="100" value="50" 
                            oninput="adjustVolume('creek', this.value)">
                    </div>
                </div>
            </div>

            <div class="mixer">
                <h3>الأصوات النشطة - Active Sounds</h3>
                <div class="active-sounds" id="activeSounds"></div>
            </div>

            <div class="timer-section">
                <h3>مؤقت الإيقاف - Sleep Timer</h3>
                <div class="timer-control">
                    <button class="timer-button" onclick="setTimer(30)">30 دقيقة - 30m</button>
                    <button class="timer-button" onclick="setTimer(60)">ساعة - 1h</button>
                    <button class="timer-button" onclick="setTimer(120)">ساعتان - 2h</button>
                    <button class="timer-button" onclick="clearTimer()">إلغاء - Cancel</button>
                </div>
                <div id="timerDisplay"></div>
            </div>
        </div>
    </main>

    <script>
        const sounds = {
            rain: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=2365&type=mp3'), volume: 0.5 },
            ocean: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=2120&type=mp3'), volume: 0.5 },
            forest: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=2156&type=mp3'), volume: 0.5 },
            birds: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=1661&type=mp3'), volume: 0.5 },
            thunder: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=2053&type=mp3'), volume: 0.5 },
            creek: { playing: false, audio: new Audio('https://soundbible.com/grab.php?id=2217&type=mp3'), volume: 0.5 }
        };

        // Set up audio loops
        Object.values(sounds).forEach(sound => {
            sound.audio.loop = true;
        });

        let timerInterval;

        function toggleSound(soundId) {
            const sound = sounds[soundId];
            const card = document.querySelector(`[onclick="toggleSound('${soundId}')"]`);
            
            if (sound.playing) {
                sound.audio.pause();
                card.classList.remove('playing');
            } else {
                sound.audio.play();
                card.classList.add('playing');
            }
            
            sound.playing = !sound.playing;
            updateActiveSounds();
        }

        function adjustVolume(soundId, value) {
            const sound = sounds[soundId];
            sound.volume = value / 100;
            sound.audio.volume = sound.volume;
        }

        function updateActiveSounds() {
            const container = document.getElementById('activeSounds');
            container.innerHTML = '';
            
            Object.entries(sounds).forEach(([id, sound]) => {
                if (sound.playing) {
                    const element = document.createElement('div');
                    element.className = 'active-sound';
                    element.textContent = id.charAt(0).toUpperCase() + id.slice(1);
                    container.appendChild(element);
                }
            });
        }

        function setTimer(minutes) {
            clearTimer();
            let timeLeft = minutes * 60;
            updateTimerDisplay(timeLeft);
            
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimerDisplay(timeLeft);
                
                if (timeLeft <= 0) {
                    stopAllSounds();
                    clearTimer();
                }
            }, 1000);
        }

        function updateTimerDisplay(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            document.getElementById('timerDisplay').textContent = 
                `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function clearTimer() {
            clearInterval(timerInterval);
            document.getElementById('timerDisplay').textContent = '';
        }

        function stopAllSounds() {
            Object.entries(sounds).forEach(([id, sound]) => {
                if (sound.playing) {
                    sound.audio.pause();
                    sound.playing = false;
                    document.querySelector(`[onclick="toggleSound('${id}')"]`)
                        .classList.remove('playing');
                }
            });
            updateActiveSounds();
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>