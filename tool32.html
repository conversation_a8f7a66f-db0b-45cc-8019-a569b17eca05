<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد بطاقات تهنئة - Greeting Card Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .generator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .input-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        .card-preview {
            width: 100%;
            aspect-ratio: 16/9;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
        }
        .card-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            background-size: cover;
            background-position: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .card-title {
            font-size: 2em;
            margin-bottom: 15px;
        }
        .card-message {
            font-size: 1.2em;
            margin-bottom: 15px;
            max-width: 80%;
        }
        .card-recipient {
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        .theme-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .theme-option {
            width: 100%;
            aspect-ratio: 1;
            border-radius: 5px;
            cursor: pointer;
            background-size: cover;
            background-position: center;
            border: 2px solid transparent;
            transition: transform 0.2s;
        }
        .theme-option:hover {
            transform: scale(1.1);
        }
        .theme-option.selected {
            border-color: var(--primary-color);
        }
        .download-section {
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <header>
        <h1>مولد بطاقات تهنئة <span>Greeting Card Generator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="generator">
            <div class="input-section">
                <div class="input-group">
                    <label for="occasion">المناسبة - Occasion</label>
                    <select id="occasion" onchange="updateThemes()">
                        <option value="birthday">عيد ميلاد - Birthday</option>
                        <option value="wedding">زواج - Wedding</option>
                        <option value="graduation">تخرج - Graduation</option>
                        <option value="newborn">مولود جديد - New Baby</option>
                        <option value="general">تهنئة عامة - General Congratulations</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="recipient">اسم المستلم - Recipient Name</label>
                    <input type="text" id="recipient" oninput="updateCard()">
                </div>
                <div class="input-group">
                    <label for="message">الرسالة - Message</label>
                    <textarea id="message" oninput="updateCard()"></textarea>
                </div>
            </div>

            <div class="theme-selector" id="themeSelector"></div>

            <div class="card-preview">
                <div class="card-content" id="cardContent">
                    <div class="card-recipient" id="cardRecipient"></div>
                    <div class="card-message" id="cardMessage"></div>
                </div>
            </div>

            <div class="download-section">
                <button onclick="downloadCard()" class="primary-button">
                    <i class="fas fa-download"></i>
                    <span class="ar">تحميل البطاقة</span>
                    <span class="en">Download Card</span>
                </button>
            </div>
        </div>
    </main>

    <script>
        const themes = {
            birthday: [
                'url("https://source.unsplash.com/1600x900/?birthday,celebration")',
                'url("https://source.unsplash.com/1600x900/?cake,party")',
                'url("https://source.unsplash.com/1600x900/?balloons,celebration")'
            ],
            wedding: [
                'url("https://source.unsplash.com/1600x900/?wedding")',
                'url("https://source.unsplash.com/1600x900/?bride")',
                'url("https://source.unsplash.com/1600x900/?rings,wedding")'
            ],
            graduation: [
                'url("https://source.unsplash.com/1600x900/?graduation")',
                'url("https://source.unsplash.com/1600x900/?diploma")',
                'url("https://source.unsplash.com/1600x900/?university")'
            ],
            newborn: [
                'url("https://source.unsplash.com/1600x900/?baby")',
                'url("https://source.unsplash.com/1600x900/?newborn")',
                'url("https://source.unsplash.com/1600x900/?toys,baby")'
            ],
            general: [
                'url("https://source.unsplash.com/1600x900/?celebration")',
                'url("https://source.unsplash.com/1600x900/?flowers")',
                'url("https://source.unsplash.com/1600x900/?gift")'
            ]
        };

        let currentTheme = themes.birthday[0];

        function updateThemes() {
            const occasion = document.getElementById('occasion').value;
            const themeSelector = document.getElementById('themeSelector');
            themeSelector.innerHTML = '';

            themes[occasion].forEach((theme, index) => {
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.style.backgroundImage = theme;
                themeOption.onclick = () => {
                    document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('selected'));
                    themeOption.classList.add('selected');
                    currentTheme = theme;
                    updateCard();
                };
                if (index === 0) {
                    themeOption.classList.add('selected');
                    currentTheme = theme;
                }
                themeSelector.appendChild(themeOption);
            });

            updateCard();
        }

        function updateCard() {
            const recipient = document.getElementById('recipient').value || 'الاسم - Name';
            const message = document.getElementById('message').value || 'رسالتك هنا - Your message here';
            const cardContent = document.getElementById('cardContent');
            
            cardContent.style.backgroundImage = currentTheme;
            document.getElementById('cardRecipient').textContent = recipient;
            document.getElementById('cardMessage').textContent = message;
        }

        function downloadCard() {
            // Here you would implement the actual card download functionality
            // For this example, we'll just show an alert
            alert('تم حفظ البطاقة! - Card saved!');
        }

        // Initialize the card generator
        updateThemes();

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>