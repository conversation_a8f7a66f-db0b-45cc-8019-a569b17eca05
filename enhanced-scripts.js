// Enhanced JavaScript for Useful Tools

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        if ('performance' in window) {
            this.measurePageLoad();
            this.measureUserInteractions();
        }
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                this.metrics.pageLoad = {
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart
                };
                
                console.log('Page Performance:', this.metrics.pageLoad);
                this.sendMetrics();
            }, 0);
        });
    }

    measureUserInteractions() {
        // Measure First Input Delay (FID)
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'first-input') {
                        this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
                        console.log('First Input Delay:', this.metrics.firstInputDelay);
                    }
                }
            });
            observer.observe({ entryTypes: ['first-input'] });
        }
    }

    sendMetrics() {
        // Send metrics to analytics service
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_performance', {
                'page_load_time': this.metrics.pageLoad.totalTime,
                'dom_content_loaded': this.metrics.pageLoad.domContentLoaded
            });
        }
    }
}

// Enhanced accessibility features
class AccessibilityEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.addKeyboardNavigation();
        this.addAriaLabels();
        this.addFocusManagement();
        this.addScreenReaderSupport();
    }

    addKeyboardNavigation() {
        // Add keyboard navigation for tool cards
        const toolCards = document.querySelectorAll('.tool-card');
        toolCards.forEach((card, index) => {
            card.setAttribute('tabindex', '0');
            card.setAttribute('role', 'button');
            
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
                
                // Arrow key navigation
                if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    e.preventDefault();
                    const nextCard = toolCards[index + 1];
                    if (nextCard) nextCard.focus();
                }
                
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prevCard = toolCards[index - 1];
                    if (prevCard) prevCard.focus();
                }
            });
        });
    }

    addAriaLabels() {
        // Add ARIA labels to interactive elements
        const searchInputs = document.querySelectorAll('#toolSearch, #toolSearchEn');
        searchInputs.forEach(input => {
            input.setAttribute('aria-label', input.placeholder);
            input.setAttribute('role', 'searchbox');
        });

        // Add ARIA labels to buttons
        const buttons = document.querySelectorAll('button:not([aria-label])');
        buttons.forEach(button => {
            const text = button.textContent.trim();
            if (text) {
                button.setAttribute('aria-label', text);
            }
        });
    }

    addFocusManagement() {
        // Manage focus for modals and overlays
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => {
            modal.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeModal(modal);
                }
                
                // Trap focus within modal
                if (e.key === 'Tab') {
                    this.trapFocus(e, modal);
                }
            });
        });
    }

    addScreenReaderSupport() {
        // Add live regions for dynamic content
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        
        window.announceToScreenReader = (message) => {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        };
    }

    trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                lastElement.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastElement) {
                firstElement.focus();
                e.preventDefault();
            }
        }
    }

    closeModal(modal) {
        modal.classList.remove('active');
        // Return focus to trigger element
        const trigger = document.querySelector('[data-modal-trigger]');
        if (trigger) trigger.focus();
    }
}

// Enhanced error handling
class ErrorHandler {
    constructor() {
        this.init();
    }

    init() {
        window.addEventListener('error', this.handleError.bind(this));
        window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    }

    handleError(event) {
        console.error('JavaScript Error:', event.error);
        this.showUserFriendlyError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
        
        // Send error to monitoring service
        this.reportError({
            message: event.error.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error.stack
        });
    }

    handlePromiseRejection(event) {
        console.error('Unhandled Promise Rejection:', event.reason);
        this.showUserFriendlyError('فشل في تحميل بعض المحتوى. يرجى إعادة تحميل الصفحة.');
        
        this.reportError({
            type: 'unhandledrejection',
            reason: event.reason
        });
    }

    showUserFriendlyError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.remove()" aria-label="إغلاق">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.insertBefore(errorDiv, document.body.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    reportError(errorData) {
        // Send error data to monitoring service
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                'description': errorData.message || errorData.reason,
                'fatal': false
            });
        }
    }
}

// Enhanced user preferences
class UserPreferences {
    constructor() {
        this.preferences = this.loadPreferences();
        this.init();
    }

    init() {
        this.applyPreferences();
        this.setupPreferenceControls();
    }

    loadPreferences() {
        const saved = localStorage.getItem('userPreferences');
        return saved ? JSON.parse(saved) : {
            theme: 'auto',
            language: 'auto',
            animations: true,
            fontSize: 'medium',
            highContrast: false
        };
    }

    savePreferences() {
        localStorage.setItem('userPreferences', JSON.stringify(this.preferences));
    }

    applyPreferences() {
        // Apply theme
        if (this.preferences.theme === 'dark') {
            document.body.classList.add('dark-mode');
        } else if (this.preferences.theme === 'light') {
            document.body.classList.remove('dark-mode');
        }

        // Apply animations preference
        if (!this.preferences.animations) {
            document.body.classList.add('reduce-motion');
        }

        // Apply font size
        document.body.classList.add(`font-size-${this.preferences.fontSize}`);

        // Apply high contrast
        if (this.preferences.highContrast) {
            document.body.classList.add('high-contrast');
        }
    }

    setupPreferenceControls() {
        // Add preference controls to settings panel
        this.createPreferencePanel();
    }

    createPreferencePanel() {
        const panel = document.createElement('div');
        panel.className = 'preferences-panel';
        panel.innerHTML = `
            <h3>إعدادات المستخدم</h3>
            <div class="preference-group">
                <label>حجم الخط:</label>
                <select id="fontSizeSelect">
                    <option value="small">صغير</option>
                    <option value="medium">متوسط</option>
                    <option value="large">كبير</option>
                </select>
            </div>
            <div class="preference-group">
                <label>
                    <input type="checkbox" id="animationsToggle">
                    تفعيل الحركات
                </label>
            </div>
            <div class="preference-group">
                <label>
                    <input type="checkbox" id="highContrastToggle">
                    تباين عالي
                </label>
            </div>
        `;

        // Add event listeners
        const fontSizeSelect = panel.querySelector('#fontSizeSelect');
        fontSizeSelect.value = this.preferences.fontSize;
        fontSizeSelect.addEventListener('change', (e) => {
            this.preferences.fontSize = e.target.value;
            this.savePreferences();
            this.applyPreferences();
        });

        const animationsToggle = panel.querySelector('#animationsToggle');
        animationsToggle.checked = this.preferences.animations;
        animationsToggle.addEventListener('change', (e) => {
            this.preferences.animations = e.target.checked;
            this.savePreferences();
            this.applyPreferences();
        });

        const highContrastToggle = panel.querySelector('#highContrastToggle');
        highContrastToggle.checked = this.preferences.highContrast;
        highContrastToggle.addEventListener('change', (e) => {
            this.preferences.highContrast = e.target.checked;
            this.savePreferences();
            this.applyPreferences();
        });
    }
}

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', () => {
    new PerformanceMonitor();
    new AccessibilityEnhancer();
    new ErrorHandler();
    new UserPreferences();
});

// Export for use in other scripts
window.EnhancedFeatures = {
    PerformanceMonitor,
    AccessibilityEnhancer,
    ErrorHandler,
    UserPreferences
};
