<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل الصور إلى رمادي - Image to Grayscale</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .image-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-box {
            text-align: center;
        }
        .image-box img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            display: none;
        }
        .image-box h3 {
            margin: 10px 0;
            color: var(--primary-color);
        }
        .drag-area {
            border: 2px dashed var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .drag-area:hover {
            border-color: var(--primary-color);
        }
        .drag-area i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        #fileInput {
            display: none;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
        }
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        input[type="range"] {
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <header>
        <h1>تحويل الصور إلى رمادي <span>Image to Grayscale</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="drag-area" id="dragArea">
            <i class="fas fa-cloud-upload-alt"></i>
            <p class="ar">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
            <p class="en">Drag & drop image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="controls">
            <div class="slider-container">
                <label for="grayscaleLevel" class="ar">مستوى التدرج الرمادي:</label>
                <label for="grayscaleLevel" class="en">Grayscale Level:</label>
                <input type="range" id="grayscaleLevel" min="0" max="100" value="100">
                <span id="levelValue">100%</span>
            </div>
        </div>

        <div class="image-container">
            <div class="image-box">
                <h3 class="ar">الصورة الأصلية</h3>
                <h3 class="en">Original Image</h3>
                <img id="originalImage" alt="Original">
            </div>
            <div class="image-box">
                <h3 class="ar">الصورة الرمادية</h3>
                <h3 class="en">Grayscale Image</h3>
                <img id="grayscaleImage" alt="Grayscale">
            </div>
        </div>

        <div class="button-group">
            <button id="downloadBtn" class="ar" disabled>تحميل الصورة الرمادية</button>
            <button id="downloadBtn" class="en" disabled>Download Grayscale Image</button>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const originalImage = document.getElementById('originalImage');
            const grayscaleImage = document.getElementById('grayscaleImage');
            const grayscaleLevel = document.getElementById('grayscaleLevel');
            const levelValue = document.getElementById('levelValue');
            const downloadBtn = document.getElementById('downloadBtn');

            let originalImageData = null;

            // Handle file selection
            function handleFile(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = () => {
                            originalImage.src = img.src;
                            originalImage.style.display = 'block';
                            grayscaleImage.style.display = 'block';
                            originalImageData = img;
                            applyGrayscale();
                            downloadBtn.disabled = false;
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }

            // Apply grayscale effect
            function applyGrayscale() {
                if (!originalImageData) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = originalImageData.width;
                canvas.height = originalImageData.height;

                // Draw original image
                ctx.drawImage(originalImageData, 0, 0);

                // Get image data
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;
                const level = grayscaleLevel.value / 100;

                // Convert to grayscale
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    // Calculate grayscale value using luminance formula
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;

                    // Blend between original and grayscale based on level
                    data[i] = r * (1 - level) + gray * level;     // Red
                    data[i + 1] = g * (1 - level) + gray * level; // Green
                    data[i + 2] = b * (1 - level) + gray * level; // Blue
                }

                // Put the modified image data back
                ctx.putImageData(imageData, 0, 0);

                // Update preview
                grayscaleImage.src = canvas.toDataURL('image/png');
            }

            // File input change
            fileInput.addEventListener('change', (e) => {
                handleFile(e.target.files[0]);
            });

            // Drag and drop
            dragArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--primary-color)';
            });

            dragArea.addEventListener('dragleave', () => {
                dragArea.style.borderColor = 'var(--secondary-color)';
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.style.borderColor = 'var(--secondary-color)';
                handleFile(e.dataTransfer.files[0]);
            });

            dragArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Grayscale level change
            grayscaleLevel.addEventListener('input', () => {
                levelValue.textContent = `${grayscaleLevel.value}%`;
                applyGrayscale();
            });

            // Download grayscale image
            downloadBtn.addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = 'grayscale-image.png';
                link.href = grayscaleImage.src;
                link.click();
            });
        });
    </script>
</body>
</html>