<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سرعة الكتابة - Typing Speed Test</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .typing-test {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .text-display {
            background-color: var(--secondary-bg);
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 1.2em;
            line-height: 1.6;
            text-align: left;
            direction: ltr;
        }
        .text-display span {
            position: relative;
        }
        .text-display span.correct {
            color: #28a745;
        }
        .text-display span.incorrect {
            color: #dc3545;
            text-decoration: underline;
        }
        .text-display span.current {
            background-color: var(--primary-color);
            color: white;
        }
        .input-area {
            margin-bottom: 20px;
        }
        .input-area textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 1.1em;
            resize: none;
            text-align: left;
            direction: ltr;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-box {
            background-color: var(--secondary-bg);
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            color: var(--primary-color);
            margin: 5px 0;
        }
        .timer {
            font-size: 2em;
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        .language-toggle {
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <header>
        <h1>اختبار سرعة الكتابة <span>Typing Speed Test</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="typing-test">
            <div class="language-toggle">
                <button onclick="switchLanguage('en')" class="primary-button">English</button>
                <button onclick="switchLanguage('ar')" class="primary-button">العربية</button>
            </div>

            <div class="timer" id="timer">1:00</div>

            <div class="text-display" id="textDisplay"></div>

            <div class="input-area">
                <textarea 
                    id="textInput" 
                    rows="3" 
                    placeholder="Start typing here..."
                    oninput="handleInput()"
                    disabled
                ></textarea>
            </div>

            <div class="controls">
                <button onclick="startTest()" class="primary-button" id="startButton">
                    <i class="fas fa-play"></i>
                    <span class="ar">ابدأ</span>
                    <span class="en">Start</span>
                </button>
                <button onclick="resetTest()" class="primary-button">
                    <i class="fas fa-redo"></i>
                    <span class="ar">إعادة</span>
                    <span class="en">Reset</span>
                </button>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <div>السرعة - Speed</div>
                    <div class="stat-value" id="wpm">0</div>
                    <div>كلمة/دقيقة - WPM</div>
                </div>
                <div class="stat-box">
                    <div>الدقة - Accuracy</div>
                    <div class="stat-value" id="accuracy">0%</div>
                </div>
                <div class="stat-box">
                    <div>الأخطاء - Errors</div>
                    <div class="stat-value" id="errors">0</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const sampleTexts = {
            en: [
                "The quick brown fox jumps over the lazy dog. This pangram contains every letter of the English alphabet at least once.",
                "Programming is the art of telling another human what one wants the computer to do.",
                "Success is not final, failure is not fatal: it is the courage to continue that counts."
            ],
            ar: [
                "كان يا ما كان في قديم الزمان قصة جميلة عن الصداقة والوفاء والإخلاص.",
                "العلم نور والجهل ظلام، فاطلب العلم من المهد إلى اللحد.",
                "من جد وجد ومن زرع حصد، ومن سار على الدرب وصل."
            ]
        };

        let currentText = '';
        let currentLanguage = 'en';
        let timeLeft = 60;
        let timer = null;
        let errors = 0;
        let totalTyped = 0;
        let isTestActive = false;

        function switchLanguage(lang) {
            currentLanguage = lang;
            resetTest();
            document.getElementById('textInput').style.direction = lang === 'ar' ? 'rtl' : 'ltr';
            document.getElementById('textDisplay').style.direction = lang === 'ar' ? 'rtl' : 'ltr';
        }

        function getRandomText() {
            const texts = sampleTexts[currentLanguage];
            return texts[Math.floor(Math.random() * texts.length)];
        }

        function startTest() {
            if (isTestActive) return;
            
            isTestActive = true;
            currentText = getRandomText();
            errors = 0;
            totalTyped = 0;
            timeLeft = 60;
            
            const textDisplay = document.getElementById('textDisplay');
            textDisplay.innerHTML = currentText.split('').map(char => 
                `<span>${char}</span>`
            ).join('');
            
            document.getElementById('textInput').value = '';
            document.getElementById('textInput').disabled = false;
            document.getElementById('textInput').focus();
            
            timer = setInterval(updateTimer, 1000);
            updateStats();
        }

        function updateTimer() {
            timeLeft--;
            document.getElementById('timer').textContent = `${Math.floor(timeLeft / 60)}:${(timeLeft % 60).toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                endTest();
            }
        }

        function handleInput() {
            if (!isTestActive) return;

            const textInput = document.getElementById('textInput');
            const textDisplay = document.getElementById('textDisplay');
            const spans = textDisplay.getElementsByTagName('span');
            const typed = textInput.value;
            totalTyped = typed.length;

            errors = 0;
            for (let i = 0; i < spans.length; i++) {
                const char = spans[i];
                if (i < typed.length) {
                    if (char.textContent === typed[i]) {
                        char.className = 'correct';
                    } else {
                        char.className = 'incorrect';
                        errors++;
                    }
                } else if (i === typed.length) {
                    char.className = 'current';
                } else {
                    char.className = '';
                }
            }

            updateStats();

            if (typed.length === currentText.length) {
                endTest();
            }
        }

        function updateStats() {
            const timePassed = 60 - timeLeft;
            const wpm = Math.round(totalTyped / 5 / (timePassed / 60)) || 0;
            const accuracy = totalTyped ? Math.round(((totalTyped - errors) / totalTyped) * 100) : 0;

            document.getElementById('wpm').textContent = wpm;
            document.getElementById('accuracy').textContent = `${accuracy}%`;
            document.getElementById('errors').textContent = errors;
        }

        function endTest() {
            clearInterval(timer);
            isTestActive = false;
            document.getElementById('textInput').disabled = true;
        }

        function resetTest() {
            clearInterval(timer);
            isTestActive = false;
            document.getElementById('textDisplay').innerHTML = '';
            document.getElementById('textInput').value = '';
            document.getElementById('textInput').disabled = true;
            document.getElementById('timer').textContent = '1:00';
            document.getElementById('wpm').textContent = '0';
            document.getElementById('accuracy').textContent = '0%';
            document.getElementById('errors').textContent = '0';
            timeLeft = 60;
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>