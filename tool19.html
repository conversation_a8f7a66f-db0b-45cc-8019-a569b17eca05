<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دمج النصوص - Text Merger</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-inputs {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        .input-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .input-row textarea {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 60px;
            resize: vertical;
        }
        .input-row button {
            padding: 10px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
        }
        .separator-options {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
        .separator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .separator-option {
            padding: 10px;
            background-color: var(--bg-color);
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s;
        }
        .separator-option:hover,
        .separator-option.active {
            background-color: var(--primary-color);
            color: white;
        }
        .custom-separator {
            margin-top: 10px;
            padding: 5px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .format-options {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }
        .format-option {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .result-container {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
        }
        .result-text {
            width: 100%;
            min-height: 150px;
            padding: 10px;
            border: 1px solid var(--secondary-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            color: var(--text-color);
            resize: vertical;
            margin-bottom: 10px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>دمج النصوص <span>Text Merger</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main class="tool-container">
        <div class="text-inputs" id="textInputs">
            <div class="input-row">
                <textarea placeholder="Enter text here..."></textarea>
                <button class="remove-btn" title="Remove">×</button>
            </div>
        </div>

        <button class="add-btn" id="addInput">
            <span class="ar">+ إضافة نص جديد</span>
            <span class="en">+ Add New Text</span>
        </button>

        <div class="separator-options">
            <div class="ar">الفاصل بين النصوص:</div>
            <div class="en">Separator between texts:</div>
            
            <div class="separator-grid">
                <div class="separator-option active" data-separator=" ">مسافة / Space</div>
                <div class="separator-option" data-separator=",">فاصلة / Comma</div>
                <div class="separator-option" data-separator="\n">سطر جديد / New Line</div>
                <div class="separator-option" data-separator="|">شريط رأسي / Vertical Bar</div>
                <div class="separator-option" data-separator=";">فاصلة منقوطة / Semicolon</div>
                <div class="separator-option" data-separator="custom">مخصص / Custom</div>
            </div>

            <input type="text" id="customSeparator" class="custom-separator" style="display: none" placeholder="Custom separator">
        </div>

        <div class="format-options">
            <div class="format-option">
                <input type="checkbox" id="trimSpaces">
                <label for="trimSpaces" class="ar">إزالة المسافات الزائدة</label>
                <label for="trimSpaces" class="en">Trim Extra Spaces</label>
            </div>
            <div class="format-option">
                <input type="checkbox" id="removeEmpty">
                <label for="removeEmpty" class="ar">إزالة السطور الفارغة</label>
                <label for="removeEmpty" class="en">Remove Empty Lines</label>
            </div>
            <div class="format-option">
                <input type="checkbox" id="removeDuplicates">
                <label for="removeDuplicates" class="ar">إزالة التكرار</label>
                <label for="removeDuplicates" class="en">Remove Duplicates</label>
            </div>
        </div>

        <div class="button-group">
            <button id="mergeBtn" class="ar">دمج النصوص</button>
            <button id="mergeBtn" class="en">Merge Texts</button>
            
            <button id="copyBtn" class="ar" disabled>نسخ النتيجة</button>
            <button id="copyBtn" class="en" disabled>Copy Result</button>
        </div>

        <div class="result-container" style="display: none;">
            <textarea id="resultText" class="result-text" readonly></textarea>
            <div class="stats">
                <span id="charCount">0 أحرف / chars</span>
                <span id="wordCount">0 كلمات / words</span>
                <span id="lineCount">0 سطور / lines</span>
            </div>
        </div>
    </main>

    <footer>
        <a href="index.html" class="ar">← العودة إلى القائمة الرئيسية</a>
        <a href="index.html" class="en">← Back to Main Menu</a>
    </footer>

    <script src="scripts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const textInputs = document.getElementById('textInputs');
            const addInput = document.getElementById('addInput');
            const separatorOptions = document.querySelectorAll('.separator-option');
            const customSeparator = document.getElementById('customSeparator');
            const mergeBtn = document.getElementById('mergeBtn');
            const copyBtn = document.getElementById('copyBtn');
            const resultContainer = document.querySelector('.result-container');
            const resultText = document.getElementById('resultText');

            // Add new input field
            function addInputField() {
                const inputRow = document.createElement('div');
                inputRow.className = 'input-row';
                inputRow.innerHTML = `
                    <textarea placeholder="Enter text here..."></textarea>
                    <button class="remove-btn" title="Remove">×</button>
                `;
                textInputs.appendChild(inputRow);

                // Remove button event
                inputRow.querySelector('.remove-btn').addEventListener('click', () => {
                    if (textInputs.children.length > 1) {
                        inputRow.remove();
                    }
                });

                return inputRow.querySelector('textarea');
            }

            // Load saved inputs
            const savedInputs = JSON.parse(localStorage.getItem('textMerger_inputs') || '[""]');
            textInputs.innerHTML = '';
            savedInputs.forEach(text => {
                const textarea = addInputField();
                textarea.value = text;
            });

            // Add input button
            addInput.addEventListener('click', () => {
                addInputField();
            });

            // Separator options
            let currentSeparator = ' ';
            separatorOptions.forEach(option => {
                option.addEventListener('click', () => {
                    separatorOptions.forEach(opt => opt.classList.remove('active'));
                    option.classList.add('active');
                    currentSeparator = option.dataset.separator;
                    customSeparator.style.display = currentSeparator === 'custom' ? 'block' : 'none';
                });
            });

            // Merge texts
            function mergeTexts() {
                const inputs = Array.from(textInputs.querySelectorAll('textarea')).map(input => input.value);
                let separator = currentSeparator === 'custom' ? customSeparator.value : currentSeparator;
                if (currentSeparator === '\n') separator = '\n';

                let result = inputs;

                if (document.getElementById('trimSpaces').checked) {
                    result = result.map(text => text.trim());
                }

                if (document.getElementById('removeEmpty').checked) {
                    result = result.filter(text => text.length > 0);
                }

                if (document.getElementById('removeDuplicates').checked) {
                    result = [...new Set(result)];
                }

                const merged = result.join(separator);
                resultText.value = merged;
                resultContainer.style.display = 'block';
                copyBtn.disabled = false;

                // Update stats
                document.getElementById('charCount').textContent = 
                    `${merged.length} أحرف / chars`;
                document.getElementById('wordCount').textContent = 
                    `${merged.trim().split(/\s+/).filter(Boolean).length} كلمات / words`;
                document.getElementById('lineCount').textContent = 
                    `${merged.split('\n').filter(Boolean).length} سطور / lines`;

                // Save inputs
                localStorage.setItem('textMerger_inputs', JSON.stringify(inputs));
            }

            // Event listeners
            mergeBtn.addEventListener('click', mergeTexts);
            
            copyBtn.addEventListener('click', () => {
                window.utils.copyToClipboard(resultText.value);
            });
        });
    </script>
</body>
</html>