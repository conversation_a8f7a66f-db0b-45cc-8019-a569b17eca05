<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Generator</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container {
            margin-top: 50px;
        }
        textarea {
            resize: none;
        }
        .output-links {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">URL Generator</h1>
        <form id="urlForm">
            <div class="mb-3">
                <label for="baseUrl" class="form-label">Base URL:</label>
                <input type="url" id="baseUrl" class="form-control" placeholder="https://example.com" required>
            </div>
            <div class="mb-3">
                <label for="keyword" class="form-label">Keyword to Replace:</label>
                <input type="text" id="keyword" class="form-control" placeholder="Enter the keyword to replace" required>
            </div>
            <div class="mb-3">
                <label for="replacementWords" class="form-label">Replacement Words (one per line):</label>
                <textarea id="replacementWords" class="form-control" rows="5" placeholder="word1\nword2\nword3" required></textarea>
            </div>
            <button type="button" class="btn btn-primary w-100" id="generateLinks">Generate Links</button>
        </form>
        <hr>
        <div id="outputSection" class="mt-4">
            <h5>Generated Links:</h5>
            <div class="output-links border p-3 rounded bg-white" id="outputLinks"></div>
            <div class="mt-3 d-flex justify-content-between">
                <button class="btn btn-success" id="downloadText">Download as Text</button>
                <button class="btn btn-warning" id="downloadHtml">Download as HTML</button>
                <button class="btn btn-info" id="copyLinks">Copy to Clipboard</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JavaScript -->
    <script>
        document.getElementById("generateLinks").addEventListener("click", () => {
            const baseUrl = document.getElementById("baseUrl").value;
            const keyword = document.getElementById("keyword").value;
            const replacementWords = document.getElementById("replacementWords").value.split("\n").map(word => word.trim()).filter(word => word !== "");
            const outputLinks = document.getElementById("outputLinks");
            outputLinks.innerHTML = "";

            if (!baseUrl.includes(keyword)) {
                alert("The base URL must contain the keyword to replace.");
                return;
            }

            let links = [];
            replacementWords.forEach(word => {
                const newUrl = baseUrl.replace(keyword, word);
                links.push(newUrl);
                const linkElement = `<a href="${newUrl}" target="_blank" class="d-block">${newUrl}</a>`;
                outputLinks.innerHTML += linkElement;
            });

            // Store links globally for downloads
            window.generatedLinks = links;
        });

        document.getElementById("downloadText").addEventListener("click", () => {
            const links = window.generatedLinks || [];
            const blob = new Blob([links.join("\n")], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "links.txt";
            a.click();
            URL.revokeObjectURL(url);
        });

        document.getElementById("downloadHtml").addEventListener("click", () => {
            const links = window.generatedLinks || [];
            const htmlContent = links.map(link => `<a href="${link}" target="_blank">${link}</a>`).join("<br>");
            const blob = new Blob([htmlContent], { type: "text/html" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "links.html";
            a.click();
            URL.revokeObjectURL(url);
        });

        document.getElementById("copyLinks").addEventListener("click", () => {
            const links = window.generatedLinks || [];
            navigator.clipboard.writeText(links.join("\n")).then(() => {
                alert("Links copied to clipboard!");
            });
        });
    </script>
</body>
</html>
