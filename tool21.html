<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة الميزانية - Budget Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .budget-form {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-color);
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }
        .transactions {
            margin-top: 20px;
        }
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background-color: var(--bg-color);
            border-radius: 4px;
        }
        .delete-btn {
            color: #ff4444;
            cursor: pointer;
        }
        .summary {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <header>
        <h1>حاسبة الميزانية <span>Budget Calculator</span></h1>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
        <div class="lang-toggle">
            <button id="toggleLang">English/العربية</button>
        </div>
    </header>

    <main>
        <div class="budget-form">
            <div class="input-group">
                <label>الوصف (Description)</label>
                <input type="text" id="description" placeholder="اكتب وصفاً للمعاملة">
            </div>
            <div class="input-group">
                <label>المبلغ (Amount)</label>
                <input type="number" id="amount" placeholder="أدخل المبلغ">
            </div>
            <div class="input-group">
                <label>النوع (Type)</label>
                <select id="type" style="width: 100%; padding: 8px;">
                    <option value="income">دخل (Income)</option>
                    <option value="expense">مصروف (Expense)</option>
                </select>
            </div>
            <button onclick="addTransaction()" class="primary-button">إضافة (Add)</button>

            <div class="transactions" id="transactionsList">
                <!-- Transactions will be added here dynamically -->
            </div>

            <div class="summary">
                <h3>الملخص (Summary)</h3>
                <p>الدخل الإجمالي (Total Income): <span id="totalIncome">0</span></p>
                <p>المصروفات الإجمالية (Total Expenses): <span id="totalExpenses">0</span></p>
                <p>الرصيد (Balance): <span id="balance">0</span></p>
            </div>
        </div>
    </main>

    <script>
        let transactions = [];

        function addTransaction() {
            const description = document.getElementById('description').value;
            const amount = parseFloat(document.getElementById('amount').value);
            const type = document.getElementById('type').value;

            if (!description || isNaN(amount)) {
                alert('الرجاء ملء جميع الحقول (Please fill all fields)');
                return;
            }

            transactions.push({
                description,
                amount,
                type
            });

            updateTransactionsList();
            updateSummary();
            clearForm();
        }

        function deleteTransaction(index) {
            transactions.splice(index, 1);
            updateTransactionsList();
            updateSummary();
        }

        function updateTransactionsList() {
            const list = document.getElementById('transactionsList');
            list.innerHTML = '';

            transactions.forEach((transaction, index) => {
                const item = document.createElement('div');
                item.className = 'transaction-item';
                item.innerHTML = `
                    <span>${transaction.description}</span>
                    <span>${transaction.type === 'income' ? '+' : '-'}${transaction.amount}</span>
                    <i class="fas fa-trash delete-btn" onclick="deleteTransaction(${index})"></i>
                `;
                list.appendChild(item);
            });
        }

        function updateSummary() {
            const income = transactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.amount, 0);

            const expenses = transactions
                .filter(t => t.type === 'expense')
                .reduce((sum, t) => sum + t.amount, 0);

            const balance = income - expenses;

            document.getElementById('totalIncome').textContent = income.toFixed(2);
            document.getElementById('totalExpenses').textContent = expenses.toFixed(2);
            document.getElementById('balance').textContent = balance.toFixed(2);
        }

        function clearForm() {
            document.getElementById('description').value = '';
            document.getElementById('amount').value = '';
        }

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
        });

        // Language toggle
        const toggleLang = document.getElementById('toggleLang');
        toggleLang.addEventListener('click', () => {
            document.documentElement.dir = document.documentElement.dir === 'rtl' ? 'ltr' : 'rtl';
            document.documentElement.lang = document.documentElement.lang === 'ar' ? 'en' : 'ar';
        });
    </script>
</body>
</html>