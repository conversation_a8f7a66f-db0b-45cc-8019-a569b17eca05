<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مترجم الرموز التعبيرية - Emoji Translator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .translator {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .mode-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .mode-toggle button {
            background: none;
            border: 2px solid var(--primary-color);
            padding: 10px 20px;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s;
        }
        .mode-toggle button:first-child {
            border-radius: 20px 0 0 20px;
        }
        .mode-toggle button:last-child {
            border-radius: 0 20px 20px 0;
        }
        .mode-toggle button.active {
            background-color: var(--primary-color);
            color: white;
        }
        .input-area, .output-area {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            resize: vertical;
            direction: ltr;
            font-size: 16px;
        }
        .output-area {
            background-color: var(--secondary-bg);
            padding: 15px;
            border-radius: 5px;
            min-height: 150px;
            word-wrap: break-word;
            direction: ltr;
            font-size: 16px;
        }
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            background-color: var(--secondary-bg);
            border-radius: 5px;
        }
        .emoji-item {
            padding: 10px;
            text-align: center;
            background-color: var(--card-bg);
            border-radius: 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .emoji-item:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <header>
        <h1>مترجم الرموز التعبيرية <span>Emoji Translator</span></h1>
        <nav>
            <a href="index.html" class="back-button">
                <i class="fas fa-home"></i>
                <span class="ar">الرئيسية</span>
                <span class="en">Home</span>
            </a>
        </nav>
        <div class="theme-toggle">
            <i class="fas fa-moon"></i>
            <label class="switch">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider round"></span>
            </label>
            <i class="fas fa-sun"></i>
        </div>
    </header>

    <main>
        <div class="translator">
            <div class="mode-toggle">
                <button onclick="setMode('text-to-emoji')" class="active" id="textToEmojiBtn">
                    <span class="ar">نص إلى رموز</span>
                    <span class="en">Text to Emoji</span>
                </button>
                <button onclick="setMode('emoji-to-text')" id="emojiToTextBtn">
                    <span class="ar">رموز إلى نص</span>
                    <span class="en">Emoji to Text</span>
                </button>
            </div>

            <div class="input-area">
                <textarea id="inputText" placeholder="Write your text here..." oninput="translate()"></textarea>
            </div>

            <button onclick="translate()" class="primary-button">
                <i class="fas fa-sync-alt"></i>
                <span class="ar">ترجم</span>
                <span class="en">Translate</span>
            </button>

            <div class="output-area" id="outputText"></div>

            <div class="emoji-grid" id="emojiGrid"></div>
        </div>
    </main>

    <script>
        const emojiDictionary = {
            // Common words and emotions
            'happy': '😊',
            'sad': '😢',
            'love': '❤️',
            'heart': '❤️',
            'laugh': '😂',
            'angry': '😠',
            'smile': '😊',
            'cry': '😢',
            // Nature
            'sun': '☀️',
            'moon': '🌙',
            'star': '⭐',
            'flower': '🌸',
            'tree': '🌳',
            // Objects
            'book': '📚',
            'phone': '📱',
            'computer': '💻',
            'car': '🚗',
            'house': '🏠',
            // Food
            'pizza': '🍕',
            'apple': '🍎',
            'banana': '🍌',
            'coffee': '☕',
            // Animals
            'cat': '🐱',
            'dog': '🐶',
            'bird': '🐦',
            'fish': '🐠',
            // People
            'person': '👤',
            'baby': '👶',
            'girl': '👧',
            'boy': '👦',
            'man': '👨',
            'woman': '👩',
            // Gestures
            'ok': '👍',
            'no': '👎',
            'wave': '👋',
            'clap': '👏',
            // Time
            'time': '⏰',
            'clock': '🕐',
            // Weather
            'rain': '🌧️',
            'snow': '❄️',
            'cloud': '☁️',
            // Sports
            'football': '⚽',
            'basketball': '🏀',
            'tennis': '🎾'
        };

        let currentMode = 'text-to-emoji';

        function setMode(mode) {
            currentMode = mode;
            document.getElementById('textToEmojiBtn').classList.toggle('active', mode === 'text-to-emoji');
            document.getElementById('emojiToTextBtn').classList.toggle('active', mode === 'emoji-to-text');
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').innerHTML = '';
            translate();
        }

        function translate() {
            const input = document.getElementById('inputText').value;
            const output = document.getElementById('outputText');

            if (currentMode === 'text-to-emoji') {
                const words = input.toLowerCase().split(/\s+/);
                const translated = words.map(word => emojiDictionary[word] || word).join(' ');
                output.innerHTML = translated;
            } else {
                let translated = input;
                for (const [word, emoji] of Object.entries(emojiDictionary)) {
                    translated = translated.replace(new RegExp(emoji, 'g'), word);
                }
                output.innerHTML = translated;
            }
        }

        // Populate emoji grid
        const emojiGrid = document.getElementById('emojiGrid');
        Object.entries(emojiDictionary).forEach(([word, emoji]) => {
            const emojiItem = document.createElement('div');
            emojiItem.className = 'emoji-item';
            emojiItem.innerHTML = `${emoji}<br><small>${word}</small>`;
            emojiItem.onclick = () => {
                const input = document.getElementById('inputText');
                input.value += emoji;
                translate();
            };
            emojiGrid.appendChild(emojiItem);
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('change', () => {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    </script>
</body>
</html>